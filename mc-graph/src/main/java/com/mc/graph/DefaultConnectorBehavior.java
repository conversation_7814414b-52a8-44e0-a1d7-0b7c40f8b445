package com.mc.graph;

import com.mc.graph.interfaces.ConnectorBehavior;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.GraphController;
import com.mc.graph.interfaces.NewLinkSkin;
import com.mc.graph.interfaces.SkinFactory;
import javafx.event.EventHandler;
import javafx.scene.Cursor;
import javafx.scene.input.MouseEvent;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DefaultConnectorBehavior implements ConnectorBehavior {
  private ConnectorSkin skin;

  private MouseEvent newConnectionPressEvent;
  private NewLinkSkin linkSkin;
  
  private EventHandler<MouseEvent> movedEvent;
  private EventHandler<MouseEvent> pressedEvent;
  private EventHandler<MouseEvent> draggedEvent;
  private EventHandler<MouseEvent> releasedEvent;

  /**
   * Constructor.
   * @param controller graph controller
   * @param skin connector skin
   */
  public DefaultConnectorBehavior(GraphController controller, ConnectorSkin skin) {
    this.skin = skin;
    movedEvent = (event) -> {
      skin.getNode().setCursor(Cursor.DEFAULT);
      event.consume();
    };
    pressedEvent = (event) -> {
      if (!controller.isCellLinkable() || !skin.linkableProperty().get()) {
        return;
      }
      newConnectionPressEvent = event;
      event.consume();
    };
    draggedEvent = (event) -> {
      if (!controller.isCellLinkable() || !skin.linkableProperty().get()) {
        return;
      }
      if (linkSkin == null) {
        linkSkin = controller.getSkinFactory().createNewLinkSkin(SkinFactory.DEFAULT_LINK,
            skin.getConnector(), controller.getGraphCanvas().getContainer(), 
            controller.getGraphCanvas().getContainer(), controller.getSkinManager());
        controller.createNewLinkBehavior(linkSkin);
        if (newConnectionPressEvent != null) {
          MouseEvent.fireEvent(linkSkin.getReceiverUi(), newConnectionPressEvent);
        }
      }

      event.consume();
      MouseEvent.fireEvent(linkSkin.getReceiverUi(), event);

      event.consume();
      MouseEvent.fireEvent(linkSkin.getReceiverUi(), event);
      event.consume();
    };
    
    releasedEvent = (event) -> {
      if (!controller.isCellLinkable() || !skin.linkableProperty().get()) {
        return;
      }
      event.consume();
      try {
        if (linkSkin != null) {
          MouseEvent.fireEvent(linkSkin.getReceiverUi(), event);
        }
      } catch (Exception exp) {
        log.info("Link skin has been removed.");
        
      }
      newConnectionPressEvent = null;
      linkSkin = null;
    };
  }

  @Override
  public void attach() {
    skin.getNode().addEventHandler(MouseEvent.MOUSE_MOVED, movedEvent);
    skin.getNode().addEventHandler(MouseEvent.MOUSE_PRESSED, pressedEvent);
    skin.getNode().addEventHandler(MouseEvent.MOUSE_DRAGGED, draggedEvent);
    skin.getNode().addEventHandler(MouseEvent.MOUSE_RELEASED, releasedEvent);
  }

  @Override
  public void detach() {
    skin.getNode().removeEventHandler(MouseEvent.MOUSE_MOVED, movedEvent);
    skin.getNode().removeEventHandler(MouseEvent.MOUSE_PRESSED, pressedEvent);
    skin.getNode().removeEventHandler(MouseEvent.MOUSE_DRAGGED, draggedEvent);
    skin.getNode().removeEventHandler(MouseEvent.MOUSE_RELEASED, releasedEvent);
  }
}
