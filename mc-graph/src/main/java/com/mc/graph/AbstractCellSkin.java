package com.mc.graph;

import com.mc.common.util.WeakAdapter;
import com.mc.graph.interfaces.CellBehavior;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.SkinManager;
import com.mc.graph.util.NodeUtil;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.value.ChangeListener;
import javafx.collections.ListChangeListener;
import javafx.scene.Parent;
import javafx.scene.layout.BorderStroke;

import java.util.ArrayList;
import java.util.List;

/**
 * Abstract cell skin.
 */
public abstract class AbstractCellSkin implements CellSkin {
  protected CellObject cellObject;
  protected List<CellBehavior> cellBehaviors = new ArrayList<>();
  protected Parent parent;
  protected Parent container;
  protected SkinManager skinManager;

  private ListChangeListener<Connector> listener;
  protected ChangeListener<Number> boundChangeListener;

  private SimpleBooleanProperty selectedProperty = new SimpleBooleanProperty();
  private SimpleBooleanProperty rotatableProperty = new SimpleBooleanProperty(true);
  private SimpleBooleanProperty movableProperty = new SimpleBooleanProperty(true);

  protected WeakAdapter weakAdapter = new WeakAdapter();

  /**
   * Constructor.
   *
   * @param cellobject  cell object to be skin
   * @param parent      parent to add the cell skin
   * @param container   TODO
   * @param skinManager skin manager
   */
  public AbstractCellSkin(CellObject cellobject, Parent parent, Parent container,
                          SkinManager skinManager) {
    this.cellObject = cellobject;
    this.parent = parent;
    this.skinManager = skinManager;
    this.container = container;

    listener = weakAdapter.wrap((ListChangeListener<Connector>) (change) -> onConnectorChange());

    boundChangeListener = weakAdapter.wrap((observable, oldValue, newValue) -> {
      layoutConnectors();
    });

    initInner();
    initRegion();

    this.getRegion().prefWidthProperty().addListener(boundChangeListener);
    this.getRegion().prefHeightProperty().addListener(boundChangeListener);

    setCell(cellobject);
  }

  @Override
  public void destroy() {
    if (getRegion() != null) {
      this.getRegion().prefWidthProperty().removeListener(boundChangeListener);
      this.getRegion().prefHeightProperty().removeListener(boundChangeListener);
    }
    if (getCell() != null) {
      getCell().getConnectors().removeListener(listener);
    }

    for (CellBehavior cellBehavior : cellBehaviors) {
      cellBehavior.detach();
    }
    cellBehaviors.clear();

    selectedProperty.unbind();
    rotatableProperty.unbind();
    movableProperty.unbind();

    parent = null;
    container = null;
    cellObject = null;
  }

  protected abstract void initInner();

  protected abstract void initRegion();

  protected abstract void onConnectorChange();

  protected abstract void layoutConnectors();

  @Override
  public boolean requestSelection(boolean select) {
    selectedProperty.set(select);
    return true;
  }

  @Override
  public boolean isSelected() {
    return selectedProperty.get();
  }

  @Override
  public BooleanProperty selectedProperty() {
    return selectedProperty;
  }

  @Override
  public Parent getParent() {
    return parent;
  }

  @Override
  public void setCell(CellObject cell) {
    if (this.cellObject != null) {
      this.cellObject.getConnectors().removeListener(listener);
    }
    this.cellObject = cell;
    this.cellObject.getConnectors().addListener(listener);

    if (getRegion() != null) {
      getRegion().visibleProperty().bind(cell.visibleProperty());
    }
    onConnectorChange();
  }

  @Override
  public CellObject getCell() {
    return cellObject;
  }

  @Override
  public void addCellBehavior(CellBehavior cellBehavior) {
    cellBehavior.attach();
    cellBehaviors.add(cellBehavior);
    for (ConnectorSkin skin : getConnectorSkins()) {
      cellBehavior.createConnectorBehavior(skin);
    }
  }

  @Override
  public double getResizableBorderWidth() {
    return 10;
  }

  @Override
  public void add() {
    NodeUtil.addToParent(parent, getRegion());
    for (ConnectorSkin skin : getConnectorSkins()) {
      skin.add();
    }
  }

  @Override
  public void remove() {
    NodeUtil.removeFromParent(getRegion());
    for (ConnectorSkin skin : getConnectorSkins()) {
      skin.remove();
    }
  }

  @Override
  public boolean isMovable() {
    return movableProperty.get();
  }

  @Override
  public BooleanProperty movableProperty() {
    return movableProperty;
  }

  @Override
  public BorderStroke getSelectedBorderStroke() {
    return null;
  }

  @Override
  public boolean isRotatable() {
    return rotatableProperty.get();
  }

  @Override
  public BooleanProperty rotatableProperty() {
    return rotatableProperty;
  }
}
