package com.mc.graph.interfaces;

import com.google.common.eventbus.EventBus;

import com.mc.graph.util.GraphSelectionModel;
import javafx.scene.image.Image;
import javafx.scene.input.MouseEvent;

public interface GraphController {
  void createCellBehavior(CellSkin cellSkin);
  
  void createConnectorBehavior(ConnectorSkin connectorSkin);
  
  LinkBehavior createNewLinkBehavior(NewLinkSkin linkSkin);
  
  SkinFactory getSkinFactory();
  
  SkinManager getSkinManager();
  
  LinkObject connect(Connector first, Connector second, boolean addToGraph);
  
  LinkObject disconnect(Connector first, Connector second);
  
  GraphModel getGraphModel();
  
  GraphCanvas getGraphCanvas();
  
  GraphSelectionModel getSelectionModel();
  
  boolean isCellDeletable();
  
  boolean isCellMovable();
  
  boolean isCellResizable();
  
  boolean isCellLinkable();
  
  boolean isCellRotatable();
  
  boolean isCellCloneable();
  
  boolean isCellOrderable();
  
  /**
   * 获取鼠标事件所经过的的cell skin.
   * @param event 鼠标事件
   * @return 经过的cell skin的集合
   */
  CellSkin[] getHittedCellskin(MouseEvent event);
  
  CellObject[] removeCells(CellObject ...cells);
  
  void orderCell(CellObject cell, int index);
  
  void centerCell(CellObject cell);
  
  /**
   * 保留部分cell，删除剩下的.
   * @param cells 需要保留的cell
   * @return 被删除的cell
   */
  CellObject[] retainCells(CellObject ...cells);
  
  /**
   * 保留部分link，删除剩下的.
   * @param links 需要保留的link
   * @return 被删除的links
   */
  LinkObject[] retainLinks(LinkObject ...links);
  
  CellObject[] removeSelectedCells();
  
  EventBus getEventBus();
  
  /**
   * 把canvas内容导出到图片中.
   * @return image
   */
  Image generateImage();
}
