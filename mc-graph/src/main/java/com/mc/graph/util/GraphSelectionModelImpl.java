package com.mc.graph.util;

import com.mc.graph.interfaces.CellSkin;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.shape.Rectangle;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Default clipboard implementation.
 *
 * <AUTHOR>
 */
public class GraphSelectionModelImpl implements GraphSelectionModel {

  private final ObservableList<SelectableNode> items = FXCollections.observableArrayList();
  
  private final ObjectProperty<SelectionMode> selectionMode = 
      new SimpleObjectProperty<>(SelectionMode.NODE);
  
  private final List<SelectionListener> listeners = new ArrayList<>();

  @Override
  public boolean select(SelectableNode node, boolean selected) {
    if (node == null) {
      return false;
    }
    if (node.requestSelection(selected)) {
      if (selected) {
        if (!items.contains(node)) {
          items.add(node);
        }
      } else {
        items.remove(node);
      }

      return true;
    } else {
      return false;
    }
  }

  @Override
  public ObservableList<SelectableNode> getSelectedItems() {
    return items;
  }

  @Override
  public void deselectAll() {

    List<SelectableNode> unselectList = new ArrayList<>();
    unselectList.addAll(items);

    for (SelectableNode selectableNode : unselectList) {
      select(selectableNode, false);
    }
  }

  @Override
  public Collection<CellSkin> getSelectedCellSkin() {
    List<CellSkin> skins = new ArrayList<>();
    for (SelectableNode node : getSelectedItems()) {
      CellSkin skin = NodeUtil.selectedNode2CellSkin(node);
      if (skin != null) {
        skins.add(skin);
      }
    }
    return skins;
  }

  @Override
  public ObjectProperty<GraphSelectionModel.SelectionMode> getSelectionMode() {
    return selectionMode;
  }

  @Override
  public void addSelectionListener(GraphSelectionModel.SelectionListener listener) {
    if (listener != null) {
      listeners.add(listener);
    }
  }

  @Override
  public void removeSelectionListener(GraphSelectionModel.SelectionListener listener) {
    listeners.remove(listener);
  }
  
  /**
   * 触发selection的改变.
   * @param mode 选择模式
   * @param rectangle 选择的框
   */
  public void fireChange(SelectionMode mode, Rectangle rectangle) {
    for (SelectionListener listener : listeners) {
      listener.onFinishSelection(mode, rectangle);
    }
  }
}