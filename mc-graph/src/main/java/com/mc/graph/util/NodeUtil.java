/*
 * Copyright 2012-2016 <PERSON> <info@micha<PERSON><PERSON>er.de>. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification, are permitted
 * provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this list of conditions
 * and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice, this list of
 * conditions and the following disclaimer in the documentation and/or other materials provided with
 * the distribution.
 *
 * Please cite the following publication(s):
 *
 * <PERSON><PERSON>, C.<PERSON>, G.<PERSON>. Visual Reflection Library - A Framework for Declarative GUI
 * Programming on the Java Platform. Computing and Visualization in Science, 2011, in press.
 *
 * THIS SOFTWARE IS PROVIDED BY <PERSON> <<EMAIL>> "AS IS" AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
 * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL <PERSON>
 * <info@micha<PERSON><PERSON>er.de> OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
 * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
 * OF SUCH DAMAGE.
 *
 * The views and conclusions contained in the software and documentation are those of the authors
 * and should not be interpreted as representing official policies, either expressed or implied, of
 * Michael Hoffer <<EMAIL>>.
 */

package com.mc.graph.util;

import com.mc.graph.interfaces.CellSkin;
import com.sun.javafx.collections.ImmutableObservableList;
import javafx.beans.Observable;
import javafx.beans.binding.DoubleBinding;
import javafx.beans.value.ObservableNumberValue;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.Group;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.layout.Pane;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Utility class that provides methods to simplify node handling. Possible use cases are searching
 * for nodes at specific locations, adding/removing nodes to/from parents (Parent interface does not
 * give write access to children).
 *
 * <AUTHOR> Hoffer &lt;<EMAIL>&gt;
 * <AUTHOR> Eugelink &lt;<EMAIL>&gt;
 */
@Slf4j
public class NodeUtil {
  /**
   * 如果Node的UserData为这个，那么计算bgpane时会忽略.
   */
  public static final String NODE_IGNORE = "node.ignore";
  
  /**
   * Node转换成cellskin.
   * @param node 要转换的node
   * @return 如果转换成功，返回cellskin，否则返回null
   */
  public static CellSkin node2CellSkin(Node node) {
    Object object = node.getUserData();
    if (object instanceof CellSkin) {
      return (CellSkin)object;
    } else {
      return null;
    }
  }
  
  /**
   * SelectedNode转换成cellskin.
   * @param node 要转换的selectednode
   * @return 如果转换成功，返回cellskin，否则返回null
   */
  public static CellSkin selectedNode2CellSkin(SelectableNode node) {
    if (node instanceof CellSkin) {
      return (CellSkin)node;
    } else if (node instanceof Node) {
      return node2CellSkin((Node)node);
    } else {
      return null;
    }
  }
  
  // no instantiation allowed
  private NodeUtil() {
    throw new AssertionError(); // not in this class either!
  }

  /**
   * Get the x screen coordinate of the node.
   * 
   * @param node node to be get x screen coordinate
   * @return The X screen coordinate of the node.
   */
  public static double screenX(Node node) {
    return node.localToScene(node.getBoundsInLocal()).getMinX() + node.getScene().getX()
        + node.getScene().getWindow().getX();
  }

  /**
   * Get the y screen coordinate of the node.
   * 
   * @param node the node to be get y screen coordinate
   * @return The Y screen coordinate of the node.
   */
  public static double screenY(Node node) {
    return node.localToScene(node.getBoundsInLocal()).getMinY() + node.getScene().getY()
        + node.getScene().getWindow().getY();
  }

  /**
   * Removes the specified node from its parent.
   *
   * @param node the node to remove
   *
   */
  public static void removeFromParent(Node node) {
    if (node.getParent() instanceof Group) {
      ((Group) node.getParent()).getChildren().remove(node);
    } else if (node.getParent() instanceof Pane) {
      ((Pane) node.getParent()).getChildren().remove(node);
    } else {
      if (node.getParent() == null) {
        log.warn("Parent is null!");
      } else {
        log.warn("Unsupported parent! {}", node.getParent().getClass().getName());
      }
    }
  }

  /**
   * Adds the given node to the specified parent.
   *
   * @param parent parent
   * @param node node
   *
   * @throws IllegalArgumentException if an unsupported parent class has been specified or the
   *         parent is <code>null</code>
   */
  public static void addToParent(Parent parent, Node node) {
    if (parent instanceof Group) {
      ((Group) parent).getChildren().add(node);
    } else if (parent instanceof Pane) {
      ((Pane) parent).getChildren().add(node);
    } else {
      throw new IllegalArgumentException("Unsupported parent: " + parent);
    }
  }

  /**
   * Returns the first node at the given location that is an instance of the specified class object.
   * The search is performed recursively until either a node has been found or a leaf node is
   * reached.
   *
   * @param parent parent node
   * @param sceneX x coordinate
   * @param sceneY y coordinate
   * @param nodeClass node class to search for
   * @return a node that contains the specified screen coordinates and is an instance of the
   *         specified class or {@code null} if no such node exist
   */
  public static Node getNode(Parent parent, double sceneX, double sceneY, Class<?> nodeClass) {

    // dammit! javafx uses "wrong" children order.
    List<Node> rightOrder = new ArrayList<>();
    rightOrder.addAll(parent.getChildrenUnmodifiable());
    Collections.reverse(rightOrder);

    for (Node n : rightOrder) {
      boolean contains = n.contains(n.sceneToLocal(sceneX, sceneY));

      if (contains) {

        if (nodeClass.isAssignableFrom(n.getClass()) || n.getUserData() != null
            && nodeClass.isAssignableFrom(n.getUserData().getClass())) {
          return n;
        }

        if (n instanceof Parent) {
          return getNode((Parent) n, sceneX, sceneY, nodeClass);
        }
      }
    }

    return null;
  }

  /**
   * This method prevents blurry horizontal or vertical lines, use snapXY(x) instead of x.
   *
   * @param position (x or y)
   * @return snapped value
   */
  public static double snapxy(double position) {
    return ((int) position) + .5;
  }

  /**
   * This is the snapXY method for using in a binding, for example: p1.bind( snapXY(
   * p2.multiply(0.1) ));
   *
   * @param position (x or y)
   * @return snapped value
   */
  public static DoubleBinding snapxy(final ObservableNumberValue position) {
    return new DoubleBinding() {
      {
        super.bind(position);
      }

      @Override
      public void dispose() {
        super.unbind(position);
      }

      @Override
      protected double computeValue() {
        return NodeUtil.snapxy(position.doubleValue());
      }

      @Override
      public ObservableList<?> getDependencies() {
        return FXCollections.singletonObservableList(position);
      }
    };
  }

  /**
   * This method prevents blurry horizontal or vertical lines, use snapWH(x, w) instead of w.
   *
   * @param position (x or y)
   * @param offset (width or height)
   * @return snapped value
   */
  public static double snapwh(double position, double offset) {
    return snapxy(position + offset) - snapxy(position);
  }

  /**
   * This is the snapXY method for using in a binding, for example: p1.bind( snapXY(
   * p2.multiply(0.1) ));
   *
   * @param position (x or y)
   * @param offset (width or height)
   * @param dependencies binding dependencies
   * @return snapped value
   */
  public static DoubleBinding snapwh(final ObservableNumberValue position,
      final ObservableNumberValue offset, final Observable... dependencies) {
    return new DoubleBinding() {
      {
        super.bind(dependencies);
      }

      @Override
      public void dispose() {
        super.unbind(dependencies);
      }

      @Override
      protected double computeValue() {
        return NodeUtil.snapwh(position.doubleValue(), offset.doubleValue());
      }

      @Override
      public ObservableList<?> getDependencies() {
        return (dependencies.length == 1) ? FXCollections.singletonObservableList(dependencies[0])
            : new ImmutableObservableList<>(dependencies);
      }
    };
  }
}
