package com.mc.graph;

import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;

import com.mc.common.collections.ObservableListWrapperEx;
import com.mc.graph.event.EditHappenEvent;
import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellPropertyUpdater;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.GraphController;
import com.mc.graph.interfaces.GraphModel;
import com.mc.graph.interfaces.LinkObject;
import com.mc.graph.interfaces.UndoableChange;
import com.mc.graph.interfaces.UndoableEdit;
import com.mc.graph.undo.CellChange;
import com.mc.graph.undo.CellMove;
import com.mc.graph.undo.DefaultUndoableEdit;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.util.Pair;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class McGraphModel implements GraphModel {
  private Set<CellObject> cellSet = new HashSet<>();
  private ObservableListWrapperEx<CellObject> cellList =
      new ObservableListWrapperEx<>(new ArrayList<>());
  private ObservableList<CellObject> readonlyCellList =
      FXCollections.unmodifiableObservableList(cellList);

  private BiMap<Pair<Connector, Connector>, LinkObject> linkMap = HashBiMap.create();
  private ObservableList<LinkObject> links = FXCollections.observableArrayList();
  private ObservableList<LinkObject> readonlyLinks =
      FXCollections.unmodifiableObservableList(links);
  
  private UndoableEdit undoableEdit = new DefaultUndoableEdit();
  private int currentUpdateLevel = 0;
  
  private final WeakReference<GraphController> controller;
  
  /**
   * Constructor.
   * @param controller  graph controller
   */
  public McGraphModel(GraphController controller) {
    this.controller = new WeakReference<>(controller);
  }

  /**
   * Append cells to the model. 
   * @param cellObject cells to be appended.
   */
  public void appendCells(CellObject ...cellObject) {
    beginUpdate();
    for (CellObject cell : cellObject) {
      execute(new CellChange(this, cell, cellList.size(), true));
    }
    endUpdate();
  }
  
  public void innserInsertCell(CellObject cellObject, int index) {
    cellSet.add(cellObject);
    cellList.add(index, cellObject);
  }
  
  @Override
  public boolean hasCell(CellObject cellObject) {
    return cellSet.contains(cellObject);
  }

  /**
   * Remove cells.
   * @param cellObject cells to be removed.
   * @return cells that have been removed successfully.
   */
  public CellObject[] removeCells(CellObject ...cellObject) {
    beginUpdate();
    List<CellObject> objects = new ArrayList<>();
    for (CellObject cell :cellObject) {
      if (hasCell(cell)) {
        cell.destroy();
        objects.add(cell);
        int index = cellList.indexOf(cell);
        execute(new CellChange(this, cell, index, false));
      }
    }
    cellSet.removeAll(objects);
    cellList.removeAll(objects);
    endUpdate();
    return objects.toArray(new CellObject[0]);
  }
  
  /**
   * 调整cell到指定索引.
   * @param cellObject cell
   * @param index 索引
   */
  public void orderCell(CellObject cellObject, int index) {
    if (index < 0 || index >= cellList.size()) {
      return;
    }
    int currentIndex = cellList.indexOf(cellObject);
    if (currentIndex == index) {
      return;
    }
    
    beginUpdate();
    execute(new CellMove(this, currentIndex, index));
    endUpdate();
  }
  
  /**
   * 内部移动cell.
   * @param cell cell
   * @param to 移动到的位置
   */
  public void innerMoveCell(CellObject cell, int to) {
    if (!cellSet.contains(cell) || to < 0 || to > cellList.size()) {
      return;
    }
    cellList.move(cell, to);
  }
  
  public void innerRemoveCell(CellObject cell) {
    cellSet.remove(cell);
    cellList.remove(cell);
  }

  /**
   * 添加link.
   * @param link 要添加的link
   */
  public void appendLinks(LinkObject ...link) {
    for (LinkObject item : link) {
      linkMap.put(item.getConnectors(), item);
    }
    links.addAll(link);
  }
  
  /**
   * 查找链接.
   * @param first   first connector
   * @param second  second connector
   * @param directed 是否为有向查找，如果为有向查找，那么链接中保存的connector顺序必须跟参数的一致 
   * @return 找到的链接对象
   */
  public LinkObject findLinks(Connector first, Connector second, boolean directed) {
    LinkObject result = linkMap.get(new Pair<Connector, Connector>(first, second));
    if (result == null && !directed) {
      result = linkMap.get(new Pair<Connector, Connector>(second, first));
    }
    return result;
  }
  
  @Override
  public CellObject findCell(CellBindedObject bindedObject) {
    for (CellObject cell : cellList) {
      if (cell.getBindedObject() == bindedObject) {
        return cell;
      }
    }
    return null;
  }

  /**
   * Remove links.
   * @param link links to be removed.
   * @return links that have been removed successfully.
   */
  public LinkObject[] removeLinks(LinkObject ...link) {
    List<LinkObject> links = new ArrayList<>();
    for (LinkObject singleLink : link) {
      if (linkMap.containsValue(singleLink)) {
        singleLink.destroy();
        links.add(singleLink);
        linkMap.inverse().remove(singleLink);
      }
    }
    this.links.removeAll(links);
    return links.toArray(new LinkObject[0]);
  }
  
  /**
   * Get the link collection which relate to specified cell.
   * @param cellObject cell object
   * @return links
   */
  public LinkObject[] getRelatedLink(CellObject cellObject) {
    List<LinkObject> resultLinks = new ArrayList<>();
    for (Connector connector : cellObject.getConnectors()) {
      for (LinkObject linkObject : links) {
        if (linkObject.getConnectors().getKey() == connector 
            || linkObject.getConnectors().getValue() == connector) {
          resultLinks.add(linkObject);
        } //end if
      } //end for
    } //end for
    return resultLinks.toArray(new LinkObject[0]);
  }

  @Override
  public ObservableList<LinkObject> getObservableLinks() {
    return readonlyLinks;
  }

  @Override
  public ObservableList<CellObject> getObservableCells() {
    return readonlyCellList;
  }

  @Override
  public void beginUpdate() {
    currentUpdateLevel++;
  }

  @Override
  public void endUpdate() {
    currentUpdateLevel--;
    if (currentUpdateLevel == 0) {
      // fire new undoable edit event.
      EditHappenEvent event = new EditHappenEvent();
      event.setEdit(undoableEdit);
      GraphController graphController = controller.get();
      if (graphController != null) {
        graphController.getEventBus().post(event);
      }
      undoableEdit = new DefaultUndoableEdit();
    }
  }
  
  protected void execute(UndoableChange change) {
    change.execute();
    undoableEdit.addChange(change);
  }

  /**
   * 销毁数据.
   */
  public void destroy() {
    for (CellObject cellObject : getObservableCells()) {
      cellObject.visibleProperty().unbind();
      cellObject.highLightProperty().unbind();
      cellObject.hoverProperty().unbind();
      cellObject.highLightColorProperty().unbind();
      
      
      cellObject.getHeightProperty().unbind();
      cellObject.getWidthProperty().unbind();
      cellObject.getXProperty().unbind();
      cellObject.getYProperty().unbind();
      cellObject.getSelectedProperty().unbind();
      cellObject.getAngleProperty().unbind();
      
      for (CellPropertyUpdater updater : cellObject.getPropertyUpdaters().values()) {
        updater.destroy();
      }
      cellObject.getPropertyUpdaters().clear();
      
      for (Connector connector : cellObject.getConnectors()) {
        connector.visibleProperty().unbind();
        connector.highLightProperty().unbind();
        connector.hoverProperty().unbind();
        connector.highLightColorProperty().unbind();
        connector.destroy();
      }
      cellObject.destroy();
    }
    
    for (LinkObject linkObject : getObservableLinks()) {
      linkObject.visibleProperty().unbind();
      linkObject.highLightProperty().unbind();
      linkObject.hoverProperty().unbind();
      linkObject.highLightColorProperty().unbind();
      linkObject.destroy();
    }
    
    cellSet.clear();
    cellList.clear();
    linkMap.clear();
    links.clear();
  }
}
