package com.mc.graph;

import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.ConnectorBehavior;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.LinkBehavior;
import com.mc.graph.interfaces.NewLinkSkin;
import com.mc.graph.interfaces.SkinManager;
import com.mc.graph.util.LinkUtil;
import com.mc.graph.util.NodeUtil;
import javafx.beans.binding.DoubleBinding;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.paint.Color;
import javafx.scene.shape.Circle;
import javafx.scene.shape.Path;

public class DefaultNewLinkSkin implements NewLinkSkin {

  protected Connector sender;

  protected Path connectionPath;
  protected Circle receiverConnectorUi;

  protected Parent parent;
  protected Parent container;
  protected LinkBehavior behavior;

  protected SkinManager skinManager;

  /**
   * Constructor.
   * 
   * @param sender start connector of new link.
   * @param parent parent to add new link.
   * @param container TODO
   * @param skinManager skin manager
   */
  public DefaultNewLinkSkin(Connector sender, Parent parent, Parent container,
      SkinManager skinManager) {
    this.sender = sender;
    this.parent = parent;
    this.container = container;
    this.skinManager = skinManager;
    init();
  }

  protected void init() {
    initSenderAndReceiver();
    initConnnectionPath();
    initStyle();
    add();
    connectionPath.toFront();
    receiverConnectorUi.toFront();
  }

  protected void initSenderAndReceiver() {
    receiverConnectorUi = new Circle(15);
    receiverConnectorUi
        .setLayoutX(skinManager.getConnectorSkin(sender).getContainerXposProperty().get());
    receiverConnectorUi
        .setLayoutY(skinManager.getConnectorSkin(sender).getContainerYposProperty().get());
    receiverConnectorUi.setUserData(NodeUtil.NODE_IGNORE);
  }

  protected void initConnnectionPath() {
    connectionPath = LinkUtil.createCurveLinkPath(skinManager.getConnectorSkin(sender),
        new ReceiverSkin(), parent, container);
    connectionPath.setUserData(NodeUtil.NODE_IGNORE);
  }



  protected void initStyle() {
    connectionPath.setStroke(new Color(0, 0, 0, 1));
    connectionPath.setStrokeWidth(5);
  }


  @Override
  public Node getReceiverUi() {
    return receiverConnectorUi;
  }

  @Override
  public void add() {
    NodeUtil.addToParent(parent, receiverConnectorUi);
    NodeUtil.addToParent(parent, connectionPath);
    receiverConnectorUi.toFront();
    connectionPath.toFront();
  }

  @Override
  public void remove() {
    NodeUtil.removeFromParent(receiverConnectorUi);
    NodeUtil.removeFromParent(connectionPath);
  }

  @Override
  public void setBehavior(LinkBehavior behavior) {
    if (this.behavior != null) {
      this.behavior.detach();
    }
    this.behavior = behavior;
    this.behavior.attach();
  }

  @Override
  public Parent getParent() {
    return parent;
  }

  @Override
  public Connector getSender() {
    return sender;
  }

  @Override
  public void setNoLink() {
    receiverConnectorUi.setFill(Color.BLACK);
  }

  @Override
  public void setLinkable() {
    receiverConnectorUi.setFill(Color.YELLOW);
  }

  class ReceiverSkin implements ConnectorSkin {
    private DoubleProperty sceneXposProperty = new SimpleDoubleProperty();
    private DoubleProperty sceneYposProperty = new SimpleDoubleProperty();
    private SimpleBooleanProperty linkableProperty = new SimpleBooleanProperty(true);

    public ReceiverSkin() {
      DoubleBinding xposBinding = new DoubleBinding() {
        {
          super.bind(receiverConnectorUi.parentProperty(), parent.layoutXProperty(),
              parent.translateXProperty(), receiverConnectorUi.layoutXProperty(),
              receiverConnectorUi.translateXProperty());
        }

        @Override
        protected double computeValue() {
          return LinkUtil.localToGrandParent(parent, container,
              receiverConnectorUi.getLayoutX() + receiverConnectorUi.getTranslateX(), true);
        }
      };
      sceneXposProperty.bind(xposBinding);

      DoubleBinding yposBinding = new DoubleBinding() {
        {
          super.bind(receiverConnectorUi.parentProperty(), parent.layoutYProperty(),
              parent.translateYProperty(), receiverConnectorUi.layoutYProperty(),
              receiverConnectorUi.translateYProperty());
        }

        @Override
        protected double computeValue() {
          return LinkUtil.localToGrandParent(parent, container,
              receiverConnectorUi.getLayoutY() + receiverConnectorUi.getTranslateY(), false);
        }
      };
      sceneYposProperty.bind(yposBinding);
    }

    @Override
    public void add() {

    }

    @Override
    public void remove() {

    }

    @Override
    public Parent getParent() {
      return null;
    }

    @Override
    public DoubleProperty getContainerXposProperty() {
      return sceneXposProperty;
    }

    @Override
    public DoubleProperty getContainerYposProperty() {
      return sceneYposProperty;
    }

    @Override
    public void setConnector(Connector connector) {

    }

    @Override
    public Connector getConnector() {
      return null;
    }

    @Override
    public Node getNode() {
      return null;
    }

    @Override
    public void addBehavior(ConnectorBehavior behavior) {

    }

    @Override
    public BooleanProperty linkableProperty() {
      return linkableProperty;
    }

  }

}
