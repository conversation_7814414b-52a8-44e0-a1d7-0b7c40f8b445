package org.controlsfx.control;

import javafx.beans.property.ObjectProperty;
import javafx.beans.property.Property;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ObservableValue;
import lombok.Getter;
import lombok.NonNull;

import java.util.Optional;

public class NumberPropertyItem extends AbstractPropertyItem {
  private final Property<Number> item;
  private final Class<?> type;
  private final boolean needToUnbindValue;
  private ObjectProperty<Number> observableValue;
  @Getter
  private Number maxValue = null;
  @Getter
  private Number minValue = null;
  @Getter
  private Number precision = null;

  private final NumberConverter converter;


  public interface NumberConverter {
    
    Number toValue(Number value);
    
    Number toDisplay(Number value);
  }

  private static class SimpleNumberConverter implements NumberConverter {
    @Override
    public Number toValue(Number value) {
      return value;
    }

    @Override
    public Number toDisplay(Number value) {
      return value;
    }
  }
  
  public NumberPropertyItem(Property<Number> item, Class<?> type) {
    this(item, type, false);
  }
  
  /**
   * 数值属性.
   * 
   * @param item 属性值
   * @param type 属性值类型
   * @param needToUnbindValue 当要销毁时是否需要unbind属性值
   */
  public NumberPropertyItem(Property<Number> item, Class<?> type, boolean needToUnbindValue) {
    this.item = item;
    this.type = type;
    this.needToUnbindValue = needToUnbindValue;
    this.converter = new SimpleNumberConverter();
  }

  /**
   * 数值属性.
   * 
   * @param item 属性值
   * @param type 属性值类型
   * @param converter 转换器
   */
  public NumberPropertyItem(Property<Number> item, Class<?> type, @NonNull NumberConverter converter) {
    this.item = item;
    this.type = type;
    this.needToUnbindValue = false;
    this.converter = converter;
  }
  
  @Override
  public Optional<ObservableValue<? extends Object>> getObservableValue() {
    if (observableValue == null) {
      observableValue = new SimpleObjectProperty<>();
      observableValue.bind(item);
    }
    return Optional.of(observableValue);
  }

  @Override
  public Class<?> getType() {
    return type;
  }

  @Override
  public Object getValue() {
    if (item != null) {
      return converter.toDisplay(item.getValue());
    } else {
      return null;
    }
  }

  @Override
  public void setValue(Object value) {
    if (value instanceof Number && item != null) {
      item.setValue(converter.toValue((Number)value));
    }
  }

  @Override
  public void delete() {
    if (observableValue != null) {
      observableValue.unbind();
      observableValue = null;
    }
    if (item != null && needToUnbindValue) {
      item.unbind();
    }
  }
  
  public void setValueRange(Number minValue, Number maxValue) {
    this.maxValue = maxValue;
    this.minValue = minValue;
  }

  public void setPrecision(int precision) {
    this.precision = precision;
  }
}
