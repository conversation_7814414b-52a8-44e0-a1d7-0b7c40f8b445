package com.mc.common.collections;

import com.sun.javafx.collections.ObservableListWrapper;

import java.util.Collection;
import java.util.List;

public class ObservableListWrapperEx<E> extends ObservableListWrapper<E> {

  public ObservableListWrapperEx(List<E> list) {
    super(list);
  }

  /**
   * 移动child的位置.
   * @param item 要移动的项.
   * @param index 移动到的位置.
   */
  public void move(E item, int index) {
    int oldIndex = indexOf(item);
    if (oldIndex < 0 || oldIndex == index) {
      return;
    }
    doRemove(indexOf(item));
    doAdd(index, item);
    beginChange();
    int from = 0;
    int to = 0;
    int[] perm = null;
    if (oldIndex < index) {
      from = oldIndex;
      to = index + 1;
      perm = new int[to - from];
      //从前面移到后面
      perm[0] = to - 1;
      for (int i = from + 1; i < to; i++) {
        perm[i - from] = i - 1;     // perm[oldindex] = newindex
      }
    } else {
      from = index;
      to = oldIndex + 1;
      perm = new int[to - from];
      //从后面移到前面
      perm[to - 1 - from] = from;
      for (int i = from; i < to - 1; i++) {
        perm[i - from] = i + 1;    // perm[oldindex] = newindex
      }
    }
    nextPermutation(from, to, perm);
    endChange();
  }
  
  /**
   * 删除后添加.
   * @param removeItems 要删除的项
   * @param addItems 要添加的项
   * @param addIndex 添加的位置
   */
  public void removeAndAdd(Collection<E> removeItems, Collection<E> addItems, int addIndex) {
    beginChange();
    removeAll(removeItems);
    addAll(addIndex, addItems);
    endChange();
  }

}
