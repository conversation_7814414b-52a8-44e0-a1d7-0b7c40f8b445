package com.mc.common.io;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;

public class StringInputStream extends InputStream {
  private final InputStream is;

  /**
   * .
   */
  public StringInputStream(String string) {
    byte[] str = null;
    try {
      str = string.getBytes("UTF-8");
    } catch (UnsupportedEncodingException ex) {
      ex.printStackTrace();
      str = new byte[0];
    }
    this.is = new ByteArrayInputStream(str);
  }

  @Override
  public int read() throws IOException {
    return this.is.read();
  }

  @Override
  public int available() throws IOException {
    return this.is.available();
  }

  @Override
  public void close() throws IOException {
    this.is.close();
  }

  @Override
  public synchronized void reset() throws IOException {
    this.is.reset();
  }
}

