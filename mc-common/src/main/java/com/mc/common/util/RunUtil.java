package com.mc.common.util;

import javafx.application.Platform;

import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.FutureTask;
import java.util.logging.Level;
import java.util.logging.Logger;

public class RunUtil {
  private static final Logger LOG = Logger.getLogger(RunUtil.class.getName());

  /**
   * 把工作放到ui线程，并等待完成.
   * 
   * @param runnable 工作
   */
  public static void waitForUiDone(Runnable runnable) {
    if (Platform.isFxApplicationThread()) {
      runnable.run();
    } else {
      FutureTask<Void> task = new FutureTask<Void>(new Callable<Void>() {

        @Override
        public Void call() throws Exception {
          runnable.run();
          return null;
        }
      });
      PlatformUtility.runInFxThread(task);
      try {
        task.get();
      } catch (InterruptedException | ExecutionException exception) {
        LOG.log(Level.SEVERE, "Wait for ui done fail!", exception);
      }
    }
  }
  
  /**
   * 把工作放到ui线程，并等待完成.
   * 
   * @param runnable 工作
   * @throws Exception runnable的异常 
   */
  public static <T extends Exception> void waitForUiDoneWithException(ThrowingRunnable<T> runnable)
      throws T {
    if (Platform.isFxApplicationThread()) {
      runnable.run();
    } else {
      FutureTask<Void> task = new FutureTask<Void>(new Callable<Void>() {

        @Override
        public Void call() throws Exception {
          runnable.run();
          return null;
        }
      });
      PlatformUtility.runInFxThread(task);
      try {
        task.get();
      } catch (InterruptedException | ExecutionException exception) {
        LOG.log(Level.SEVERE, "Wait for ui done fail!", exception);
      }
    }
  }
  
  public interface ThrowingRunnable<T extends Exception> {
    void run() throws T;
  }
}
