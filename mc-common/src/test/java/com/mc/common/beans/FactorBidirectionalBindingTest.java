package com.mc.common.beans;

import static org.junit.Assert.*;
import org.junit.Test;
import com.mc.common.beans.FactorBidirectionalBinding.FactorType;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.SimpleDoubleProperty;

public class FactorBidirectionalBindingTest{
  
  @Test
  public void test() {
    DoubleProperty propertyA = new SimpleDoubleProperty(1);
    DoubleProperty propertyB = new SimpleDoubleProperty(1);
    DoubleProperty factorProprety = new SimpleDoubleProperty(0.5);
    
    FactorBidirectionalBinding.bind(propertyA, propertyB, factorProprety, FactorType.MULTIPLY);
    propertyB.set(2);
    assertEquals(1, propertyA.get(), 1e-10);
    propertyA.set(10);
    assertEquals(20, propertyB.get(), 1e-10);
    factorProprety.set(0.3);
    assertEquals(6, propertyA.get(), 1e-10);
    assertEquals(20, propertyB.get(), 1e-10);
    
    FactorBidirectionalBinding.unbind(propertyA, propertyB, factorProprety);
    factorProprety.set(0.2);
    assertEquals(6, propertyA.get(), 1e-10);
    assertEquals(20, propertyB.get(), 1e-10);
    propertyA.set(5);
    assertEquals(0.2, factorProprety.get(), 1e-10);
    assertEquals(20, propertyB.get(), 1e-10);
    propertyB.set(15);
    assertEquals(5, propertyA.get(), 1e-10);
    assertEquals(0.2, factorProprety.get(), 1e-10);
  }
}
