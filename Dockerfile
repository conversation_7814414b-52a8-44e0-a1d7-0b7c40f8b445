# 此镜像用于构建vpm的基础镜像 image: 192.168.1.233/vpm/vpm-base:jdk8-mvn
FROM ubuntu:22.04
ENV JAVA_HOME /usr/lib/jvm/zulujdk-8
ENV MAVEN_HOME /usr/share/maven
ENV PATH $PATH:$JAVA_HOME/bin

WORKDIR /root
RUN apt-get update \
  && apt-get install -y ca-certificates wget git --no-install-recommends \
  && wget https://dlcdn.apache.org/maven/maven-3/3.9.6/binaries/apache-maven-3.9.6-bin.tar.gz \
  && tar -zxvf apache-maven-3.9.6-bin.tar.gz \
  && rm apache-maven-3.9.6-bin.tar.gz \
  && mv apache-maven-3.9.6 /usr/share/maven \
  && ln -s ${MAVEN_HOME}/bin/mvn /usr/bin/mvn \
  && wget https://cdn.azul.com/zulu/bin/zulu8.76.0.17-ca-fx-jdk8.0.402-linux_x64.tar.gz \
  && tar -zxvf zulu8.76.0.17-ca-fx-jdk8.0.402-linux_x64.tar.gz \
  && rm zulu8.76.0.17-ca-fx-jdk8.0.402-linux_x64.tar.gz \
  && mkdir /usr/lib/jvm \
  && mv zulu8.76.0.17-ca-fx-jdk8.0.402-linux_x64 /usr/lib/jvm/zulujdk-8 \
  && rm -rf /var/lib/apt/lists/*
