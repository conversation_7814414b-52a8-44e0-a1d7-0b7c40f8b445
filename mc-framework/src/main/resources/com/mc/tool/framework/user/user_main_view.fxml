<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ListView?>
<?import javafx.scene.control.PasswordField?>
<?import javafx.scene.control.Tab?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TabPane?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<fx:root type="javafx.scene.layout.VBox" xmlns="http://javafx.com/javafx/8"
         xmlns:fx="http://javafx.com/fxml/1" stylesheets="@user_main_view.css"
         id="rootPane">
  <VBox id="mainContainer" HBox.hgrow="ALWAYS" VBox.vgrow="ALWAYS">
    <HBox id="panel" HBox.hgrow="ALWAYS" VBox.vgrow="ALWAYS" fx:id="mainPanel">
      <VBox id="panel" minWidth="200" maxWidth="200" HBox.hgrow="ALWAYS"
            fx:id="leftPanel">
        <HBox id="panel" maxHeight="30" VBox.vgrow="ALWAYS"/>
        <TableView VBox.vgrow="ALWAYS" fx:id="usersTable"/>
      </VBox>
      <Region minWidth="5" id="seperator"/>
      <VBox id="panel" HBox.hgrow="ALWAYS" fx:id="rightPanel">
        <HBox id="panel" minHeight="300" prefHeight="300" VBox.vgrow="ALWAYS"
              fx:id="topPanel">
          <VBox id="userPanel" minWidth="300" maxWidth="300" HBox.hgrow="ALWAYS"
                fx:id="userPanel">
            <HBox maxHeight="40" VBox.vgrow="ALWAYS">
              <Label text="ID" prefWidth="120"/>
              <TextField prefWidth="200" editable="false" fx:id="idTxtField"/>
            </HBox>
            <HBox maxHeight="40" VBox.vgrow="ALWAYS">
              <Label text="name" prefWidth="120"/>
              <TextField prefWidth="200" fx:id="usernameTxtField"/>
            </HBox>
            <HBox maxHeight="40" VBox.vgrow="ALWAYS">
              <Label text="password" prefWidth="120"/>
              <PasswordField prefWidth="200" fx:id="passwordPwdField"/>
            </HBox>
            <ListView VBox.vgrow="ALWAYS" fx:id="userLevelListView"/>
          </VBox>
          <VBox id="permissionPanel" minWidth="300" maxWidth="300"
                HBox.hgrow="ALWAYS" fx:id="permissionPanel">
            <ListView VBox.vgrow="ALWAYS" fx:id="permissionListView"/>
          </VBox>
        </HBox>
        <HBox id="panel" VBox.vgrow="ALWAYS" fx:id="bottomPanel">
          <TabPane HBox.hgrow="ALWAYS" fx:id="funcTable">
            <tabs>
              <Tab text="CPU访问控制" closable="false">
                <HBox id="tabPanel" VBox.vgrow="ALWAYS">
                  <VBox id="Panel" minWidth="300" HBox.hgrow="ALWAYS"
                        fx:id="operationPanel">
                    <HBox id="titlePanel" maxHeight="40" VBox.vgrow="ALWAYS"
                          alignment="CENTER_LEFT">
                      <Label id="font-green" text="Table Title" fx:id="operationTitleLabel"/>
                    </HBox>
                    <TableView VBox.vgrow="ALWAYS" fx:id="operationTable"/>
                  </VBox>
                  <VBox id="Panel" minWidth="300" HBox.hgrow="ALWAYS" fx:id="videoPanel">
                    <HBox id="titlePanel" maxHeight="40" VBox.vgrow="ALWAYS"
                          alignment="CENTER_LEFT">
                      <Label id="font-orange" text="Table Title" fx:id="videoTitleLabel"/>
                    </HBox>
                    <TableView VBox.vgrow="ALWAYS" fx:id="videoTable"/>
                  </VBox>
                  <VBox id="Panel" minWidth="300" HBox.hgrow="ALWAYS"
                        fx:id="nonPermissionPanel">
                    <HBox id="titlePanel" maxHeight="40" VBox.vgrow="ALWAYS"
                          alignment="CENTER_LEFT">
                      <Label id="font-red" text="Table Title" fx:id="nonPermissionTitleLabel"/>
                    </HBox>
                    <TableView VBox.vgrow="ALWAYS" fx:id="nonPermissionTable"/>
                  </VBox>
                </HBox>
              </Tab>
              <Tab text="收藏列表" closable="false">
                <HBox id="tabPanel" VBox.vgrow="ALWAYS">
                  <VBox id="Panel" minWidth="200" HBox.hgrow="ALWAYS">
                    <HBox id="titlePanel" maxHeight="40" VBox.vgrow="ALWAYS"
                          alignment="CENTER_LEFT">
                      <Label text="Table Title" fx:id="cpuTitleLabel"/>
                    </HBox>
                    <TableView VBox.vgrow="ALWAYS" fx:id="cpuTable"/>
                  </VBox>
                  <VBox id="titlePanel" maxWidth="60" VBox.vgrow="ALWAYS"
                        fx:id="btnPanel" alignment="CENTER">
                    <Button minWidth="60"/>
                    <Button minWidth="60"/>
                  </VBox>
                  <VBox id="Panel" HBox.hgrow="ALWAYS">
                    <HBox id="titlePanel" maxHeight="40" VBox.vgrow="ALWAYS"
                          alignment="CENTER_LEFT">
                      <Label text="Table Title" fx:id="roundTitleLabel"/>
                    </HBox>
                    <TableView VBox.vgrow="ALWAYS" fx:id="roundTable"/>
                  </VBox>
                </HBox>
              </Tab>
              <Tab text="宏" closable="false">
                <HBox id="tabPanel" VBox.vgrow="ALWAYS">
                  <TableView HBox.hgrow="ALWAYS" fx:id="macroTable"/>
                </HBox>
              </Tab>
            </tabs>
          </TabPane>
        </HBox>
      </VBox>
    </HBox>
    <HBox id="bottomPanel" maxHeight="40" fx:id="bottomPanel">
      <Button minWidth="100" text="自定义设置..." fx:id="customBtn"/>
      <Button minWidth="80" text="复制设置" fx:id="copyBtn"/>
      <Region HBox.hgrow="ALWAYS"/>
      <Button minWidth="60" text="新建用户" fx:id="createUserBtn"/>
      <Button minWidth="60" text="删除用户" fx:id="deleteUserBtn"/>
      <Button minWidth="60" text="应用" fx:id="submitBtn"/>
      <Button minWidth="60" text="取消" fx:id="cancelBtn"/>
    </HBox>
  </VBox>
</fx:root>