@import "../scenario_list.css";

.scroll-pane > .viewport {
    -fx-background-color: transparent;
}

.label {
    -fx-background-repeat: no-repeat;
}

#zoomin-btn {
    -fx-background-image: url("zoomin_normal.png");
    -fx-background-repeat: no-repeat;
    -fx-background-position: center;
}

#zoomin-btn:hover {
    -fx-background-image: url("zoomin_hover.png");
}

#zoomin-btn:disable {
    -fx-background-image: url("zoomin_disable.png");
}

#zoomout-btn {
    -fx-background-image: url("zoomout_normal.png");
    -fx-background-repeat: no-repeat;
    -fx-background-position: center;
}

#zoomout-btn:hover {
    -fx-background-image: url("zoomout_hover.png");
}

#zoomout-btn:disable {
    -fx-background-image: url("zoomout_disable.png");
}

#zoomin-btn:disable {
    -fx-background-image: url("zoomin_disable.png");
}

#restore-btn {
    -fx-background-image: url("restore_normal.png");
    -fx-background-repeat: no-repeat;
    -fx-background-position: center;
}

#restore-btn:hover {
    -fx-background-image: url("restore_hover.png");
}

#restore-btn:disable {
    -fx-background-image: url("restore_disable.png");
}

#preview-btn {
    -fx-background-image: url("preview_normal.png");
    -fx-background-repeat: no-repeat;
    -fx-background-position: center;
}

#preview-btn:hover {
    -fx-background-image: url("preview_hover.png");
}

#preview-btn:disable {
    -fx-background-image: url("preview_disable.png");
}


#graph-tool-bar {
    -fx-padding: 0 5 0 0;
    -fx-spacing: 40;
    -fx-background-color: #f7f7f7;
}


.seperator {
    -fx-background-color: #f0f0f0;
}


#list-mode-btn {
    -fx-background-image: url("mode_normal.png");
    -fx-background-repeat: no-repeat;
    -fx-background-position: center;
}

#list-mode-btn:hover {
    -fx-background-image: url("mode_hover.png");
}

#list-mode-btn:disable {
    -fx-background-image: url("mode_disable.png");
}

#close-btn {
    -fx-background-image: url("close_normal.png");
    -fx-background-repeat: no-repeat;
    -fx-background-position: center;
}

#close-btn:hover {
    -fx-background-image: url("close_hover.png");
}

#close-btn:disable {
    -fx-background-image: url("close_disable.png");
}

#scenario-list-title-box {
    -fx-padding: 0 5 0 5;
    -fx-background-color: #cccccc;
}

#scenario-list-box {
    -fx-border-width: 0 0 0 1;
    -fx-border-color: #cccccc;
}

