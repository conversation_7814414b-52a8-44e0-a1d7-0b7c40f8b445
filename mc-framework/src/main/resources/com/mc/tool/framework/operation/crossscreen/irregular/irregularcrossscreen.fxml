<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.Group?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<fx:root type="javafx.scene.layout.VBox" xmlns="http://javafx.com/javafx/8"
         xmlns:fx="http://javafx.com/fxml/1" stylesheets="@irregularcrossscreen.css">
  <HBox VBox.Vgrow="ALWAYS">
    <HBox HBox.Hgrow="ALWAYS" fx:id="graphBox"/>
    <ScrollPane HBox.Hgrow="ALWAYS" fitToHeight="true"
                fitToWidth="true" fx:id="screenScroller">
      <Group>
        <StackPane fx:id="screenStack">
          <Group fx:id="screenGroup">
            <GridPane id="screen-list" fx:id="screenList"/>
          </Group>
        </StackPane>
      </Group>
    </ScrollPane>
  </HBox>
  <HBox prefHeight="36" minHeight="36" alignment="CENTER" id="graph-tool-bar">
    <Label id="zoomin-btn" fx:id="zoominBtn" prefWidth="18"
           prefHeight="18" onMouseClicked="#onZoomin"/>
    <Label id="zoomout-btn" fx:id="zoomoutBtn" prefWidth="18"
           prefHeight="18" onMouseClicked="#onZoomout"/>
    <Label id="restore-btn" prefWidth="18" prefHeight="18"
           onMouseClicked="#onRestore"/>
  </HBox>
  <Region minHeight="1" styleClass="seperator"/>
</fx:root>