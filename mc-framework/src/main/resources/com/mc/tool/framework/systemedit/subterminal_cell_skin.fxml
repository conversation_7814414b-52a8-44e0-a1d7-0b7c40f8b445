<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<HBox xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1"
      stylesheets="@subterminal_cell_skin.css" styleClass="subterminal" minWidth="100" maxWidth="100" minHeight="35"
      maxHeight="35">
  <Region minWidth="5" maxWidth="5" fx:id="connector"/>
  <HBox HBox.Hgrow="ALWAYS" alignment="CENTER" fx:id="nameLabelContainer">
    <Label minHeight="35" maxHeight="35" textAlignment="center" fx:id="nameLabel"/>
  </HBox>
  <Region minWidth="5" maxWidth="5"/>
</HBox>