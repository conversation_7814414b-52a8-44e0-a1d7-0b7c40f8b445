<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<fx:root type="javafx.scene.layout.VBox" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1"
         stylesheets="@property_view.css" id="root">
  <HBox fx:id="titleBox" alignment="center">
    <Label fx:id="deviceIcon" prefWidth="23" prefHeight="23"/>
    <Label fx:id="deviceNameLabel"/>
    <Region HBox.hgrow="ALWAYS"/>
    <Label id="edit-icon" fx:id="editConfigBtn" prefWidth="20" prefHeight="20"/>
  </HBox>
  <GridPane fx:id="propertiesGrid"/>
  <VBox fx:id="emptyBox" alignment="center" VBox.Vgrow="ALWAYS">
    <Label fx:id="emptyIcon" id="empty-icon" prefWidth="47" prefHeight="33"/>
  </VBox>
</fx:root>