<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.canvas.Canvas?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<VBox xmlns="http://javafx.com/javafx/8"
      xmlns:fx="http://javafx.com/fxml/1" stylesheets="@scenario_cell.css" fx:id="root" id="scenario-cell" alignment="center">
  <HBox>
    <Canvas width="94" height="40" fx:id="canvas">
    </Canvas>
  </HBox>
  <HBox prefHeight="20" minHeight="20" alignment="CENTER" id="name-box" fx:id="nameBox">
    <Label fx:id="scenarioNameLabel" id="name-label"/>
  </HBox>
</VBox>