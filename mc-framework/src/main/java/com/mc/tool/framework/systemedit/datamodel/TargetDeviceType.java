package com.mc.tool.framework.systemedit.datamodel;

import com.mc.tool.framework.utility.I18nUtility;

/**
 * .
 */
public enum TargetDeviceType {
  COMPUTER(false, I18nUtility.getI18nBundle("systemedit").getString("device_type.computer")),
  DVD(false, I18nUtility.getI18nBundle("systemedit").getString("device_type.dvd")),
  GRID_LINE_N(true, ""),
  GRID_LINE_T(true, ""),
  GRID_LINE_R(true, ""),
  GRID_LINE_DUAL(true, ""),
  MONITOR(true, I18nUtility.getI18nBundle("systemedit").getString("device_type.monitor")),
  PROJECTOR(true, I18nUtility.getI18nBundle("systemedit").getString("device_type.projector"));

  private boolean rxType;
  private String name;

  private TargetDeviceType(boolean isRx, String name) {
    rxType = isRx;
    this.name = name;
  }

  public boolean isRx() {
    return rxType;
  }

  public boolean isTx() {
    return !rxType;
  }

  public String getName() {
    return name;
  }
}
