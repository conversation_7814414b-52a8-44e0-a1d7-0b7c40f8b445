package com.mc.tool.framework.systemedit.view;

import com.mc.graph.interfaces.CellBehavior;
import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.SkinManager;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditSubTerminal;
import com.mc.tool.framework.utility.JavaFxUtility;
import java.io.IOException;
import java.net.URL;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.ResourceBundle;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.control.Label;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Region;
import javafx.scene.paint.Color;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class SubTerminalCellSkin extends AbstractCellSkinEx implements Initializable {
  private HBox region;
  @FXML
  private Label nameLabel;
  @FXML
  private HBox nameLabelContainer;
  @FXML
  private Region connector;
  private Map<Object, ConnectorSkin> connectorSkins = new HashMap<>();
  private boolean leftItem = true;

  public SubTerminalCellSkin(CellObject cellobject, Parent parent, Parent container,
                             SkinManager skinManager) {
    super(cellobject, parent, container, skinManager);
  }

  @Override
  public Region getRegion() {
    return region;
  }

  @Override
  public Collection<ConnectorSkin> getConnectorSkins() {
    return connectorSkins.values();
  }

  @Override
  public Color getSelectionBorderColor() {
    return Color.RED;
  }

  @Override
  public boolean isSelectionEffectEnabled() {
    return true;
  }

  @Override
  public boolean isResizeble() {
    return false;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    init();
  }

  @Override
  protected void initInner() {

  }

  @Override
  protected void initRegion() {
    try {
      FXMLLoader loader = new FXMLLoader(
          getClass().getResource("/com/mc/tool/framework/systemedit/subterminal_cell_skin.fxml"));
      loader.setController(this);
      region = loader.load();
      region.setUserData(this);

      updateLayout(cellObject.getBindedObject());
      cellObject.getBindedObjectProperty().addListener((observable, oldValue, newValue) -> updateLayout(newValue));

    } catch (IOException exc) {
      log.warn("Can not load subterminal_cell_skin.fxml", exc);
    }
  }

  @Override
  protected void onConnectorChange() {
    if (getCell().getBindedObject() == null) {
      return;
    }
    VisualEditNode node = (VisualEditNode) getCell().getBindedObject();
    for (Object id : node.getConnectorId()) {
      if (connectorSkins.containsKey(id)
          && connectorSkins.get(id).getConnector() == getCell().getConnector(id)) {
        continue;
      }
      if (getCell().getConnector(id) == null) {
        continue;
      }
      Node connector = null;
      if (id.equals(VisualEditSubTerminal.CONNECTOR)) {
        connector = this.connector;
      } else {
        log.error("Unexpected connector id : {}.", id);
        continue;
      }
      SimpleConnectorSkin skin =
          new SimpleConnectorSkin(getCell().getConnector(id), region, region, container, connector);
      skinManager.setConnectorSkin(getCell().getConnector(id), skin);
      for (CellBehavior cellBehavior : cellBehaviors) {
        cellBehavior.createConnectorBehavior(skin);
      }
      connectorSkins.put(id, skin);
    }
    updateConnectorDirection();
  }

  @Override
  protected void layoutConnectors() {

  }

  private void updateLayout(CellBindedObject newValue) {

    if (newValue instanceof VisualEditSubTerminal) {
      VisualEditSubTerminal group = (VisualEditSubTerminal) newValue;
      // rx的布局与tx布局相反
      if (!SystemEditDefinition.nodeAtLeft(group)) {
        setRight();
      }
    }
  }

  private void setRight() {
    if (leftItem) {
      JavaFxUtility.reversePaneContent(region, false);
      leftItem = false;
      updateConnectorDirection();
      region.setStyle("-fx-background-position:right;");
    }
  }

  private void updateConnectorDirection() {
    SimpleConnectorSkin skin =
        (SimpleConnectorSkin) connectorSkins.get(VisualEditSubTerminal.CONNECTOR);
    if (skin != null) {
      skin.setConnectAtLeft(!leftItem);
    }
  }

  @Override
  protected Label getNameLabel() {
    return nameLabel;
  }

  @Override
  protected Node[] getNameLabelChangeSource() {
    return new Node[] {nameLabel, nameLabelContainer};
  }
}
