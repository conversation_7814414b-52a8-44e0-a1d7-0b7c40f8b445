package com.mc.tool.framework.systemedit.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.graph.interfaces.ConnectorIdentifier;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;

/**
 * .
 */
public class DefaultVisualEditTerminal extends AbstractVisualEditNode
    implements VisualEditTerminal {

  @Expose
  private boolean rx = true;
  protected ObservableList<ConnectorIdentifier> connectorIds = FXCollections.observableArrayList();
  @Expose
  protected ObjectProperty<TargetDeviceType> targetDeviceType = new SimpleObjectProperty<>();
  @Expose
  protected ObjectProperty<NetworkInterfaceType> networkInterfaceType =
      new SimpleObjectProperty<>(NetworkInterfaceType.SPF);
  @Expose
  protected ObjectProperty<MultimediaInterfaceType> multimediaInterfaceType =
      new SimpleObjectProperty<>(MultimediaInterfaceType.HDMI);

  protected BooleanProperty targetDeviceConnectedProperty = new SimpleBooleanProperty(false);

  protected BooleanProperty onlineProperty = new SimpleBooleanProperty(true);

  public DefaultVisualEditTerminal() {
  }

  /**
   * Constructor.
   *
   * @param rx 是否为rx
   */
  public DefaultVisualEditTerminal(boolean rx) {
    this.rx = rx;
  }

  @Override
  public String getNodeType() {
    return SystemEditDefinition.TERMINAL_CELL;
  }

  @Override
  public boolean isRx() {
    return rx;
  }

  @Override
  public boolean isTx() {
    return !rx;
  }

  @Override
  public TargetDeviceType getTargetDeviceType() {
    return targetDeviceTypeProperty().get();
  }

  @Override
  public void setTargetDeviceType(TargetDeviceType type) {
    targetDeviceTypeProperty().set(type);
  }

  @Override
  public NetworkInterfaceType getNetworkInterfaceType() {
    return networkInterfaceType.get();
  }

  @Override
  public void setNetworkInterfaceType(NetworkInterfaceType type) {
    networkInterfaceType.set(type);
  }

  @Override
  public MultimediaInterfaceType getMultimediaInterfaceType() {
    return multimediaInterfaceType.get();
  }

  @Override
  public void setMultimediaInterfaceType(MultimediaInterfaceType type) {
    multimediaInterfaceType.set(type);
  }

  @Override
  public boolean canSeperate() {
    return false;
  }

  @Override
  public void seperate(boolean sep) {

  }

  @Override
  public int getPortCount() {
    return 2;
  }

  @Override
  public ObservableList<ConnectorIdentifier> getConnectorId() {
    return connectorIds;
  }

  @Override
  public ObservableList<VisualEditTerminal> getAllTerminalChild() {
    ObservableList<VisualEditTerminal> result = FXCollections.observableArrayList();
    result.add(this);
    return result;
  }

  @Override
  public ObjectProperty<TargetDeviceType> targetDeviceTypeProperty() {
    return targetDeviceType;
  }

  @Override
  public ObjectProperty<NetworkInterfaceType> networkInterfaceTypeProperty() {
    return networkInterfaceType;
  }

  @Override
  public ObjectProperty<MultimediaInterfaceType> multimediaInterfaceTypeProperty() {
    return multimediaInterfaceType;
  }

  @Override
  public boolean isOnline() {
    return onlineProperty.get();
  }

  @Override
  public boolean isTargetDeviceConnected() {
    return targetDeviceConnectedProperty.get();
  }

  @Override
  public BooleanProperty targetDeviceConnectedProperty() {
    return targetDeviceConnectedProperty;
  }

  @Override
  public void init() {
    if (connectorIds.isEmpty()) {
      for (int i = 0; i < getPortCount(); i++) {
        connectorIds.add(ConnectorIdentifier.getIdentifier(i + 1));
      }
    }

    if (getTargetDeviceType() == null) {
      if (isRx()) {
        setTargetDeviceType(TargetDeviceType.MONITOR);
      } else {
        setTargetDeviceType(TargetDeviceType.COMPUTER);
      }
    }
  }

  @Override
  public ObservableList<VisualEditFunc> getAllFunctions() {
    return FXCollections.observableArrayList();
  }

  @Override
  public Resolution getResolution(int index) {
    return new Resolution();
  }

  @Override
  public BooleanProperty onlineProperty() {
    return onlineProperty;
  }

  @Override
  public boolean isValid() {
    return true;
  }
}
