package com.mc.tool.framework.operation.videowall.datamodel;

import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public class VideoClipData {

  /**
   * 缩放前的宽度.
   */
  @Getter
  @Setter
  private int originWidth = 1;
  /**
   * 缩放前的高度.
   */
  @Getter
  @Setter
  private int originHeight = 1;

  /**
   * 缩放前的裁剪的横坐标位置.
   */
  @Getter
  @Setter
  private int originClipX = 0;

  /**
   * 缩放前的裁剪的纵坐标位置.
   */
  @Getter
  @Setter
  private int originClipY = 0;

  /**
   * 缩放前的裁剪的宽度.
   */
  @Getter
  @Setter
  private int originClipWidth = 1;

  /**
   * 缩放前的裁剪的高度.
   */
  @Getter
  @Setter
  private int originClipHeight = 1;

  /**
   * 缩放后的裁剪的横坐标位置.
   */
  @Getter
  @Setter
  private int scaledClipX = 0;
  /**
   * 缩放后的裁剪的纵坐标位置.
   */
  @Getter
  @Setter
  private int scaledClipY = 0;
  /**
   * 缩放后的裁剪的宽度.
   */
  @Getter
  @Setter
  private int scaledClipWidth = 1;
  /**
   * 缩放后的裁剪的高度.
   */
  @Getter
  @Setter
  private int scaledClipHeight = 1;
  /**
   * 缩放后的宽度.
   */
  @Getter
  @Setter
  private int scaledWidth = 1;
  /**
   * 缩放后的高度.
   */
  @Getter
  @Setter
  private int scaledHeight = 1;

  @Getter
  @Setter
  private int outX = 0;
  @Getter
  @Setter
  private int outY = 0;
  @Getter
  @Setter
  private int outWidth = 1;
  @Getter
  @Setter
  private int outHeight = 1;
  @Getter
  @Setter
  private double alpha = 1.0;
  @Getter
  @Setter
  private int videoIndex = -1;
  @Getter
  @Setter
  private VisualEditTerminal source;
  /**
   * 切割索引.4k信号源使用.
   */
  @Getter
  @Setter
  private int splitIndex = -1;

  @Override
  public boolean equals(Object obj) {
    if (!(obj instanceof VideoClipData)) {
      return false;
    }
    VideoClipData input = (VideoClipData) obj;
    boolean result = true;
    result &= originWidth == input.originWidth;
    result &= originHeight == input.originHeight;
    result &= originClipWidth == input.originClipWidth;
    result &= originClipHeight == input.originClipHeight;
    result &= originClipX == input.originClipX;
    result &= originClipY == input.originClipY;
    result &= scaledClipWidth == input.scaledClipWidth;
    result &= scaledClipHeight == input.scaledClipHeight;
    result &= scaledClipX == input.scaledClipX;
    result &= scaledClipY == input.scaledClipY;
    result &= scaledWidth == input.scaledWidth;
    result &= scaledHeight == input.scaledHeight;
    result &= outWidth == input.outWidth;
    result &= outHeight == input.outHeight;
    result &= outX == input.outX;
    result &= outY == input.outY;
    result &= Math.abs(alpha - input.alpha) < 1e-10;
    result &= videoIndex == input.videoIndex;
    result &= source == input.source;
    result &= splitIndex == input.splitIndex;

    return result;
  }

  /**
   * 复制数据.
   *
   * @param target 目标位置
   */
  public void copyTo(VideoClipData target) {
    target.originWidth = originWidth;
    target.originHeight = originHeight;
    target.originClipWidth = originClipWidth;
    target.originClipHeight = originClipHeight;
    target.originClipX = originClipX;
    target.originClipY = originClipY;
    target.scaledClipWidth = scaledClipWidth;
    target.scaledClipHeight = scaledClipHeight;
    target.scaledClipX = scaledClipX;
    target.scaledClipY = scaledClipY;
    target.scaledWidth = scaledWidth;
    target.scaledHeight = scaledHeight;
    target.outWidth = outWidth;
    target.outHeight = outHeight;
    target.outX = outX;
    target.outY = outY;
    target.alpha = alpha;
    target.videoIndex = videoIndex;
    target.source = source;
    target.splitIndex = splitIndex;
  }

  @Override
  public int hashCode() {
    int hashcode = 1;
    int factor = 31;
    hashcode = hashcode * factor + originWidth;
    hashcode = hashcode * factor + originHeight;
    hashcode = hashcode * factor + originClipWidth;
    hashcode = hashcode * factor + originClipHeight;
    hashcode = hashcode * factor + originClipX;
    hashcode = hashcode * factor + originClipY;
    hashcode = hashcode * factor + scaledClipWidth;
    hashcode = hashcode * factor + scaledClipHeight;
    hashcode = hashcode * factor + scaledClipX;
    hashcode = hashcode * factor + scaledClipY;
    hashcode = hashcode * factor + scaledWidth;
    hashcode = hashcode * factor + scaledHeight;
    hashcode = hashcode * factor + outWidth;
    hashcode = hashcode * factor + outHeight;
    hashcode = hashcode * factor + outX;
    hashcode = hashcode * factor + outY;
    hashcode = (int) (hashcode * factor + alpha * 255);
    hashcode = hashcode * factor + videoIndex;
    if (source != null) {
      hashcode = hashcode * factor + source.hashCode();
    }
    hashcode = hashcode * factor + splitIndex;
    return hashcode;
  }
}
