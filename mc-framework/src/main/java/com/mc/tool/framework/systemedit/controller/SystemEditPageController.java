package com.mc.tool.framework.systemedit.controller;

import com.mc.common.util.WeakAdapter;
import com.mc.common.util.WeakBinder;
import com.mc.graph.McGraph;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.graph.interfaces.GraphObject;
import com.mc.graph.util.SelectableNode;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.systemedit.control.PreviewPopover;
import com.mc.tool.framework.systemedit.datamodel.DefaultVisualEditMatrix;
import com.mc.tool.framework.systemedit.datamodel.DefaultVisualEditTerminal;
import com.mc.tool.framework.systemedit.datamodel.NodeUtility;
import com.mc.tool.framework.systemedit.datamodel.VisualEditConnection;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditMatrix;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.graph.SystemEditGraph;
import com.mc.tool.framework.systemedit.menu.MenuCrossScreen;
import com.mc.tool.framework.systemedit.menu.MenuDelete;
import com.mc.tool.framework.systemedit.menu.MenuDeviceType;
import com.mc.tool.framework.systemedit.menu.MenuGroup;
import com.mc.tool.framework.systemedit.menu.MenuSeat;
import com.mc.tool.framework.systemedit.menu.MenuVideoWall;
import com.mc.tool.framework.systemedit.property.PropertyItem;
import com.mc.tool.framework.systemedit.property.PropertySheet;
import com.mc.tool.framework.systemedit.view.SystemeditConstants;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import com.mc.tool.framework.utility.AggregatedObservableArrayList;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.TypeWrapper;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.ResourceBundle;
import javafx.beans.InvalidationListener;
import javafx.beans.binding.Bindings;
import javafx.beans.binding.ObjectBinding;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.event.ActionEvent;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.HPos;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.geometry.Side;
import javafx.geometry.VPos;
import javafx.scene.control.Button;
import javafx.scene.control.CheckMenuItem;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Label;
import javafx.scene.control.TextField;
import javafx.scene.control.ToggleButton;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.input.KeyEvent;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.ColumnConstraints;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.Priority;
import javafx.scene.layout.RowConstraints;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import lombok.Getter;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.AbstractPropertyItem;
import org.controlsfx.control.ExPropertyEditorFactory;
import org.controlsfx.control.MasterDetailPane;
import org.controlsfx.control.PropertySheet.Item;
import org.controlsfx.control.textfield.TextFields;
import org.joor.Reflect;

/**
 * .
 */
@Slf4j
public class SystemEditPageController implements Initializable, SystemEditControllable {

  @FXML
  protected VBox graphContainer;
  @FXML
  protected ToggleButton previewButton;
  @FXML
  protected Button zoominBtn;
  @FXML
  protected Button zoomoutBtn;
  @FXML
  protected Button switchBtn;
  @FXML
  protected org.controlsfx.control.PropertySheet newPropertySheet;
  @FXML
  protected MasterDetailPane masterDetailPane;
  @FXML
  protected GridPane iconGridPane;
  @FXML
  protected VBox showBtnContainer;
  @FXML
  protected TextField searchText;
  @FXML
  protected ToggleButton searchBtn;
  @FXML
  protected VBox actionsBox;
  @FXML
  protected VBox actionsGridPaneContainer;

  protected AggregatedObservableArrayList<Item> aggregatedProperties =
      new AggregatedObservableArrayList<>("aggregatedProperties");

  protected ObservableList<Item> properties = aggregatedProperties.getAggregatedList();

  protected AggregatedObservableArrayList<TypeWrapper> aggregatedNodeActions =
      new AggregatedObservableArrayList<>("aggregatedNodeActions");

  protected ObservableList<TypeWrapper> nodeActions = aggregatedNodeActions.getAggregatedList();

  protected VisualEditModel systemEditModel;

  protected ContextMenu menu = new ContextMenu();

  protected List<GraphObject> mainHighLighter = new ArrayList<>();

  protected PreviewPopover popOver = null;

  protected BooleanProperty onlineEdit = new SimpleBooleanProperty(true);

  protected WeakAdapter weakAdapter = new WeakAdapter();

  protected WeakBinder weakBinder = new WeakBinder();

  protected ObjectProperty<GraphWrapper> currentGraph = new SimpleObjectProperty<>();

  protected ObservableList<GraphWrapper> graphWrappers = FXCollections.observableArrayList();

  protected NodeSearcher nodeSearcher;

  @Override
  public void initModel(VisualEditModel model) {
    systemEditModel = model;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    initImpl();

    testData();
  }

  /**
   * 创建测试数据.
   *
   * @param model 存储测试数据的datamodel
   */
  public static void createTestData(VisualEditModel model) {
    DefaultVisualEditMatrix matrix = new DefaultVisualEditMatrix();
    matrix.init();

    VisualEditTerminal t1 = new DefaultVisualEditTerminal(true);
    t1.init();
    VisualEditTerminal t2 = new DefaultVisualEditTerminal(true);
    t2.init();
    VisualEditTerminal t3 = new DefaultVisualEditTerminal(true);
    t3.init();
    VisualEditTerminal t4 = new DefaultVisualEditTerminal(true);
    t4.init();
    VisualEditTerminal t5 = new DefaultVisualEditTerminal(true);
    t5.init();
    VisualEditTerminal t6 = new DefaultVisualEditTerminal(true);
    t6.init();
    VisualEditTerminal t7 = new DefaultVisualEditTerminal(false);
    t7.init();

    matrix.insertTerminal(t1, 2, 1);
    matrix.insertTerminal(t1, 3, 2);
    matrix.insertTerminal(t2, 4, 1);
    matrix.insertTerminal(t3, 6, 1);
    matrix.insertTerminal(t4, 8, 1);
    matrix.insertTerminal(t5, 10, 1);
    matrix.insertTerminal(t6, 12, 1);
    matrix.insertTerminal(t7, 11, 1);
    VisualEditGroup group = new VisualEditGroup();
    group.init();
    group.addChildren(t1, t2);
    VisualEditGroup func = new VisualEditGroup();
    func.init();
    func.addChildren(t3, t4);
    group.addChildren(func);
    matrix.insertGroup(-1, group);
    model.addItem(matrix);

    // DefaultVisualEditMatrix matrix2 = new DefaultVisualEditMatrix();
    // matrix2.init();
    // VisualEditTerminal y1 = new DefaultVisualEditTerminal(false);
    // y1.init();
    // VisualEditTerminal y2 = new DefaultVisualEditTerminal(false);
    // y2.init();
    // VisualEditTerminal y3 = new DefaultVisualEditTerminal(true);
    // y3.init();
    // VisualEditTerminal y4 = new DefaultVisualEditTerminal(false);
    // y4.init();
    // VisualEditTerminal y5 = new DefaultVisualEditTerminal(true);
    // y5.init();
    // VisualEditTerminal y6 = new DefaultVisualEditTerminal(true);
    // y6.init();
    // VisualEditTerminal y7 = new DefaultVisualEditTerminal(false);
    // y7.init();
    // matrix2.insertTerminal(y1, 2, 1);
    // matrix2.insertTerminal(y2, 4, 1);
    // matrix2.insertTerminal(y3, 6, 1);
    // matrix2.insertTerminal(y4, 8, 1);
    // matrix2.insertTerminal(y5, 10, 1);
    // matrix2.insertTerminal(y6, 12, 1);
    // matrix2.insertTerminal(y7, 11, 1);
    // matrix2.insertTerminal(t7, 15, 2);

    // model.addItem(matrix2);
  }

  private void testData() {
    if (systemEditModel.getRoots().size() == 0) {
      createTestData(systemEditModel);
    }
    updateModelToGraph();
  }

  protected void highLightSelections() {
    setHighLightLinks(getSelectedNodes().toArray(new VisualEditNode[0]));
  }

  protected SystemEditMatrixWrapper createSystemEditMatrixWrapper() {
    return new SystemEditMatrixWrapper(systemEditModel, createSystemEditGraph(), onlineEdit);
  }

  protected void initImpl() {
    currentGraph.set(createSystemEditMatrixWrapper());
    graphWrappers.add(currentGraph.get());

    systemEditModel.getConnections().addListener(weakAdapter
        .wrap((ListChangeListener<VisualEditConnection>) change -> highLightSelections()));
    // 初始化预览
    popOver = new PreviewPopover();

    previewButton.selectedProperty()
        .addListener(weakAdapter.wrap((observable, oldValue, newValue) -> {
          if (newValue) {
            popOver.show(previewButton);
          } else {
            popOver.hide();
          }
        }));

    popOver.setOnHidden((event) -> previewButton.setSelected(false));
    popOver.setOnShown((event) -> previewButton.setSelected(true));

    weakBinder.bind(showBtnContainer.managedProperty(), showBtnContainer.visibleProperty());
    weakBinder
        .bind(showBtnContainer.visibleProperty(), masterDetailPane.showDetailNodeProperty().not());

    //切换按钮
    switchBtn.managedProperty().bind(switchBtn.visibleProperty());
    switchBtn.visibleProperty().bind(Bindings.size(graphWrappers).greaterThan(1));

    // 属性框
    newPropertySheet.setPropertyEditorFactory(new ExPropertyEditorFactory());
    // Evil trick to set grid line color!!
    Reflect.on(GridPane.class).set("GRID_LINE_COLOR", Color.rgb(0xcc, 0xcc, 0xcc));
    newPropertySheet.setModeSwitcherVisible(false);
    newPropertySheet.setSearchBoxVisible(false);
    properties.addListener(weakAdapter.wrap((ListChangeListener<Item>) change -> {
      // 切换时删除
      for (Item item : newPropertySheet.getItems()) {
        if (item instanceof AbstractPropertyItem) {
          ((AbstractPropertyItem) item).delete();
        }
      }
      //
      newPropertySheet.getItems().setAll(aggregatedProperties.getAggregatedList());
    }));

    //操作列表
    weakBinder.bind(actionsBox.managedProperty(), actionsBox.visibleProperty());
    weakBinder.bind(actionsBox.visibleProperty(),
        Bindings.createBooleanBinding(() -> nodeActions.size() > 0, nodeActions));

    nodeActions.addListener(
        weakAdapter.wrap((ListChangeListener<TypeWrapper>) change -> updateActionsGridPane()));

    // 图标列表
    initIconGrid();

    // 搜索
    weakBinder.bind(searchText.managedProperty(), searchText.visibleProperty());
    weakBinder.bind(searchText.visibleProperty(), searchBtn.selectedProperty());

    nodeSearcher = createNodeSearcher();
    TextFields.bindAutoCompletion(searchText, nodeSearcher.createSearchSuggestion(),
        nodeSearcher.createNodeStringConverter());
    searchText.setOnKeyPressed(weakAdapter.wrap(new EventHandler<KeyEvent>() {
      @Override
      public void handle(KeyEvent key) {
        switch (key.getCode()) {
          case ENTER:
            Collection<VisualEditNode> result = nodeSearcher.search(searchText.getText(), false);
            if (result.size() > 0) {
              log.info("Node finded : {}!", searchText.getText());
              selectAndCenterNode(result.iterator().next());
            } else {
              log.info("Fail to find node :{}！", searchText.getText());
            }
            break;
          default:
            break;
        }
      }
    }));

    attachGraph(currentGraph.get());

    currentGraph.addListener((observable, oldValue, newValue) -> {
      detachGraph(oldValue);
      attachGraph(newValue);
      updateProperties(newValue.getGraph());
      updateButtonStatus(newValue.getGraph());
    });

    graphWrappers.addListener((ListChangeListener<GraphWrapper>) (change) -> {
      //如果当前matrix被删掉，重新选择一个作为当前
      if (!graphWrappers.contains(currentGraph.get()) && graphWrappers.size() > 0) {
        currentGraph.set(graphWrappers.get(0));
      }
    });
  }

  protected void updateProperties(McGraph graph) {
    if (graph.getSelectionModel().getSelectedItems().isEmpty()) {
      aggregatedProperties.clearList();
      aggregatedNodeActions.clearList();
    } else {
      SelectableNode node = graph.getSelectionModel().getSelectedItems().get(0);
      VisualEditNode visualEditNode = NodeUtility.selectedNode2VisualEditNode(node);
      aggregatedProperties.clearList();
      aggregatedProperties.appendList(createNewProperties(visualEditNode));

      aggregatedNodeActions.clearList();
      aggregatedNodeActions.appendList(createNodeActions(visualEditNode));
    }
  }

  protected void updateButtonStatus(McGraph graph) {
    zoominBtn.disableProperty()
        .set(graph.getCanvas().getScaleProperty().get() >= SystemeditConstants.MAX_SCALE);
    zoomoutBtn.disableProperty()
        .set(graph.getCanvas().getScaleProperty().get() <= SystemeditConstants.MIN_SCALE);
  }

  protected void attachGraph(GraphWrapper graphWrapper) {
    if (graphWrapper.isAttached()) {
      return;
    }
    graphWrapper.attach();
    McGraph graph = graphWrapper.getGraph();
    graphContainer.getChildren().add(graph.getCanvas().getNode());
    VBox.setVgrow(graph.getCanvas().getNode(), Priority.ALWAYS);
    popOver.setContentNode(graph.getOverviewCanvas());

    ListChangeListener<SelectableNode> selectChangeListener = change -> {
      highLightSelections();
      updateProperties(graph);
    };

    // 初始化右键菜单
    EventHandler<MouseEvent> clickHandler = (event) -> {
      if (event.getButton() == MouseButton.SECONDARY && event.getClickCount() == 1) {
        menu.getItems().clear();
        createContextMenuItems(menu);
        menu.show(graph.getCanvas().getContainer(), event.getScreenX(), event.getScreenY());
      } else {
        menu.hide();
      }
    };

    InvalidationListener scaleInvalidationListener = (change) -> updateButtonStatus(graph);

    graphWrapper.attachSelectChangeListener(selectChangeListener);
    graphWrapper.attachClickEventFilter(clickHandler);
    graphWrapper.attachScaleChangeListener(scaleInvalidationListener);
  }

  protected void detachGraph(GraphWrapper graphWrapper) {
    McGraph graph = graphWrapper.getGraph();
    graphContainer.getChildren().remove(graph.getCanvas().getNode());
    popOver.setContentNode(null);
    graphWrapper.detach();
  }

  protected SystemEditGraph createSystemEditGraph() {
    return new SystemEditGraph(this);
  }

  protected void updateActionsGridPane() {
    if (getGraph().getSelectionModel().getSelectedItems().isEmpty()) {
      return;
    }
    ColumnConstraints col1 = new ColumnConstraints();
    col1.setHalignment(HPos.LEFT);
    col1.setPercentWidth(50);
    ColumnConstraints col2 = new ColumnConstraints();
    col2.setPercentWidth(50);
    col2.setHalignment(HPos.LEFT);

    GridPane gridPane = new GridPane();
    gridPane.getColumnConstraints().addAll(col1, col2);
    gridPane.setGridLinesVisible(true);
    gridPane.setAlignment(Pos.CENTER);

    int index = 0;
    for (TypeWrapper wrapper : nodeActions) {
      Button button = new Button(wrapper.getName());
      button.getStyleClass().add("common-button");
      SelectableNode node = getGraph().getSelectionModel().getSelectedItems().get(0);
      VisualEditNode visualEditNode = NodeUtility.selectedNode2VisualEditNode(node);
      button.setOnAction((event) -> onNodeAction(visualEditNode, wrapper));

      VBox tempVbox = new VBox();
      tempVbox.getChildren().add(button);
      tempVbox.setPadding(new Insets(0, 0, 0, 12));
      tempVbox.setAlignment(Pos.CENTER_LEFT);

      gridPane.add(tempVbox, index % 2, index / 2);

      if (index % 2 == 0) {
        RowConstraints rowConstraints = new RowConstraints();
        rowConstraints.setPrefHeight(33);
        rowConstraints.setValignment(VPos.CENTER);
        gridPane.getRowConstraints().add(rowConstraints);
      }
      index++;

    }

    actionsGridPaneContainer.getChildren().clear();
    actionsGridPaneContainer.getChildren().add(gridPane);
  }

  protected void initIconGrid() {
    ColumnConstraints col1 = new ColumnConstraints();
    col1.setPercentWidth(50);
    ColumnConstraints col2 = new ColumnConstraints();
    col2.setPercentWidth(50);
    iconGridPane.getColumnConstraints().addAll(col1, col2);
    String[] iconPaths = new String[] {SystemeditConstants.MONITOR_ONLINE_LOGO,
        SystemeditConstants.PROJECTOR_ONLINE_LOGO, SystemeditConstants.COMPUTER_ONLINE_LOGO,
        SystemeditConstants.DVD_ONLINE_LOGO, SystemeditConstants.HDMI_LOGO,
        SystemeditConstants.DP_LOGO, SystemeditConstants.DVI_LOGO, SystemeditConstants.USB_LOGO};
    String[] iconNames =
        new String[] {I18nUtility.getI18nBundle("systemedit").getString("device_type.monitor"),
            I18nUtility.getI18nBundle("systemedit").getString("device_type.projector"),
            I18nUtility.getI18nBundle("systemedit").getString("device_type.computer"),
            I18nUtility.getI18nBundle("systemedit").getString("device_type.dvd"), "HDMI", "DP",
            "DVI", "USB"};

    int count = iconPaths.length;
    assert iconPaths.length == iconNames.length;
    final int column_count = 2;
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    for (int i = 0; i < count; i++) {
      Label label = new Label();
      label.setText(iconNames[i]);
      Image image = new Image(
          classLoader.getResourceAsStream(SystemeditConstants.RESOURCE_PATH_SHORT + iconPaths[i]));
      label.setGraphic(new ImageView(image));
      iconGridPane.add(label, i % column_count, i / column_count);
    }
  }

  protected ObservableList<Item> createNewProperties(VisualEditNode node) {
    if (node != null) {
      return node.getProperties();
    } else {
      return FXCollections.observableArrayList();
    }
  }

  protected ObservableList<TypeWrapper> createNodeActions(VisualEditNode node) {
    return FXCollections.observableArrayList();
  }

  protected void onNodeAction(VisualEditNode node, TypeWrapper wrapper) {

  }

  protected ObjectBinding<PropertySheet> createPropertiesBinding(VisualEditNode node) {
    return new DefaultPropertyBinding(node);
  }

  protected void createContextMenuItems(ContextMenu menu) {
    if (getSelectedNodes().size() == 0) {
      return;
    }
    menu.getItems().add(new MenuDelete(this));
    menu.getItems().add(new MenuGroup(this));
    menu.getItems().add(new MenuVideoWall(this));
    menu.getItems().add(new MenuSeat(this));
    menu.getItems().add(new MenuCrossScreen(this));
    menu.getItems().add(new MenuDeviceType(this));
  }

  @Override
  public boolean isNodeVisible(VisualEditNode node) {
    if (node instanceof VisualEditTerminal) {
      VisualEditTerminal terminal = (VisualEditTerminal) node;
      return !isOnlineEdit() || terminal.isOnline();
    } else if (node instanceof VisualEditGroup) {
      VisualEditGroup group = (VisualEditGroup) node;
      // 如果有一个或多个子节点visible，那么显示，否则不显示
      for (VisualEditTerminal terminal : group.getAllTerminalChild()) {
        if (isNodeVisible(terminal)) {
          return true;
        }
      }
      return false;
    } else {
      return true;
    }
  }

  @Override
  public NodeSearcher createNodeSearcher() {
    return new NodeNameSearcher(systemEditModel.getRoots());
  }

  @Override
  public <T extends VisualEditGroup> T addGroup(String groupName, Class<T> groupClazz,
                                                VisualEditNode... nodes) {
    T newGroup = systemEditModel.addGroup(groupName, groupClazz, nodes);
    if (newGroup != null) {
      updateModelToGraph();
    }
    return newGroup;
  }

  /**
   * 删除一个组.
   *
   * @param group        要删除的组
   * @param needToUpdate 是否需要更新界面
   */
  @Override
  public void deleteGroup(VisualEditGroup group, boolean needToUpdate) {
    if (!systemEditModel.deleteGroup(group)) {
      return;
    }
    if (needToUpdate) {
      updateModelToGraph();
    }
  }

  @Override
  public void deleteTerminal(VisualEditTerminal terminal, boolean needToUpdate) {
    if (!systemEditModel.deleteTerminal(terminal)) {
      return;
    }
    if (needToUpdate) {
      updateModelToGraph();
    }
  }

  @Override
  public VisualEditMatrix findMatrix(VisualEditNode node) {
    while (node != null && !(node instanceof VisualEditMatrix)) {
      node = node.getParent();
    }
    return (VisualEditMatrix) node;
  }

  static class LinkInfo {

    @Getter
    private Object parentConnector;
    @Getter
    private Object childConnector;
    @Getter
    private Collection<Object> relatedMatrixConnector;

    public LinkInfo(Object parentConnector, Object childConnector,
                    Object... relatedMatrixConnector) {
      this.parentConnector = parentConnector;
      this.childConnector = childConnector;
      this.relatedMatrixConnector = Arrays.asList(relatedMatrixConnector);
    }
  }

  static class DefaultPropertyBinding extends ObjectBinding<PropertySheet> {

    private VisualEditNode node;

    public DefaultPropertyBinding(VisualEditNode node) {
      super.bind(node.nameProperty());
      this.node = node;
    }

    @Override
    protected PropertySheet computeValue() {
      PropertySheet propertySheet = new PropertySheet();
      propertySheet.setName(node.getName());
      propertySheet.setLogoUrl(ViewUtility.getLogoUrl(node));
      String nameTitle = I18nUtility.getI18nBundle("systemedit").getString("property.name");
      PropertyItem<String> nameItem = new PropertyItem<>(nameTitle, node.getName());
      List<String> childrenName = new ArrayList<>();
      for (VisualEditNode child : node.getChildren()) {
        childrenName.add(child.getName());
      }
      PropertyItem<String> children =
          new PropertyItem<>("children:", childrenName.toArray(new String[0]));
      propertySheet.getProperties().add(nameItem);
      propertySheet.getProperties().add(children);
      return propertySheet;
    }
  }

  @Override
  public void setHighLighter(GraphObject... graphObjects) {
    if (mainHighLighter != null) {
      for (GraphObject object : mainHighLighter) {
        object.highLightProperty().set(false);
      }
    }
    mainHighLighter = Arrays.asList(graphObjects);
    for (GraphObject object : mainHighLighter) {
      object.highLightProperty().set(true);
    }
  }

  @Override
  public boolean isOnlineEdit() {
    return onlineEdit.get();
  }

  @Override
  public boolean isConnectable(@NonNull VisualEditTerminal rx, @NonNull Object rxConnector,
                               @NonNull VisualEditTerminal tx, @NonNull Object txConnector) {
    if (getDeviceController() != null && !getDeviceController().getUserRight()
        .isDeviceConnectable()) {
      return false;
    }
    // 不允许功能组做连接操作
    if (rx.getParent() instanceof VisualEditFunc) {
      return false;
    }
    return true;
  }

  @Override
  public boolean isLinkable(VisualEditNode first, Object firstConnector, VisualEditNode second,
                            Object secondConnector) {
    return false;
  }

  @Override
  public boolean isDeletable(VisualEditNode node) {
    if (node instanceof VisualEditGroup) {
      return true;
    } else {
      return !isOnlineEdit();
    }
  }

  @Override
  public boolean isFixDeviceType(VisualEditNode node) {
    return false;
  }

  @Override
  public void deleteNodes(VisualEditNode... nodes) {
    for (VisualEditNode node : nodes) {
      if (!isDeletable(node)) {
        continue;
      }
      if (node instanceof VisualEditGroup) {
        deleteGroup((VisualEditGroup) node, false);
      } else if (node instanceof VisualEditTerminal) {
        deleteTerminal((VisualEditTerminal) node, false);
      } else {
        log.warn("Not supported yet!");
      }
    }
    updateModelToGraph();
  }

  @Override
  public void connect(VisualEditTerminal tx, ConnectorIdentifier txConnector, VisualEditTerminal rx,
                      ConnectorIdentifier rxConnector, String mode, int rxChannel) {
    systemEditModel.addConnection(
        new VisualEditConnection(tx, txConnector, rx, rxConnector, mode, rxChannel));
  }

  @Override
  public void setHighLightLinks(VisualEditNode... nodes) {
    Collection<GraphObject> highLightList =
        SystemEditHighLightHelper.getHighLightGraphs(this, systemEditModel, nodes);
    setHighLighter(highLightList.toArray(new GraphObject[0]));
  }

  @Override
  public Collection<Color> getConnectionModeColor(String mode) {
    return Collections.singletonList(Color.RED);
  }

  @Override
  public Collection<TypeWrapper> getConnectableType(VisualEditTerminal rx, Object rxConnector,
                                                    VisualEditTerminal tx, Object txConnector) {
    List<TypeWrapper> types = new ArrayList<>();
    types.add(new TypeWrapper("Full", "full", ""));
    types.add(new TypeWrapper("Video", "video", ""));
    types.add(new TypeWrapper("Private", "private", ""));
    return types;
  }

  @Override
  public Collection<TypeWrapper> getMultiviewChannel(VisualEditTerminal rx, VisualEditTerminal tx) {
    return Collections.emptyList();
  }

  @Override
  public McGraph getGraph() {
    return currentGraph.get().getGraph();
  }

  @Override
  public Collection<VisualEditNode> getSelectedNodes() {
    List<VisualEditNode> result = new ArrayList<>();
    for (SelectableNode node : getGraph().getSelectionModel().getSelectedItems()) {
      VisualEditNode visualEditNode = NodeUtility.selectedNode2VisualEditNode(node);
      if (visualEditNode != null) {
        result.add(visualEditNode);
      }
    }
    return result;
  }

  @Override
  public void updateModelToGraph() {
    if (currentGraph.get() != null) {
      currentGraph.get().updateModelToGraph();
    }
  }

  protected void updateModelToAllGraph() {
    for (GraphWrapper wrapper : graphWrappers) {
      wrapper.updateModelToGraph();
    }
  }

  protected void clearAllGraph() {
    for (GraphWrapper wrapper : graphWrappers) {
      wrapper.clearGraph();
    }
  }

  @Override
  public boolean isMovable(VisualEditNode from, VisualEditNode to) {
    if (from == null || to == null) {
      return false;
    }
    if (from.getParent() == to) {
      return false;
    }
    // matrix不能作为子节点
    if (from instanceof VisualEditMatrix) {
      return false;
    }
    //func只有一个子节点的话不能移子节点.
    if (from.getParent() instanceof VisualEditFunc && from.getParent().getChildren().size() <= 1) {
      return false;
    }
    if (to instanceof VisualEditMatrix) {
      // to为matrix时from必须是to的子节点
      VisualEditMatrix matrix = (VisualEditMatrix) to;
      return matrix.hasChild(from);
    } else if (to instanceof VisualEditGroup) {
      if (to.isRx() != from.isRx() && to.isTx() != from.isTx()) {
        return false;
      }
      // to与from必须在同一个矩阵
      if (!isRelative(from, to)) {
        return false;
      }
      // func下不能有group
      if (to instanceof VisualEditFunc && from instanceof VisualEditGroup) {
        return false;
      }
      return true;
    } else {
      return false;
    }
  }

  /**
   * 判断两个节点是否有关联.
   *
   * @param first  第一个节点
   * @param second 第二个节点
   * @return 如果有关联，返回true
   */
  protected boolean isRelative(VisualEditNode first, VisualEditNode second) {
    for (VisualEditNode root : systemEditModel.getRoots()) {
      if ((root == first || root.hasChild(first)) && (root == second || root.hasChild(second))) {
        return true;
      }
    }
    return false;
  }

  @Override
  public void moveTo(VisualEditNode from, VisualEditNode to) {
    if (systemEditModel.moveTo(from, to)) {
      updateModelToGraph();
    }
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    // No device controller.
  }

  @FXML
  protected void onZoomin(ActionEvent event) {
    double scale = getGraph().getCanvas().getScaleProperty().get();
    scale = scale * SystemeditConstants.SCALE_FACTOR;
    if (scale > SystemeditConstants.MAX_SCALE) {
      scale = SystemeditConstants.MAX_SCALE;
    }
    getGraph().getCanvas().getScaleProperty().set(scale);
  }

  @FXML
  protected void onZoomout(ActionEvent event) {
    double scale = getGraph().getCanvas().getScaleProperty().get();
    scale = scale / SystemeditConstants.SCALE_FACTOR;
    if (scale < SystemeditConstants.MIN_SCALE) {
      scale = SystemeditConstants.MIN_SCALE;
    }
    getGraph().getCanvas().getScaleProperty().set(scale);
  }

  @FXML
  protected void onRestore(ActionEvent event) {
    getGraph().getCanvas().getScaleProperty().set(1);
    getGraph().getCanvas().fitToView();
  }

  @FXML
  protected void onSwitchMatrix(ActionEvent event) {
    ContextMenu menu = new ContextMenu();
    for (GraphWrapper matrixWrapper : graphWrappers) {
      CheckMenuItem item = new CheckMenuItem();
      item.setOnAction((evt) -> currentGraph.set(matrixWrapper));
      item.setText(matrixWrapper.getName());
      item.setSelected(matrixWrapper == currentGraph.get());
      menu.getItems().add(item);
    }
    menu.show(switchBtn, Side.TOP, 0, 0);
  }

  @FXML
  protected void onHideProperty(ActionEvent event) {
    masterDetailPane.setShowDetailNode(false);
  }

  @FXML
  protected void onShowProperty(ActionEvent event) {
    masterDetailPane.setShowDetailNode(true);
  }

  @FXML
  protected void onRefresh(ActionEvent event) {

  }

  @Override
  public VisualEditModel getModel() {
    return systemEditModel;
  }

  @Override
  public DeviceControllable getDeviceController() {
    return null;
  }

  @Override
  public void close() {
    getGraph().destroy();
  }

  @Override
  public void selectAndCenterNode(VisualEditNode node) {
    if (node != null) {
      CellSkin skin = getGraph().getSkinManager().getCellSkin(node.getCellObject());
      if (skin != null) {
        getGraph().getSelectionModel().deselectAll();
        getGraph().getSelectionModel().select(skin, true);
        getGraph().centerCell(node.getCellObject());
      } else {
        log.warn("Node's skin is null : {}!", node.getName());
      }
    }
  }

  @Override
  public void onEntityActiveChange(boolean active) {
    if (active) {
      attachGraph(currentGraph.get());
    } else {
      detachGraph(currentGraph.get());
    }
  }
}
