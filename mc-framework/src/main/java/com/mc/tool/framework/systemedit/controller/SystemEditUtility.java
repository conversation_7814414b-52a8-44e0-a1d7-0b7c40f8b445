package com.mc.tool.framework.systemedit.controller;

import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellPropertyUpdater;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;

/**
 * .
 */
public class SystemEditUtility {

  /**
   * 获取指定的CelPropertyUpdater.
   *
   * @param cell  cell
   * @param name  updater的名称
   * @param clazz updater的类型
   * @return 如果找到，返回该updater，否则返回null
   */
  public static <T> T getCellPropertyUpdater(CellObject cell, String name, Class<T> clazz) {
    CellPropertyUpdater updater = cell.getPropertyUpdaters().get(name);
    if (clazz.isInstance(updater)) {
      return clazz.cast(updater);
    } else {
      return null;
    }
  }

  /**
   * 节点是否可显示.
   *
   * @param node       节点.
   * @param onlineEdit 在线编辑.
   * @return 如果可显示，返回true
   */
  public static boolean isNodeVisible(VisualEditNode node, boolean onlineEdit) {
    if (node instanceof VisualEditTerminal) {
      VisualEditTerminal terminal = (VisualEditTerminal) node;
      return !onlineEdit || terminal.isOnline();
    } else if (node instanceof VisualEditGroup) {
      VisualEditGroup group = (VisualEditGroup) node;
      // 如果有一个或多个子节点visible，那么显示，否则不显示
      for (VisualEditTerminal terminal : group.getAllTerminalChild()) {
        if (isNodeVisible(terminal, onlineEdit)) {
          return true;
        }
      }
      return false;
    } else {
      return true;
    }
  }
}
