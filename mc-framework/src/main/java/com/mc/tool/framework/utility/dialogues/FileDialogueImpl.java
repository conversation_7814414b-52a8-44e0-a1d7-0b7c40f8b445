package com.mc.tool.framework.utility.dialogues;

import java.io.File;
import javafx.stage.FileChooser;
import javafx.stage.FileChooser.ExtensionFilter;
import javafx.stage.Window;

/**
 * .
 */
public class FileDialogueImpl implements FileDialogue {
  private final FileChooser chooser = new FileChooser();

  @Override
  public void addExtensionFilter(ExtensionFilter filter) {
    chooser.getExtensionFilters().add(filter);
  }

  @Override
  public File showOpenDialog(Window stage) {
    return chooser.showOpenDialog(stage);
  }

  @Override
  public File showSaveDialog(Window stage) {
    return chooser.showSaveDialog(stage);
  }

  @Override
  public void setInitialFileName(String name) {
    chooser.setInitialFileName(name);
  }
}
