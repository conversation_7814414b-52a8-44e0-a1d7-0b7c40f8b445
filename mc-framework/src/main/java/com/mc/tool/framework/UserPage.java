package com.mc.tool.framework;

import com.mc.tool.framework.interfaces.Page;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.user.view.UserPageView;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.scene.layout.Pane;

/**
 * .
 */
public class UserPage implements Page {
  private final UserPageView view;
  private SimpleBooleanProperty visibilityProperty = new SimpleBooleanProperty(true);

  public UserPage(VisualEditModel model) {
    view = new UserPageView(model);
  }

  @Override
  public String getTitle() {
    return "User";
  }

  @Override
  public String getName() {
    return "User";
  }

  @Override
  public Pane getView() {
    return view;
  }

  @Override
  public BooleanProperty getVisibleProperty() {
    return visibilityProperty;
  }

  @Override
  public void showObject(Object object) {
  }

  @Override
  public String getStyleClass() {
    // TODO Auto-generated method stub
    return null;
  }
}
