package com.mc.tool.framework.operation.videowall.scenario;

import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoWallObject;
import com.mc.tool.framework.utility.I18nUtility;
import java.io.InputStream;
import javafx.beans.property.ReadOnlyStringWrapper;
import javafx.geometry.Pos;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.control.MenuItem;
import javafx.scene.control.Tooltip;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.HBox;

/**
 * .
 */
public class SimpleScenarioCell extends ListCell<VideoWallObject> {

  private final ContextMenu menu = new ContextMenu();

  /**
   * Constructor.
   */
  public SimpleScenarioCell(VideoWallControllable controllable) {
    this.addEventHandler(MouseEvent.MOUSE_CLICKED, (event) -> {
      if (event.getButton() == MouseButton.PRIMARY && event.getClickCount() == 2) {
        boolean activatable =
            controllable.getDeviceController() == null || controllable.getDeviceController()
                .getUserRight().isVideoWallScenarioActivatable(controllable.getVideoWallFunction());
        if (activatable) {
          controllable.activeScenario(getItem());
        }
      } else if (event.getButton() == MouseButton.SECONDARY && event.getClickCount() == 1) {
        if (menu.isShowing()) {
          menu.hide();
        } else {
          menu.getItems().clear();
          boolean deletable =
              controllable.getDeviceController() == null || controllable.getDeviceController()
                  .getUserRight()
                  .isVideoWallScenarioCreateDeletable(controllable.getVideoWallFunction());
          if (deletable) {
            MenuItem item = new MenuItem();
            item.setText(I18nUtility.getI18nBundle("operation").getString("menu.delete"));
            item.setDisable(!controllable.hasScenario(getItem()));
            item.setOnAction((menuEvent) -> controllable.deleteScenario(getItem()));
            menu.getItems().add(item);
          }
          menu.show(this.getScene().getWindow(), event.getScreenX(), event.getScreenY());
        }
      }
    });
  }

  @Override
  protected void updateItem(VideoWallObject item, boolean empty) {
    super.updateItem(item, empty);
    if (empty) {
      setGraphic(null);
    } else {
      HBox root = new HBox();
      root.setSpacing(5);
      root.setAlignment(Pos.CENTER_LEFT);
      InputStream resource = SimpleScenarioCell.class.getResourceAsStream(
          "/com/mc/tool/framework/operation/videowall/scenario/target_type_computer_offline.png");
      ImageView view = new ImageView(new Image(resource));
      Label scenarioNameLabel = new Label();
      root.getChildren().addAll(view, scenarioNameLabel);
      if (item.hasIndex()) {
        scenarioNameLabel.textProperty().bind(
            new ReadOnlyStringWrapper(String.format("[%02d] ", item.getIndex()))
                .concat(item.getName()));
      } else {
        scenarioNameLabel.textProperty().bind(item.getName());
      }
      Tooltip tooltip = new Tooltip();
      tooltip.textProperty().bind(scenarioNameLabel.textProperty());
      setTooltip(tooltip);
      setGraphic(root);
    }
  }
}
