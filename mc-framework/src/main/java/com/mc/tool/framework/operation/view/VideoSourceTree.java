package com.mc.tool.framework.operation.view;

import com.mc.tool.framework.systemedit.datamodel.EmptyVisualEditTerminal;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditMatrix;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import javafx.beans.InvalidationListener;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.scene.control.TreeItem;
import javafx.scene.control.TreeView;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class VideoSourceTree extends TreeView<VisualEditNode> {

  protected SourceTreeItem sourceTreeRoot;
  protected Function<Void, Void> updateTreeFunction = null;
  protected ObservableList<VisualEditTerminal> emptyVideoSource =
      FXCollections.observableArrayList(new EmptyVisualEditTerminal("Empty", false));

  protected VisualEditModel model;
  protected ObjectProperty<VisualEditFunc> func = new SimpleObjectProperty<>();

  protected InvalidationListener listener;
  protected SourceMode mode = SourceMode.CURRENT;
  protected Predicate<VisualEditTerminal> terminalFilter;

  /**
   * 信号源树视图.
   */
  public VideoSourceTree() {
    setCellFactory((view) -> new VideoSourceTreeCell());
    setRoot(sourceTreeRoot);
    setShowRoot(false);
    managedProperty().bind(visibleProperty());

    updateTreeFunction = (param) -> {
      updateSourceTree(func.get());
      return null;
    };

    sourceTreeRoot = new SourceTreeItem(null, updateTreeFunction);

    listener = (change) -> updateSourceTree(func.get());
  }

  /**
   * 初始化.
   *
   * @param model       model
   * @param currentFunc func
   */
  public void init(VisualEditModel model, ObjectProperty<VisualEditFunc> currentFunc,
                   SourceMode mode, Predicate<VisualEditTerminal> terminalFilter) {
    this.model = model;
    this.func = currentFunc;
    this.mode = mode;
    this.terminalFilter = terminalFilter;

    model.getTerminalStatusObservable()
        .addListener(listener);

    model.getGroupObservable()
        .addListener(listener);

    updateSourceTree(currentFunc.get());
  }

  /**
   * 销毁.
   */
  public void destroy() {
    model.getTerminalStatusObservable().removeListener(listener);
    model.getGroupObservable().removeListener(listener);
    destroyTreeItem(sourceTreeRoot);
  }

  protected void destroyTreeItem(SourceTreeItem item) {
    if (item == null) {
      return;
    }
    item.destroy();
    for (TreeItem<VisualEditNode> child : item.getChildren()) {
      if (child instanceof SourceTreeItem) {
        destroyTreeItem((SourceTreeItem) child);
      }
    }
  }

  protected void updateSourceTree(VisualEditFunc func) {
    if (mode == SourceMode.CURRENT || model.getRoots().size() <= 1) {
      updateSourceTreeCurrent(func);
    } else {
      updateSourceTreeAll();
    }
  }

  private void updateSourceTreeAll() {
    final Set<TreeItem<VisualEditNode>> oldItems = new HashSet<>(getAllTreeItems(sourceTreeRoot));

    if (sourceTreeRoot.getValue() != null) {
      sourceTreeRoot = new SourceTreeItem(null, updateTreeFunction);
    }
    setRoot(sourceTreeRoot);
    Map<VisualEditNode, TreeItem<VisualEditNode>> oldMatrixItems = new HashMap<>();
    for (TreeItem<VisualEditNode> item : sourceTreeRoot.getChildren()) {
      oldMatrixItems.put(item.getValue(), item);
    }

    for (VisualEditNode node : model.getRoots()) {
      TreeItem<VisualEditNode> item = null;
      if (oldMatrixItems.containsKey(node)) {
        item = oldMatrixItems.get(node);
      } else {
        item = new SourceTreeItem(node, updateTreeFunction);
        sourceTreeRoot.getChildren().add(item);
      }
      Set<VisualEditTerminal> visibleTerminals = new HashSet<>(
          node.getAllTerminalChild().filtered((val) -> terminalFilter.test(val)));
      updateTreeItem(node, item, visibleTerminals);
    }

    if (sourceTreeRoot.getChildren().get(0) != null
        && sourceTreeRoot.getChildren().get(0).getValue() != emptyVideoSource.get(0)) {
      sourceTreeRoot.getChildren().add(0,
          new TreeItem<>(emptyVideoSource.get(0)));
    }
    //清除旧节点
    Set<TreeItem<VisualEditNode>> newItems = new HashSet<>(getAllTreeItems(sourceTreeRoot));
    oldItems.removeAll(newItems);
    for (TreeItem<VisualEditNode> item : oldItems) {
      if (item instanceof SourceTreeItem) {
        ((SourceTreeItem) item).destroy();
      }
    }
    refresh();
  }

  private void updateSourceTreeCurrent(VisualEditFunc func) {
    VisualEditNode parent = func;
    if (parent == null) {
      return;
    }
    while (parent.getParent() != null) {
      parent = parent.getParent();
    }
    if (parent == func) {
      log.warn("Function has no parent !");
      return;
    }

    if (parent instanceof VisualEditMatrix) {
      Set<VisualEditTerminal> visibleTerminals =
          new HashSet<>(parent.getAllTerminalChild().filtered((val) -> terminalFilter.test(val)));
      final Set<TreeItem<VisualEditNode>> oldItems = new HashSet<>(getAllTreeItems(sourceTreeRoot));

      if (sourceTreeRoot.getValue() != parent) {
        sourceTreeRoot = new SourceTreeItem(parent, updateTreeFunction);
        setRoot(sourceTreeRoot);
      }
      updateTreeItem(parent, sourceTreeRoot, visibleTerminals);
      sourceTreeRoot.getChildren().add(0,
          new TreeItem<>(emptyVideoSource.get(0)));
      //清除旧节点
      Set<TreeItem<VisualEditNode>> newItems = new HashSet<>(getAllTreeItems(sourceTreeRoot));
      oldItems.removeAll(newItems);
      for (TreeItem<VisualEditNode> item : oldItems) {
        if (item instanceof SourceTreeItem) {
          ((SourceTreeItem) item).destroy();
        }
      }

      refresh();
    }
  }

  protected TreeItem<VisualEditNode> findTreeItem(TreeItem<VisualEditNode> root, VisualEditNode node) {
    if (root == null) {
      return null;
    }
    if (root.getValue() == node) {
      return root;
    }
    for (TreeItem<VisualEditNode> child : root.getChildren()) {
      TreeItem<VisualEditNode> treeItem = findTreeItem(child, node);
      if (treeItem != null) {
        return treeItem;
      }
    }
    return null;
  }

  protected Collection<TreeItem<VisualEditNode>> getAllTreeItems(TreeItem<VisualEditNode> root) {
    List<TreeItem<VisualEditNode>> result = new ArrayList<>();
    if (root == null) {
      return result;
    }
    result.add(root);
    for (TreeItem<VisualEditNode> item : root.getChildren()) {
      result.addAll(getAllTreeItems(item));
    }
    return result;
  }

  protected void updateTreeItem(VisualEditNode nodeParent, TreeItem<VisualEditNode> treeParent,
                                Set<VisualEditTerminal> visibleTerminals) {
    Map<VisualEditNode, TreeItem<VisualEditNode>> existItems = new HashMap<>();
    for (TreeItem<VisualEditNode> child : treeParent.getChildren()) {
      existItems.put(child.getValue(), child);
    }

    List<TreeItem<VisualEditNode>> nodes = new ArrayList<>();
    for (VisualEditNode node : nodeParent.getTxChildren()) {
      if (!isNodeVisible(node, visibleTerminals)) {
        continue;
      }
      TreeItem<VisualEditNode> treeItem = existItems.get(node);
      if (treeItem == null) {
        treeItem = new SourceTreeItem(node, updateTreeFunction);
      }
      nodes.add(treeItem);
      updateTreeItem(node, treeItem, visibleTerminals);
    }
    treeParent.getChildren().setAll(nodes);
  }

  protected boolean isNodeVisible(VisualEditNode node, Set<VisualEditTerminal> visibleTerminals) {
    if (node instanceof VisualEditTerminal) {
      VisualEditTerminal terminal = (VisualEditTerminal) node;
      return visibleTerminals.contains(terminal);
    } else if (node instanceof VisualEditGroup) {
      VisualEditGroup group = (VisualEditGroup) node;
      // 如果有一个或多个子节点visible，那么显示，否则不显示
      for (VisualEditTerminal terminal : group.getAllTerminalChild()) {
        if (isNodeVisible(terminal, visibleTerminals)) {
          return true;
        }
      }
      return false;
    } else {
      return true;
    }
  }

  /**
   * .
   */
  protected static class SourceTreeItem extends TreeItem<VisualEditNode> {
    private ListChangeListener<VisualEditNode> childrenChangeListener;
    private ObservableList<VisualEditNode> children = null;

    public SourceTreeItem(VisualEditNode node, Function<Void, Void> updateFunction) {
      super(node);
      childrenChangeListener = (change) -> {
        updateFunction.apply(null);
      };
      if (getValue() != null) {
        children = getValue().getObservableChildren();
        children.addListener(childrenChangeListener);
      }
    }

    public void destroy() {
      if (children != null) {
        children.removeListener(childrenChangeListener);
      }
    }
  }

  /**
   * .
   */
  public enum SourceMode {
    CURRENT,
    ALL
  }
}
