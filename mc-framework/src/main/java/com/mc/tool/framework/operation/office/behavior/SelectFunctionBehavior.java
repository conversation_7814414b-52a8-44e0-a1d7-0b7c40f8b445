package com.mc.tool.framework.operation.office.behavior;

import com.mc.graph.interfaces.CellBehavior;
import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.tool.framework.office.datamodel.OfficeData;
import com.mc.tool.framework.operation.controller.OperationPageControllable;
import javafx.event.EventHandler;
import javafx.scene.input.MouseEvent;

/**
 * .
 */
public class SelectFunctionBehavior implements CellBehavior {
  private CellSkin skin;
  private OperationPageControllable controllable;
  private EventHandler<MouseEvent> clickHandler;

  /**
   * Constructor.
   *
   * @param controllable controllable
   * @param skin         skin
   */
  public SelectFunctionBehavior(OperationPageControllable controllable, CellSkin skin) {
    this.controllable = controllable;
    this.skin = skin;

    clickHandler = (event) -> {
      CellBindedObject bindedObject = skin.getCell().getBindedObject();
      if (bindedObject instanceof OfficeData) {
        OfficeData officeData = (OfficeData) bindedObject;
        this.controllable.switchToFunction(officeData.getFunc().getGuid());
      }
    };
  }

  @Override
  public void attach() {
    skin.getRegion().addEventHandler(MouseEvent.MOUSE_CLICKED, clickHandler);
  }

  @Override
  public void detach() {
    skin.getRegion().removeEventHandler(MouseEvent.MOUSE_CLICKED, clickHandler);
  }

  @Override
  public void createConnectorBehavior(ConnectorSkin skin) {
  }

}
