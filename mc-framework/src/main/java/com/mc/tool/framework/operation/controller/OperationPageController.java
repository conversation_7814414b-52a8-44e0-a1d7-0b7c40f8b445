package com.mc.tool.framework.operation.controller;

import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.operation.crossscreen.view.CrossScreenOperationView;
import com.mc.tool.framework.operation.interfaces.OperationView;
import com.mc.tool.framework.operation.office.OfficeWindow;
import com.mc.tool.framework.operation.seat.view.SeatOperationView;
import com.mc.tool.framework.operation.videowall.view.VideoWallOperationView;
import com.mc.tool.framework.operation.view.VideoSourceListCellWithImage;
import com.mc.tool.framework.operation.view.VideoSourceTreeCell;
import com.mc.tool.framework.systemedit.datamodel.CrossScreenFunc;
import com.mc.tool.framework.systemedit.datamodel.DefaultSeatFunc;
import com.mc.tool.framework.systemedit.datamodel.EmptyVisualEditTerminal;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditMatrix;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.AggregatedObservableArrayList;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.function.Function;
import javafx.beans.Observable;
import javafx.beans.binding.Bindings;
import javafx.beans.binding.BooleanBinding;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Bounds;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.ListView;
import javafx.scene.control.ToggleButton;
import javafx.scene.control.TreeItem;
import javafx.scene.control.TreeView;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class OperationPageController implements Initializable, OperationPageControllable {
  @FXML
  protected Label functionTitle;
  @FXML
  protected StackPane functionView;
  @FXML
  protected ListView<VisualEditTerminal> sourceList;
  @FXML
  protected TreeView<VisualEditNode> sourceTree;
  @FXML
  protected Label leftArrow;
  @FXML
  protected Label rightArrow;
  @FXML
  protected Button saveAsScenarioBtn;
  @FXML
  protected Button saveScenarioBtn;
  @FXML
  protected Button configBtn;
  @FXML
  protected ToggleButton sourceListStyleBtn;
  @FXML
  protected VBox videoPreviewContainer;

  protected ObjectProperty<VisualEditTerminal> previewVideo = new SimpleObjectProperty<>();

  protected TreeItem<VisualEditNode> sourceTreeRoot;

  protected AggregatedObservableArrayList<VisualEditTerminal> videoSources =
      new AggregatedObservableArrayList<>("videoSources");

  protected ObservableList<VisualEditTerminal> emptyVideoSource =
      FXCollections.observableArrayList(new EmptyVisualEditTerminal("Empty", false));

  protected Map<VisualEditFunc, OperationView> views = new HashMap<>();

  protected ObjectProperty<VisualEditFunc> currentFunc = new SimpleObjectProperty<>();

  protected VisualEditModel systemEditModel = null;

  protected DeviceControllable deviceControllable = null;

  protected Function<Void, Void> updateTreeFunction = null;

  public void initModel(VisualEditModel model) {
    systemEditModel = model;
  }

  protected OperationView createFuncView(VisualEditFunc func) {
    if (func.getNodeType().equals(SystemEditDefinition.VIDEO_WALL_CELL)) {
      VideoWallOperationView video = new VideoWallOperationView(systemEditModel, func);
      video.getControllable().setDeviceController(deviceControllable);
      return video;
    } else if (func instanceof DefaultSeatFunc) {
      SeatOperationView seat = new SeatOperationView(systemEditModel, func);
      seat.getOperationControllable().setDeviceController(deviceControllable);
      return seat;
    } else if (func instanceof CrossScreenFunc) {
      CrossScreenOperationView crossScreen = new CrossScreenOperationView(systemEditModel, func);
      crossScreen.getOperationControllable().setDeviceController(deviceControllable);
      return crossScreen;
    } else {
      log.warn("Unsupported function : {}", func.getClass().getName());
      return null;
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    sourceList.setCellFactory((gridview) -> {
      return new VideoSourceListCellWithImage(systemEditModel);
    });
    sourceList.setFixedCellSize(130);
    sourceList.managedProperty().bind(sourceList.visibleProperty());
    sourceList.setVisible(false);

    sourceListStyleBtn.selectedProperty().addListener(new ChangeListener<Boolean>() {

      @Override
      public void changed(ObservableValue<? extends Boolean> observable, Boolean oldValue,
                          Boolean newValue) {
        if (newValue) {
          sourceList.setVisible(false);
          sourceTree.setVisible(true && getCurrentFunc() != null);
          VisualEditTerminal selectedTerminal = sourceList.getSelectionModel().getSelectedItem();
          TreeItem<VisualEditNode> node = findTreeItem(sourceTree.getRoot(), selectedTerminal);
          if (node != null) {
            sourceTree.getSelectionModel().select(node);
            sourceTree.scrollTo(sourceTree.getSelectionModel().getSelectedIndex());
          } else {
            sourceTree.getSelectionModel().select(null);
          }
        } else {
          sourceList.setVisible(true && getCurrentFunc() != null);
          sourceTree.setVisible(false);

          TreeItem<VisualEditNode> treeItem = sourceTree.getSelectionModel().getSelectedItem();
          if (treeItem == null || !(treeItem.getValue() instanceof VisualEditTerminal)) {
            sourceList.getSelectionModel().select(null);
          } else {
            sourceList.getSelectionModel().select((VisualEditTerminal) treeItem.getValue());
            sourceList.scrollTo(sourceList.getSelectionModel().getSelectedIndex());
          }
        }
      }
    });

    videoSources.appendList(emptyVideoSource);
    sourceList.setItems(videoSources.getAggregatedList());

    sourceTree.setCellFactory((view) -> new VideoSourceTreeCell());
    sourceTreeRoot = new TreeItem<>();
    sourceTree.setRoot(sourceTreeRoot);
    sourceTree.setShowRoot(false);
    sourceTree.managedProperty().bind(sourceTree.visibleProperty());
    sourceTree.setVisible(false);

    updateTreeFunction = (param) -> {
      updateSourceTree(getCurrentFunc());
      return null;
    };

    systemEditModel.getTerminalStatusObservable()
        .addListener((change) -> updateSourceTree(getCurrentFunc()));

    systemEditModel.getGroupObservable()
        .addListener((change) -> updateSourceTree(getCurrentFunc()));

    currentFunc.addListener((observable, oldVal, newVal) -> {
      functionView.getChildren().clear();
      if (newVal != null) {
        sourceList.setVisible(!sourceListStyleBtn.isSelected());
        sourceTree.setVisible(sourceListStyleBtn.isSelected());
        functionView.getChildren().add(getOperationView(newVal).getView());
        updateVideoSourceList(newVal);
        functionTitle.textProperty().bind(newVal.nameProperty());
      } else {
        functionTitle.textProperty().unbind();
        functionTitle.setText("");

        sourceList.setVisible(false);
        sourceTree.setVisible(false);
      }

    });

    saveScenarioBtn.managedProperty().bind(saveScenarioBtn.visibleProperty());
    saveAsScenarioBtn.managedProperty().bind(saveAsScenarioBtn.visibleProperty());
    updateSaveScenarioBtnVisibility();
    configBtn.managedProperty().bind(configBtn.visibleProperty());
    configBtn.visibleProperty().bind(Bindings.createBooleanBinding(() -> {
      VisualEditFunc func = currentFunc.get();
      if (func == null) {
        return false;
      }
      return getOperationView(func).getOperationControllable().isConfigable();
    }, currentFunc));


    // 左侧切换按钮的disable逻辑
    BooleanBinding previousDisableBinding = new BooleanBinding() {
      {
        super.bind(currentFunc, systemEditModel.getAllOnlineOperatableFuncs());
      }

      @Override
      protected boolean computeValue() {
        return systemEditModel.getAllOnlineOperatableFuncs().isEmpty() || currentFunc.get() == null
            || systemEditModel.getAllOnlineOperatableFuncs().indexOf(currentFunc.get()) <= 0;
      }
    };
    leftArrow.disableProperty().bind(previousDisableBinding);

    // 右侧切换按钮的disable逻辑
    BooleanBinding nextDisableBinding = new BooleanBinding() {
      {
        super.bind(currentFunc, systemEditModel.getAllOnlineOperatableFuncs());
      }

      @Override
      protected boolean computeValue() {
        ObservableList<VisualEditFunc> groups = systemEditModel.getAllOnlineOperatableFuncs();
        return groups.isEmpty() || groups.indexOf(currentFunc.get()) + 1 >= groups.size();
      }
    };
    rightArrow.disableProperty().bind(nextDisableBinding);
    // 增删时的逻辑
    systemEditModel.getAllOnlineOperatableFuncs()
        .addListener(new ListChangeListener<VisualEditFunc>() {

          @Override
          public void onChanged(ListChangeListener.Change<? extends VisualEditFunc> change) {
            while (change.next()) {
              for (VisualEditFunc func : change.getRemoved()) {
                if (func == getCurrentFunc()) {
                  setCurrentFunc(null);
                  onRightArrow(null);
                  break;
                }
              }
            }
            if (getCurrentFunc() == null) {
              onRightArrow(null);
            }
          }
        });

    // 默认显示第一个function
    onRightArrow(null);

    // video preview
    videoPreviewContainer.managedProperty().bind(videoPreviewContainer.visibleProperty());
    videoPreviewContainer.setVisible(isVideoPreviewable());
    // 选中时预览
    sourceList.getSelectionModel().selectedItemProperty().addListener((obs, oldVal, newVal) -> {
      previewVideoProperty().set(sourceList.getSelectionModel().getSelectedItem());
    });
    sourceTree.getSelectionModel().selectedItemProperty().addListener((obs, oldVal, newVal) -> {
      TreeItem<VisualEditNode> treeItem = sourceTree.getSelectionModel().getSelectedItem();
      if (treeItem == null) {
        previewVideoProperty().set(null);
        return;
      }
      VisualEditNode node = treeItem.getValue();
      if (node instanceof VisualEditTerminal) {
        previewVideoProperty().set((VisualEditTerminal) node);
      } else {
        previewVideoProperty().set(null);
      }
    });

  }

  protected void updateSaveScenarioBtnVisibility() {
    List<Observable> dependencies = new ArrayList<>();
    dependencies.add(currentFunc);
    if (deviceControllable != null) {
      dependencies.add(deviceControllable.onlineProperty());
    }

    BooleanBinding visibilityBinding = Bindings.createBooleanBinding(() -> {
      VisualEditFunc func = currentFunc.get();
      if (func == null) {
        return false;
      }
      return getOperationView(func).getOperationControllable().canSaveScenario();
    }, dependencies.toArray(new Observable[0]));

    saveScenarioBtn.visibleProperty().bind(visibilityBinding);
    saveAsScenarioBtn.visibleProperty().bind(visibilityBinding);
  }

  protected void updateVideoSourceList(VisualEditFunc func) {
    if (func == null) {
      return;
    }
    videoSources.clearList();
    videoSources.appendList(emptyVideoSource);
    ObservableList<VisualEditTerminal> availableList = getFunctionAvailableTx(func);
    videoSources.appendList(availableList);

    updateSourceTree(func);
  }

  protected ObservableList<VisualEditTerminal> getFunctionAvailableTx(VisualEditFunc func) {
    if (func == null) {
      return FXCollections.observableArrayList();
    } else {
      return systemEditModel.getFunctionAvailableTx(func);
    }
  }

  protected void updateSourceTree(VisualEditFunc func) {
    VisualEditNode parent = func;
    if (parent == null) {
      return;
    }
    while (parent.getParent() != null) {
      parent = parent.getParent();
    }
    if (parent == func) {
      log.warn("Function has no parent !");
      return;
    }

    if (parent instanceof VisualEditMatrix) {
      Set<VisualEditTerminal> visibleTerminals =
          new HashSet<>(getFunctionAvailableTx(func));
      final Set<TreeItem<VisualEditNode>> oldItems = new HashSet<>(getAllTreeItems(sourceTreeRoot));

      if (sourceTreeRoot.getValue() != parent) {
        sourceTreeRoot = new SourceTreeItem(parent, updateTreeFunction);
        sourceTree.setRoot(sourceTreeRoot);
      }
      updateTreeItem(parent, sourceTreeRoot, visibleTerminals);
      sourceTreeRoot.getChildren().add(0,
          new TreeItem<VisualEditNode>(emptyVideoSource.get(0)));
      //清除旧节点
      Set<TreeItem<VisualEditNode>> newItems = new HashSet<>(getAllTreeItems(sourceTreeRoot));
      oldItems.removeAll(newItems);
      for (TreeItem<VisualEditNode> item : oldItems) {
        if (item instanceof SourceTreeItem) {
          ((SourceTreeItem) item).destroy();
        }
      }

      sourceTree.refresh();
    }
  }

  protected TreeItem<VisualEditNode> findTreeItem(TreeItem<VisualEditNode> root,
                                                  VisualEditNode node) {
    if (root == null) {
      return null;
    }
    if (root.getValue() == node) {
      return root;
    }
    for (TreeItem<VisualEditNode> child : root.getChildren()) {
      TreeItem<VisualEditNode> treeItem = findTreeItem(child, node);
      if (treeItem != null) {
        return treeItem;
      }
    }
    return null;
  }

  protected Collection<TreeItem<VisualEditNode>> getAllTreeItems(TreeItem<VisualEditNode> root) {
    List<TreeItem<VisualEditNode>> result = new ArrayList<>();
    if (root == null) {
      return result;
    }
    result.add(root);
    for (TreeItem<VisualEditNode> item : root.getChildren()) {
      result.addAll(getAllTreeItems(item));
    }
    return result;
  }

  protected void updateTreeItem(VisualEditNode nodeParent, TreeItem<VisualEditNode> treeParent,
                                Set<VisualEditTerminal> visibleTerminals) {
    Map<VisualEditNode, TreeItem<VisualEditNode>> existItems = new HashMap<>();
    for (TreeItem<VisualEditNode> child : treeParent.getChildren()) {
      existItems.put(child.getValue(), child);
    }

    List<TreeItem<VisualEditNode>> nodes = new ArrayList<>();
    for (VisualEditNode node : nodeParent.getTxChildren()) {
      if (!isNodeVisible(node, visibleTerminals)) {
        continue;
      }
      TreeItem<VisualEditNode> treeItem = existItems.get(node);
      if (treeItem == null) {
        treeItem = new SourceTreeItem(node, updateTreeFunction);
      }
      nodes.add(treeItem);
      updateTreeItem(node, treeItem, visibleTerminals);
    }
    treeParent.getChildren().setAll(nodes);
  }

  protected boolean isNodeVisible(VisualEditNode node, Set<VisualEditTerminal> visibleTerminals) {
    if (node instanceof VisualEditTerminal) {
      VisualEditTerminal terminal = (VisualEditTerminal) node;
      return visibleTerminals.contains(terminal);
    } else if (node instanceof VisualEditGroup) {
      VisualEditGroup group = (VisualEditGroup) node;
      // 如果有一个或多个子节点visible，那么显示，否则不显示
      for (VisualEditTerminal terminal : group.getAllTerminalChild()) {
        if (isNodeVisible(terminal, visibleTerminals)) {
          return true;
        }
      }
      return false;
    } else {
      return true;
    }
  }

  protected OperationView getOperationView(VisualEditFunc func) {
    if (views.containsKey(func)) {
      return views.get(func);
    } else {
      OperationView view = createFuncView(func);
      views.put(func, view);
      return view;
    }
  }

  public VisualEditFunc getCurrentFunc() {
    return currentFunc.get();
  }

  public void setCurrentFunc(VisualEditFunc func) {
    currentFunc.set(func);
  }

  private VisualEditFunc getPreviousFunc() {
    if (systemEditModel == null) {
      return null;
    }
    VisualEditFunc func = getCurrentFunc();
    if (func == null) {
      return null;
    } else {
      int index = systemEditModel.getAllOnlineOperatableFuncs().indexOf(func);
      if (index <= 0) {
        return func;
      } else {
        return systemEditModel.getAllOnlineOperatableFuncs().get(index - 1);
      }
    }
  }

  private VisualEditFunc getNextFunc() {
    if (systemEditModel == null) {
      return null;
    }
    ObservableList<VisualEditFunc> funcs = systemEditModel.getAllOnlineOperatableFuncs();
    if (funcs.isEmpty()) {
      return null;
    }
    int currentIndex = -1;
    VisualEditFunc func = getCurrentFunc();
    if (func != null) {
      currentIndex = funcs.indexOf(func);
    }

    currentIndex++;
    if (currentIndex >= funcs.size()) {
      return func;
    } else {
      return funcs.get(currentIndex);
    }
  }

  @FXML
  protected void onLeftArrow(MouseEvent event) {
    setCurrentFunc(getPreviousFunc());
  }

  @FXML
  protected void onRightArrow(MouseEvent event) {
    setCurrentFunc(getNextFunc());
  }

  @FXML
  protected void onSaveScenario(ActionEvent event) {
    if (getCurrentFunc() == null) {
      return;
    }
    getOperationView(getCurrentFunc()).getOperationControllable().saveScenario();
  }

  @FXML
  protected void onSaveAsScenario(ActionEvent event) {
    if (getCurrentFunc() == null) {
      return;
    }
    getOperationView(getCurrentFunc()).getOperationControllable().saveAsScenario();
  }

  @FXML
  protected void onConfig(ActionEvent event) {
    if (getCurrentFunc() == null) {
      return;
    }
    getOperationView(getCurrentFunc()).getOperationControllable().onConfig();
  }

  @FXML
  protected void onSwitch(ActionEvent event) {
    Bounds bounds = functionView.getBoundsInLocal();
    bounds = functionView.localToScreen(bounds);
    OfficeWindow.showWindow(this, systemEditModel, functionTitle.getScene().getWindow(),
        bounds.getMinX(), bounds.getMinY(), bounds.getWidth(), bounds.getHeight());
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    deviceControllable = deviceController;
    for (OperationView view : views.values()) {
      view.getOperationControllable().setDeviceController(deviceControllable);
    }
    updateSaveScenarioBtnVisibility();
  }

  @Override
  public void switchToFunction(String funcGuid) {
    for (VisualEditFunc func : systemEditModel.getAllOnlineOperatableFuncs()) {
      if (func.getGuid().equals(funcGuid)) {
        setCurrentFunc(func);
        OfficeWindow.hideWindow();
        return;
      }
    }
    log.warn("Can not find function {}!", funcGuid);
  }

  @Override
  public boolean isVideoPreviewable() {
    return false;
  }

  @Override
  public ObjectProperty<VisualEditTerminal> previewVideoProperty() {
    return previewVideo;
  }

  @Override
  public DeviceControllable getDeviceController() {
    return null;
  }

  @Override
  public void close() {
    for (OperationView view : views.values()) {
      if (view != null && view.getOperationControllable() != null) {
        view.getOperationControllable().close();
      }
    }
  }

  static class SourceTreeItem extends TreeItem<VisualEditNode> {
    private ListChangeListener<VisualEditNode> childrenChangeListener;
    private ObservableList<VisualEditNode> children = null;

    public SourceTreeItem(VisualEditNode node, Function<Void, Void> updateFunction) {
      super(node);
      childrenChangeListener = (change) -> {
        updateFunction.apply(null);
      };
      if (getValue() != null) {
        children = getValue().getObservableChildren();
        children.addListener(childrenChangeListener);
      }
    }

    public void destroy() {
      if (children != null) {
        children.removeListener(childrenChangeListener);
      }
    }
  }
}
