package com.mc.tool.framework.utility;

import com.mc.common.util.PlatformUtility;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class ViewLogger {

  @Getter
  private ObservableList<LogInfo> logs = FXCollections.observableArrayList();

  /**
   * 添加成功日志.
   *
   * @param message    信息
   * @param exceptions 异常信息
   */
  public void success(String message, Object... exceptions) {
    log.info(message, exceptions);
    final String newlog = formatMessage(message);
    PlatformUtility.runInFxThread(() -> {
      logs.add(new LogInfo(LogType.SUCCESS, newlog));
    });
  }

  /**
   * 添加信息日志.
   *
   * @param message    信息
   * @param exceptions 异常信息
   */
  public void info(String message, Object... exceptions) {
    log.info(message, exceptions);
    final String newlog = formatMessage(message);
    PlatformUtility.runInFxThread(() -> {
      logs.add(new LogInfo(LogType.INFO, newlog));
    });
  }

  /**
   * 添加警告日志.
   *
   * @param message    信息
   * @param exceptions 异常信息
   */
  public void warn(String message, Object... exceptions) {
    log.warn(message, exceptions);
    final String newlog = formatMessage(message);
    PlatformUtility.runInFxThread(() -> {
      logs.add(new LogInfo(LogType.WARN, newlog));
    });
  }

  /**
   * 添加错误日志.
   *
   * @param message    信息
   * @param exceptions 异常信息
   */
  public void error(String message, Object... exceptions) {
    log.error(message, exceptions);
    final String newlog = formatMessage(message);
    PlatformUtility.runInFxThread(() -> {
      logs.add(new LogInfo(LogType.ERR, newlog));
    });
  }

  private String formatMessage(String message) {
    return "[" + DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS").format(LocalDateTime.now())
        + "] " + message;
  }

  /**
   * 清除日志.
   */
  public void clearLog() {
    PlatformUtility.runInFxThread(() -> {
      logs.clear();
    });
  }

  enum LogType {
    INFO, WARN, ERR, SUCCESS
  }

  /**
   * .
   */
  public static class LogInfo {

    private final LogType type;
    private final String message;

    public LogInfo(LogType type, String msg) {
      this.type = type;
      this.message = msg;
    }

    public LogType getType() {
      return type;
    }

    public String getMessage() {
      return message;
    }
  }
}
