package com.mc.tool.framework.systemedit.view;

import java.util.ArrayList;
import java.util.List;
import javafx.beans.value.WritableValue;
import javafx.scene.layout.GridPane;

/**
 * .
 */
public class NoCssGridPane extends GridPane {
  @Override
  public List<String> impl_getAllParentStylesheets() {
    return new ArrayList<>();
  }

  @Override
  protected void impl_processCSS(WritableValue<Boolean> unused) {
  }
}
