package com.mc.tool.framework.systemedit.controller;

import com.mc.graph.McGraph;
import com.mc.graph.util.SelectableNode;
import javafx.beans.InvalidationListener;
import javafx.collections.ListChangeListener;
import javafx.event.EventHandler;
import javafx.scene.input.MouseEvent;

/**
 * .
 */
public interface GraphWrapper {

  void attachScaleChangeListener(InvalidationListener scaleChangeListener);

  void attachSelectChangeListener(ListChangeListener<SelectableNode> selectChangeListener);

  void attachClickEventHandler(EventHandler<MouseEvent> clickEventHandler);

  void attachClickEventFilter(EventHandler<MouseEvent> clickEventFilter);

  /**
   * 添加到界面中.
   */
  void attach();

  /**
   * 解除与界面的绑定.
   */
  void detach();

  void updateModelToGraph();

  void clearGraph();

  McGraph getGraph();

  String getName();

  boolean isAttached();
}
