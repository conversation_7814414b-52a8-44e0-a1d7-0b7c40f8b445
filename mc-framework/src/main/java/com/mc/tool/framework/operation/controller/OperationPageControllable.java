package com.mc.tool.framework.operation.controller;

import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import javafx.beans.property.ObjectProperty;

/**
 * .
 */
public interface OperationPageControllable extends ViewControllable {
  void initModel(VisualEditModel model);

  /**
   * 切换到相应function的配置界面.
   *
   * @param funcGuid function的guid
   */
  void switchToFunction(String funcGuid);

  /**
   * 是否可预览视频源.
   *
   * @return 如果可预览，返回true
   */
  boolean isVideoPreviewable();

  /**
   * 预览视频源.
   */
  ObjectProperty<VisualEditTerminal> previewVideoProperty();

  default void refreshOnShow() {

  }
}
