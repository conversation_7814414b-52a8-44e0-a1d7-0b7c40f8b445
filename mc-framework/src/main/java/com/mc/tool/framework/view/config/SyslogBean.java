package com.mc.tool.framework.view.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLMapper;
import com.mc.tool.framework.DefaultLog4jConfigurationFactory;
import com.mc.tool.framework.VpmConfig;
import java.io.File;
import java.io.IOException;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * .
 */
public class SyslogBean {

  private String host = "";
  @Min(0)
  @Max(65535)
  private int port;

  /**
   * .
   */
  public SyslogBean() {
    try {
      File configFile = new File(DefaultLog4jConfigurationFactory.CONFIG_FILE);
      if (configFile.exists()) {
        ObjectMapper mapper = new YAMLMapper();
        VpmConfig data =
            mapper.readValue(configFile, VpmConfig.class);
        if (data != null && data.getSyslog() != null) {
          host = data.getSyslog().getHost();
          port = data.getSyslog().getPort();
        }
      }
    } catch (IOException ex) {
      System.out.println(ex.getLocalizedMessage());
      ex.printStackTrace();
    }
  }

  public String getHost() {
    return host;
  }

  public void setHost(String host) {
    this.host = host;
  }

  public int getPort() {
    return port;
  }

  public void setPort(int port) {
    this.port = port;
  }
}
