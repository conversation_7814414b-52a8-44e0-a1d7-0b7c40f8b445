package com.mc.tool.framework.interfaces;

/**
 * .
 */
public interface AppManager {
  /**
   * Get the task manager.
   *
   * @return task manager
   */
  TaskManager getTaskManager();

  /**
   * Get the view manager.
   *
   * @return view manager
   */
  ViewManager getViewManager();

  /**
   * Get the entity manager.
   *
   * @return entity manager
   */
  EntityManager getEntityMananger();


  void exit();
}
