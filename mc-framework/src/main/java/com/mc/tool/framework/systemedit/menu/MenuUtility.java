package com.mc.tool.framework.systemedit.menu;

import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * .
 */
public class MenuUtility {
  /**
   * 获取指定类型的选中项.
   *
   * @param controllable controllable
   * @param clazz        指定类型的class
   * @param allowOther   是否允许选中项有其他类型，如果不允许，而且选中项有其他类型，返回空
   * @return 指定类型的选中项的集合
   */
  public static <T> Collection<T> getSpecifiedSelectedItems(SystemEditControllable controllable,
                                                            Class<T> clazz, boolean allowOther) {
    List<T> result = new ArrayList<>();
    for (VisualEditNode node : controllable.getSelectedNodes()) {
      if (clazz.isInstance(node)) {
        result.add(clazz.cast(node));
      } else if (!allowOther) {
        return Collections.emptyList();
      }
    }
    return result;
  }
}
