package com.mc.tool.framework.utility;

import impl.org.controlsfx.ImplUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.Stack;
import java.util.function.BooleanSupplier;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.ObservableMap;
import javafx.event.ActionEvent;
import javafx.event.EventHandler;
import javafx.geometry.Rectangle2D;
import javafx.scene.Node;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonBar.ButtonData;
import javafx.scene.control.ButtonType;
import javafx.scene.layout.Pane;
import javafx.stage.Screen;
import javafx.stage.Window;
import org.controlsfx.tools.ValueExtractor;

/**
 * .
 */
public class UndecoratedWizard {

  private UndecoratedDialog<ButtonType> dialog;

  private final ObservableMap<String, Object> settings = FXCollections.observableHashMap();

  private final Stack<UndecoratedWizardPane> pageHistory = new Stack<>();

  private Optional<UndecoratedWizardPane> currentPage = Optional.empty();

  private final BooleanProperty invalidProperty = new SimpleBooleanProperty(false);

  private final BooleanProperty readSettingsProperty = new SimpleBooleanProperty(true);

  public static final ButtonType buttonPrevious =
      new ButtonType(I18nUtility.getI18nBundle("main").getString("framework.previous"),
          ButtonData.BACK_PREVIOUS);
  private final EventHandler<ActionEvent> buttonPreviousActionHandler = actionEvent -> {
    actionEvent.consume();
    currentPage = Optional.ofNullable(pageHistory.isEmpty() ? null : pageHistory.pop());
    updatePage(dialog, false);
  };

  public static final ButtonType buttonNext = new ButtonType(
      I18nUtility.getI18nBundle("main").getString("framework.next"), ButtonData.NEXT_FORWARD);
  private final EventHandler<ActionEvent> buttonNextActionHandler = actionEvent -> {
    actionEvent.consume();
    currentPage.ifPresent(page -> pageHistory.push(page));
    currentPage = getFlow().advance(currentPage.orElse(null));
    updatePage(dialog, true);
  };

  private final StringProperty titleProperty = new SimpleStringProperty();

  public UndecoratedWizard() {
    this(null);
  }

  public UndecoratedWizard(Object owner) {
    this(owner, "");
  }

  /**
   * Constructor.
   */
  public UndecoratedWizard(Object owner, String title) {

    invalidProperty.addListener((ob, ov, nv) -> validateActionState());

    dialog = new UndecoratedDialog<>();
    dialog.titleProperty().bind(this.titleProperty);
    setTitle(title);

    Window window = null;
    if (owner instanceof Window) {
      window = (Window) owner;
    } else if (owner instanceof Node) {
      window = ((Node) owner).getScene().getWindow();
    }

    dialog.initOwner(window);
  }

  public final Optional<ButtonType> showAndWait() {
    return dialog.showAndWait();
  }


  public final ObjectProperty<ButtonType> resultProperty() {
    return dialog.resultProperty();
  }

  public final ObservableMap<String, Object> getSettings() {
    return settings;
  }

  public final StringProperty titleProperty() {
    return titleProperty;
  }

  public final String getTitle() {
    return titleProperty.get();
  }

  public final void setTitle(String title) {
    titleProperty.set(title);
  }

  private ObjectProperty<Flow> flow = new SimpleObjectProperty<Flow>() {
    @Override
    protected void invalidated() {
      updatePage(dialog, false);
    }

    @Override
    public void set(Flow flow) {
      super.set(flow);
      pageHistory.clear();
      if (flow != null) {
        currentPage = flow.advance(currentPage.orElse(null));
        updatePage(dialog, true);
      }
    }
  };

  public final ObjectProperty<Flow> flowProperty() {
    return flow;
  }

  public final Flow getFlow() {
    return flow.get();
  }

  public final void setFlow(Flow flow) {
    this.flow.set(flow);
  }


  private static final Object USER_DATA_KEY = new Object();

  private ObservableMap<Object, Object> properties;

  /**
   * .
   */
  public final ObservableMap<Object, Object> getProperties() {
    if (properties == null) {
      properties = FXCollections.observableMap(new HashMap<>());
    }
    return properties;
  }

  public boolean hasProperties() {
    return properties != null && !properties.isEmpty();
  }


  public void setUserData(Object value) {
    getProperties().put(USER_DATA_KEY, value);
  }


  public Object getUserData() {
    return getProperties().get(USER_DATA_KEY);
  }

  public final void setInvalid(boolean invalid) {
    invalidProperty.set(invalid);
  }

  final boolean isInvalid() {
    return invalidProperty.get();
  }


  public final BooleanProperty invalidProperty() {
    return invalidProperty;
  }

  public final void setReadSettings(boolean readSettings) {
    readSettingsProperty.set(readSettings);
  }

  public final boolean isReadSettings() {
    return readSettingsProperty.get();
  }


  public final BooleanProperty readSettingsProperty() {
    return readSettingsProperty;
  }


  private void updatePage(UndecoratedDialog<ButtonType> dialog, boolean advancing) {
    Flow flow = getFlow();
    if (flow == null) {
      return;
    }

    Optional<UndecoratedWizardPane> prevPage =
        Optional.ofNullable(pageHistory.isEmpty() ? null : pageHistory.peek());
    prevPage.ifPresent(page -> {
      if (advancing && isReadSettings()) {
        readSettings(page);
      }

      page.onExitingPage(this);
    });

    currentPage.ifPresent(currentPage -> {
      List<ButtonType> buttons = currentPage.getButtonTypes();
      if (!buttons.contains(buttonPrevious)) {
        buttons.add(buttonPrevious);
        Button button = (Button) currentPage.lookupButton(buttonPrevious);
        button.addEventFilter(ActionEvent.ACTION, buttonPreviousActionHandler);
      }
      if (!buttons.contains(buttonNext)) {
        buttons.add(buttonNext);
        Button button = (Button) currentPage.lookupButton(buttonNext);
        button.addEventFilter(ActionEvent.ACTION, buttonNextActionHandler);
      }
      if (!buttons.contains(ButtonType.FINISH)) {
        buttons.add(ButtonType.FINISH);
      }
      if (!buttons.contains(ButtonType.CANCEL)) {
        buttons.add(ButtonType.CANCEL);
      }

      currentPage.onEnteringPage(this);

      if (currentPage.getParent() != null && currentPage.getParent() instanceof Pane) {
        Pane parentOfCurrentPage = (Pane) currentPage.getParent();
        parentOfCurrentPage.getChildren().remove(currentPage);
      }

      double previousX = dialog.getX();
      double previousY = dialog.getY();
      double previousWidth = dialog.getWidth();
      double previousHeight = dialog.getHeight();
      dialog.setDialogPane(currentPage);
      Window wizard = currentPage.getScene().getWindow();
      wizard.sizeToScene();


      if (!Double.isNaN(previousX) && !Double.isNaN(previousY)) {
        double newWidth = dialog.getWidth();
        double newHeight = dialog.getHeight();
        int newX = (int) (previousX + (previousWidth / 2.0) - (newWidth / 2.0));
        int newY = (int) (previousY + (previousHeight / 2.0) - (newHeight / 2.0));

        ObservableList<Screen> screens = Screen.getScreensForRectangle(previousX, previousY, 1, 1);
        Screen screen = screens.isEmpty() ? Screen.getPrimary() : screens.get(0);
        Rectangle2D scrBounds = screen.getBounds();
        int maxX = (int) Math.round(scrBounds.getMaxX());
        int maxY = (int) Math.round(scrBounds.getMaxY());
        if (newX + newWidth > maxX) {
          newX = maxX - (int) Math.round(newWidth);
        }
        if (newY + newHeight > maxY) {
          newY = maxY - (int) Math.round(newHeight);
        }
        int minX = (int) Math.round(scrBounds.getMinX());
        if (newX < minX) {
          newX = minX;
        }
        int minY = (int) Math.round(scrBounds.getMinY());
        if (newY < minY) {
          newY = minY;
        }

        dialog.setX(newX);
        dialog.setY(newY);
      }
    });

    validateActionState();
  }

  private void validateActionState() {
    final List<ButtonType> currentPaneButtons = dialog.getDialogPane().getButtonTypes();

    if (getFlow().canAdvance(currentPage.orElse(null))) {
      currentPaneButtons.remove(ButtonType.FINISH);
    } else {
      currentPaneButtons.remove(buttonNext);
    }

    validateButton(buttonPrevious, () -> pageHistory.isEmpty());
    validateButton(buttonNext, () -> invalidProperty.get());
    validateButton(ButtonType.FINISH, () -> invalidProperty.get());

  }

  private void validateButton(ButtonType buttonType, BooleanSupplier condition) {
    Button btn = (Button) dialog.getDialogPane().lookupButton(buttonType);
    if (btn != null) {
      Node focusOwner = (btn.getScene() != null) ? btn.getScene().getFocusOwner() : null;
      btn.setDisable(condition.getAsBoolean());
      if (focusOwner != null) {
        focusOwner.requestFocus();
      }
    }
  }

  private int settingCounter;

  private void readSettings(UndecoratedWizardPane page) {

    settingCounter = 0;
    checkNode(page.getContent());
  }

  private boolean checkNode(Node node) {
    boolean success = readSetting(node);

    if (success) {
      return true;
    } else {

      List<Node> children = ImplUtils.getChildren(node, true);

      boolean childSuccess = false;
      for (Node child : children) {
        childSuccess |= checkNode(child);
      }
      return childSuccess;
    }
  }

  private boolean readSetting(Node node) {
    if (node == null) {
      return false;
    }

    Object setting = ValueExtractor.getValue(node);

    if (setting != null) {
      String settingName = node.getId();

      if (settingName == null || settingName.isEmpty()) {
        settingName = "page_" + ".setting_" + settingCounter;
      }

      getSettings().put(settingName, setting);

      settingCounter++;
    }

    return setting != null;
  }


  /**
   * Represents the page flow of the wizard. It defines only methods required to move forward in the
   * wizard logic, as backward movement is automatically handled by wizard itself, using internal
   * page history.
   */
  public interface Flow {

    Optional<UndecoratedWizardPane> advance(UndecoratedWizardPane currentPage);

    boolean canAdvance(UndecoratedWizardPane currentPage);
  }

  /**
   * .
   */
  public static class LinearFlow implements UndecoratedWizard.Flow {

    private final List<UndecoratedWizardPane> pages;

    /**
     * Creates a new LinearFlow instance that will allow for stepping through the given collection
     * of {@link WizardPane} instances.
     */
    public LinearFlow(Collection<UndecoratedWizardPane> pages) {
      this.pages = new ArrayList<>(pages);
    }

    /**
     * Creates a new LinearFlow instance that will allow for stepping through the given varargs
     * array of {@link WizardPane} instances.
     */
    public LinearFlow(UndecoratedWizardPane... pages) {
      this(Arrays.asList(pages));
    }


    @Override
    public Optional<UndecoratedWizardPane> advance(UndecoratedWizardPane currentPage) {
      int pageIndex = pages.indexOf(currentPage);
      return Optional.ofNullable(pages.get(++pageIndex));
    }

    @Override
    public boolean canAdvance(UndecoratedWizardPane currentPage) {
      int pageIndex = pages.indexOf(currentPage);
      return pages.size() - 1 > pageIndex;
    }
  }


  public UndecoratedDialog<ButtonType> getDialog() {
    return dialog;
  }

}
