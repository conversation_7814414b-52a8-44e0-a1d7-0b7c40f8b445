package com.mc.tool.framework.systemedit.datamodel;

import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.util.SelectableNode;

/**
 * .
 */
public class NodeUtility {
  /**
   * 把SelectedNode转换为VisualEditNode.
   *
   * @param node 要转换的SelectedNode
   * @return 如果转换成功，返回相应的VisualEditNode，否则返回null
   */
  public static VisualEditNode selectedNode2VisualEditNode(SelectableNode node) {
    if (node instanceof CellSkin) {
      CellSkin skin = (CellSkin) node;
      CellBindedObject cellBindedObject = skin.getCell().getBindedObject();
      if (cellBindedObject instanceof VisualEditNode) {
        return (VisualEditNode) cellBindedObject;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  /**
   * 把SelectedNode转换为CellBindedObject.
   *
   * @param node 要转换的CellBindedObject
   * @return 如果转换成功，返回相应的CellBindedObject，否则返回null
   */
  public static CellBindedObject selectedNode2CellBindedObject(SelectableNode node) {
    if (node instanceof CellSkin) {
      CellSkin skin = (CellSkin) node;
      return skin.getCell().getBindedObject();
    } else {
      return null;
    }
  }
}
