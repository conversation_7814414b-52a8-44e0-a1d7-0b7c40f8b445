package com.mc.tool.framework.systemedit.menu.predicate;

import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.Predicate;
import javafx.beans.binding.BooleanBinding;

/**
 * .
 */
public class MenuPredicateBinding extends BooleanBinding {
  private List<Predicate<VisualEditNode>> singlePredicates = new ArrayList<>();
  private List<Predicate<Collection<VisualEditNode>>> allPredicates = new ArrayList<>();
  private SystemEditControllable controllable;
  private boolean isOr;

  /**
   * Constructor.
   *
   * @param controllable controllable
   * @param isOr         predicate之间的组合是否用或运算，如果为true，那么为或运算，否则为与运算
   */
  public MenuPredicateBinding(SystemEditControllable controllable, boolean isOr) {
    this.controllable = controllable;
    this.bind(controllable.getGraph().getSelectionModel().getSelectedItems());
    this.isOr = isOr;
  }

  public void addSingleSelectionPredicate(Predicate<VisualEditNode> predicate) {
    singlePredicates.add(predicate);
  }

  public void addAllSelectionPredicate(Predicate<Collection<VisualEditNode>> predicate) {
    allPredicates.add(predicate);
  }

  @Override
  protected boolean computeValue() {
    Collection<VisualEditNode> nodes = controllable.getSelectedNodes();
    for (Predicate<Collection<VisualEditNode>> pre : allPredicates) {
      boolean result = pre.test(nodes);
      if (result && isOr) {
        return true;
      } else if (!result && !isOr) {
        return false;
      }
    }

    for (VisualEditNode node : nodes) {
      for (Predicate<VisualEditNode> pre : singlePredicates) {
        boolean result = pre.test(node);
        if (result && isOr) {
          return true;
        } else if (!result && !isOr) {
          return false;
        }
      }
    }

    return !isOr;
  }
}
