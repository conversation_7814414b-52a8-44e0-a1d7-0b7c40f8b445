package com.mc.tool.framework.systemedit.datamodel;

import com.mc.tool.framework.operation.videowall.datamodel.VirtualVideoWallData;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.ScreenObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoWallObject;
import java.util.Collection;
import java.util.Collections;
import java.util.Set;
import java.util.TreeSet;
import javafx.collections.ObservableList;

/**
 * .
 */
public abstract class VideoWallFunc extends VisualEditFunc {

  public abstract VideoWallObject getVideoWallObject();

  public abstract VideoWallObject createVideoWallObject();

  /**
   * 把当前的虚拟数据适配到实际的视频墙数据中.
   *
   * @return 适配后的视频墙数据，如果适配不成功，返回null
   */
  public abstract VideoWallObject toVideoWallObject(VirtualVideoWallData virtualVideoWallData);

  public abstract ObservableList<? extends VideoWallObject> getScenarios();

  public abstract VideoObject createVideo();

  public abstract ScreenObject createScreen();

  public abstract boolean addScenario(VideoWallObject object);

  public abstract boolean removeScenario(VideoWallObject object);


  public String getUniqueName() {
    return getUniqueName(Collections.emptyList());
  }

  /**
   * 获取没有使用过的名字.
   *
   * @return 没有使用过的名字
   */
  public String getUniqueName(Collection<String> excludeItems) {
    Set<String> existNames = new TreeSet<>(String.CASE_INSENSITIVE_ORDER);
    existNames.addAll(excludeItems);

    for (VideoObject object : getVideoWallObject().getVideos()) {
      if (object.getName().get() == null || object.getName().get().isEmpty()) {
        continue;
      }
      existNames.add(object.getName().get());
    }

    for (int i = 0; i < 1000; i++) {
      String newName = String.format("VIDEO_%03d", i);
      if (!existNames.contains(newName)) {
        return newName;
      }
    }

    return "";
  }

  @Override
  public String getNodeType() {
    return SystemEditDefinition.VIDEO_WALL_CELL;
  }
}
