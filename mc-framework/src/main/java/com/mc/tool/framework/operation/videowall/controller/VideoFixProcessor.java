package com.mc.tool.framework.operation.videowall.controller;

import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoWallObject;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * 用于检查video的错误并修正.
 *
 * <AUTHOR>
 */
public class VideoFixProcessor {
  private List<Function<VideoObject, Boolean>> videoPredicates = new ArrayList<>();
  private List<Function<VideoWallObject, Boolean>> videoWallPredicates = new ArrayList<>();

  /**
   * 处理视频墙数据.
   *
   * @param videoWallData 视频墙数据
   */
  public void process(VideoWallObject videoWallData) {
    for (VideoObject videoData : videoWallData.getVideos()) {
      for (Function<VideoObject, Boolean> function : videoPredicates) {
        function.apply(videoData);
      }
    }

    for (Function<VideoWallObject, Boolean> function : videoWallPredicates) {
      function.apply(videoWallData);
    }
  }

  public void registerVideoProcessor(Function<VideoObject, Boolean> function) {
    videoPredicates.add(function);
  }

  public void registerVideoWalProcessor(Function<VideoWallObject, Boolean> function) {
    videoWallPredicates.add(function);
  }
}
