package com.mc.tool.framework.office.view;

import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.SkinManager;
import com.mc.tool.framework.office.datamodel.OfficeData;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.beans.binding.Bindings;
import javafx.collections.ObservableList;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.image.Image;
import javafx.scene.layout.Background;
import javafx.scene.layout.BackgroundImage;
import javafx.scene.layout.BackgroundPosition;
import javafx.scene.layout.BackgroundRepeat;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class VideoWallCellSkin extends RatoteCellSkin {

  /**
   * Contructor.
   *
   * @param cellobject  cell object
   * @param parent      parent
   * @param container   container
   * @param skinManager skin manager
   */
  public VideoWallCellSkin(CellObject cellobject, Parent parent, Parent container,
                           SkinManager skinManager) {
    super(cellobject, parent, container, skinManager);
  }

  @Override
  protected void initRegion() {
    try {
      FXMLLoader loader = new FXMLLoader(
          getClass().getResource("/com/mc/tool/framework/office/video_wall_cell_skin.fxml"));
      loader.setController(this);
      loader.load();
    } catch (IOException exc) {
      log.warn("Can not load office/video_wall_cell_skin.fxml", exc);
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    super.initialize(location, resources);

    updateBackground();
    cellObject.getBindedObjectProperty().addListener((observable, oldValue, newValue) -> updateBackground());
  }

  protected void updateBackground() {
    CellBindedObject object = cellObject.getBindedObject();
    if (object instanceof OfficeData) {
      OfficeData data = (OfficeData) object;
      ObservableList<VisualEditTerminal> children = data.getFunc().getAllTerminalChild();
      getImage().minWidthProperty()
          .bind(Bindings.createDoubleBinding(() -> children.size() == 1 ? 204.0 : 408.0, children));
      Image multiImage =
          new Image(getClass().getResourceAsStream("/com/mc/tool/framework/office/video_wall.png"));
      Image singleImage = new Image(
          getClass().getResourceAsStream("/com/mc/tool/framework/office/video_wall_one.png"));
      Background multiBackground = new Background(new BackgroundImage(multiImage,
          BackgroundRepeat.REPEAT, BackgroundRepeat.REPEAT, BackgroundPosition.CENTER, null));
      Background singleBackground = new Background(new BackgroundImage(singleImage,
          BackgroundRepeat.REPEAT, BackgroundRepeat.REPEAT, BackgroundPosition.CENTER, null));
      getImage().backgroundProperty().bind(Bindings.createObjectBinding(
          () -> children.size() == 1 ? singleBackground : multiBackground, children));
    }
  }

}
