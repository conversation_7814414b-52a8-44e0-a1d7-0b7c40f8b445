package com.mc.tool.framework.systemedit.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.graph.interfaces.ConnectorIdentifier;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * .
 */
@Getter
@AllArgsConstructor
public class VisualEditConnection {
  @Expose
  private VisualEditTerminal txTerminal;
  @Expose
  private ConnectorIdentifier txPort;
  @Expose
  private VisualEditTerminal rxTerminal;
  @Expose
  private ConnectorIdentifier rxPort;
  @Expose
  private String mode; // 连接模式
  @Expose
  private int rxChannel; // 连接通道

  /**
   * Constructor.
   *
   * @param txTerminal tx terminal
   * @param txPort     tx port
   * @param rxTerminal rx terminal
   * @param rxPort     rx port
   * @param mode       连接模式
   */
  public VisualEditConnection(VisualEditTerminal txTerminal, ConnectorIdentifier txPort,
                              VisualEditTerminal rxTerminal, ConnectorIdentifier rxPort, String mode) {
    this.txTerminal = txTerminal;
    this.txPort = txPort;
    this.rxTerminal = rxTerminal;
    this.rxPort = rxPort;
    this.mode = mode;
  }
}
