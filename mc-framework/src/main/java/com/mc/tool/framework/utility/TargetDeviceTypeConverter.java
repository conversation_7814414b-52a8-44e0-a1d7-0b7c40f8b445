package com.mc.tool.framework.utility;

import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.mc.tool.framework.systemedit.datamodel.TargetDeviceType;
import javafx.util.StringConverter;

/**
 * .
 */
public class TargetDeviceTypeConverter extends StringConverter<TargetDeviceType> {
  private BiMap<TargetDeviceType, String> values = HashBiMap.create();

  /**
   * Constructor.
   */
  public TargetDeviceTypeConverter() {
    for (TargetDeviceType type : TargetDeviceType.values()) {
      if (type.getName().isEmpty()) {
        continue;
      }
      values.put(type, type.getName());
    }
  }

  @Override
  public String toString(TargetDeviceType object) {
    return values.get(object);
  }

  @Override
  public TargetDeviceType fromString(String string) {
    return values.inverse().get(string);
  }

}
