package com.mc.tool.framework.systemedit.menu;

import com.mc.graph.interfaces.CellSkin;
import com.mc.tool.framework.operation.videowall.datamodel.LayoutData;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.DefaultVideoWallFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.menu.predicate.MenuPredicateBinding;
import com.mc.tool.framework.systemedit.menu.predicate.TypePredicate;
import com.mc.tool.framework.utility.I18nUtility;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.Predicate;
import javafx.scene.control.MenuItem;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class <PERSON>uVideoWall extends MenuItem {
  protected SystemEditControllable controllable;

  /**
   * Constructor.
   *
   * @param controllable controllable
   */
  public MenuVideoWall(SystemEditControllable controllable) {
    this.controllable = controllable;
    this.setText(I18nUtility.getI18nBundle("systemedit").getString("menu.create_video_wall"));
    this.setOnAction(event -> onAction());

    this.disableProperty().bind(getMenuDisableBinding(controllable));
  }

  /**
   * 获取菜单是否应该禁掉的binding.
   *
   * @param controllable controllable
   * @return binding
   */
  public MenuPredicateBinding getMenuDisableBinding(SystemEditControllable controllable) {
    MenuPredicateBinding binding = MenuGroup.getMenuDisableBinding(controllable);
    Predicate<VisualEditNode> notTypePredicate =
        new TypePredicate(VisualEditTerminal.class).negate();
    binding.addSingleSelectionPredicate(notTypePredicate);
    binding.addSingleSelectionPredicate(VisualEditNode::isTx);
    return binding;
  }

  protected void onAction() {
    Collection<CellSkin> skins = controllable.getGraph().getSelectionModel().getSelectedCellSkin();
    List<VisualEditNode> nodeList = new ArrayList<>();
    for (CellSkin skin : skins) {
      if (skin.getCell().getBindedObject() instanceof VisualEditNode) {
        nodeList.add((VisualEditNode) skin.getCell().getBindedObject());
      } else {
        log.warn("Cell's bindedobject is not VisualEditNode but {}",
            skin.getCell().getBindedObject());
      }
    }
    initLayoutData(nodeList);
  }

  protected void initLayoutData(List<VisualEditNode> nodeList) {
    DefaultVideoWallFunc func = controllable.addGroup("VideoWall", DefaultVideoWallFunc.class,
        nodeList.toArray(new VisualEditNode[0]));
    if (func != null && !nodeList.isEmpty()) {
      LayoutData layoutData = func.getVideoWallData().getLayoutData();
      layoutData.getRowsProperty().set(1);
      layoutData.getColumnsProperty().set(nodeList.size());
    }
  }
}
