package com.mc.tool.framework.systemedit.controller;

import com.mc.graph.interfaces.ColorComponent;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.graph.interfaces.GraphObject;
import com.mc.tool.framework.systemedit.datamodel.VisualEditMatrix;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import javafx.scene.paint.Color;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class SystemEditHighLightHelper {

  /**
   * 获取所有需要高亮的矩阵的端口Connector.
   *
   * @param controllable    controllable
   * @param systemEditModel model
   * @param nodes           需要高亮的节点
   * @return 高亮的connector
   */
  public static Collection<GraphObject> getHighLightGraphs(SystemEditControllable controllable,
                                                           VisualEditModel systemEditModel, VisualEditNode[] nodes) {
    if (nodes == null) {
      return Collections.emptyList();
    }
    // 获取所有的terminal对应的连接模式
    Set<GraphObject> highLightList = new HashSet<>();
    Map<VisualEditTerminal, Set<String>> highLightModes = new HashMap<>();
    Map<VisualEditTerminal, Set<ConnectorIdentifier>> highLightConnectors = new HashMap<>();
    Set<VisualEditTerminal> initiator = new HashSet<>(); //高亮的发起者
    for (VisualEditNode node : nodes) {
      VisualEditMatrix matrix = controllable.findMatrix(node);
      if (matrix == null) {
        continue;
      }
      if (node == matrix) {
        continue;
      }
      for (VisualEditTerminal terminal : node.getAllOnlineTerminalChild()) {
        Map<VisualEditTerminal, Collection<String>> connectedModes =
            systemEditModel.getConnectedTerminalMode(terminal);
        Map<VisualEditTerminal, Set<ConnectorIdentifier>> connectedConnectors =
            systemEditModel.getConnectedTerminalPort(terminal);

        Set<String> sourceModes = highLightModes.get(terminal);
        if (sourceModes == null) {
          sourceModes = new HashSet<>();
        }
        connectedModes.values().forEach(sourceModes::addAll);
        highLightModes.put(terminal, sourceModes);

        initiator.add(terminal);

        for (Map.Entry<VisualEditTerminal, Collection<String>> entry : connectedModes.entrySet()) {
          Set<String> targetModes = highLightModes.get(entry.getKey());
          if (targetModes == null) {
            targetModes = new HashSet<>();
          }
          targetModes.addAll(entry.getValue());
          highLightModes.put(entry.getKey(), targetModes);
          //记录连接的connector
          Set<ConnectorIdentifier> connectors = highLightConnectors.get(entry.getKey());
          if (connectors == null) {
            connectors = new HashSet<>();
          }
          connectors.addAll(connectedConnectors.get(entry.getKey()));
          highLightConnectors.put(entry.getKey(), connectors);
        }

      }
    }
    // 计算颜色并收集connector
    for (Map.Entry<VisualEditTerminal, Set<String>> entry : highLightModes.entrySet()) {
      VisualEditTerminal terminal = entry.getKey();
      Set<Color> colors = new HashSet<>();
      for (String mode : entry.getValue()) {
        colors.addAll(controllable.getConnectionModeColor(mode));
      }
      ColorComponent highLightColor = mergeConnectionColor(colors);
      VisualEditMatrix matrix = controllable.findMatrix(terminal);
      if (matrix == null || matrix.getCellObject() == null) {
        continue;
      }
      for (Pair<ConnectorIdentifier, ConnectorIdentifier> pair : matrix
          .getChildConnectorPair(terminal)) {
        //发起者的所有connector都高亮，非发起者的只有有连接的connector才高亮
        if (!initiator.contains(terminal)
            && highLightConnectors.get(terminal) != null
            && !highLightConnectors.get(terminal).contains(pair.getValue())) {
          continue;
        }
        Connector connector = matrix.getCellObject().getConnector(pair.getKey());
        if (connector == null) {
          log.warn("Empty connector!");
          continue;
        }
        connector.highLightColorProperty().set(highLightColor);
        highLightList.add(connector);

      }
    }
    return highLightList;
  }

  static ColorComponent mergeConnectionColor(Collection<Color> colors) {
    ColorComponent component = new ColorComponent();
    if (colors.isEmpty()) {
      return component;
    }
    double red = 0;
    double green = 0;
    double blue = 0;
    StringBuilder colorBuilder = new StringBuilder();
    for (Color item : colors) {
      red += item.getRed();
      green += item.getGreen();
      blue += item.getBlue();
      colorBuilder.append(String.format("#%02X%02X%02X;", (int) (item.getRed() * 255),
          (int) (item.getGreen() * 255), (int) (item.getBlue() * 255)));
    }
    red /= colors.size();
    green /= colors.size();
    blue /= colors.size();
    component.setMergedColor(Color.color(red, green, blue));
    component.setColorComponents(colorBuilder.toString());
    return component;
  }
}
