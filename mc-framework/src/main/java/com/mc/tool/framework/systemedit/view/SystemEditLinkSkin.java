package com.mc.tool.framework.systemedit.view;

import com.mc.graph.DefaultLinkSkin;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.LinkObject;
import com.mc.graph.interfaces.SkinManager;
import com.mc.graph.util.LinkUtil;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import javafx.beans.binding.ObjectBinding;
import javafx.scene.Parent;
import javafx.scene.paint.Paint;

/**
 * .
 */
public class SystemEditLinkSkin extends DefaultLinkSkin {

  public SystemEditLinkSkin(LinkObject link, Parent parent, Parent container,
                            SkinManager skinManager) {
    super(link, parent, container, skinManager);
  }

  @Override
  protected void initConnnectionPath() {
    final ConnectorSkin senderNode = skinManager.getConnectorSkin(link.getConnectors().getKey());
    final ConnectorSkin receiverNode =
        skinManager.getConnectorSkin(link.getConnectors().getValue());
    connectionPath = LinkUtil.createRightAngleLinkPath(senderNode, receiverNode, parent, container);
    connectionPath.setStrokeWidth(2);
    connectionPath.visibleProperty().bind(link.visibleProperty());

    link.highLightProperty().addListener(weakAdapter.wrap((obs, oldVal, newVal) -> {
      if (link.highLightProperty().get()) {
        connectionPath.toFront();
      }
    }));

    connectionPath.strokeProperty().bind(new ObjectBinding<Paint>() {
      {
        super.bind(link.highLightProperty(), link.highLightColorProperty());
      }

      @Override
      protected Paint computeValue() {
        if (link.highLightProperty().get()) {
          if (link.highLightColorProperty().get() != null
              && link.highLightColorProperty().get().getMergedColor() != null) {
            return link.highLightColorProperty().get().getMergedColor();
          } else {
            return SystemEditDefinition.LINK_HIGHLIGHT_COLOR;
          }
        } else {
          return SystemEditDefinition.LINK_NORMAL_COLOR;
        }
      }
    });
  }
}
