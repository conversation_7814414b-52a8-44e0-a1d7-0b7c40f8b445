package com.mc.tool.framework.systemedit.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.common.beans.SimpleObservable;
import com.mc.tool.framework.operation.crossscreen.datamodel.CrossScreenData;
import com.mc.tool.framework.operation.crossscreen.datamodel.CrossScreenObject;
import com.mc.tool.framework.operation.seat.datamodel.SeatData.SeatConnection;
import javafx.beans.property.ObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.util.Pair;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class DefaultCrossScreenFunc extends CrossScreenFunc {
  @Expose
  private CrossScreenData crossScreendata = new CrossScreenData();
  @Getter
  @Expose
  protected ObservableList<CrossScreenData> scenarios = FXCollections.observableArrayList();

  private SimpleObservable resetObservable = new SimpleObservable();

  public DefaultCrossScreenFunc() {
    crossScreendata.resetCapacity(getMaxColumn() * getMaxRow());
  }

  @Override
  public void init() {
    super.init();

    getAllTerminalChild().addListener((ListChangeListener<VisualEditTerminal>) change -> {
      while (change.next()) {
        if (change.wasRemoved()) {
          for (VisualEditTerminal item : change.getRemoved()) {
            removeItem(item);
          }
        }

        if (change.wasAdded()) {
          for (VisualEditTerminal item : change.getAddedSubList()) {
            addItem(item);
          }
        }
      }
    });
  }

  protected void addItem(VisualEditTerminal item) {
    if (item == null) {
      log.warn("null item!");
      return;
    }

    for (ObjectProperty<VisualEditTerminal> property : crossScreendata.getTargets()) {
      if (property.get() == null) {
        property.set(item);
        if (!crossScreendata.getConnections().containsKey(item)) {
          SeatConnection connection = new SeatConnection();
          connection.setRx(item);
          crossScreendata.getConnections().put(item, connection);
        }
        break;
      }
    }

  }

  protected void removeItem(VisualEditTerminal item) {
    if (item == null) {
      log.warn("null item!");
      return;
    }

    for (ObjectProperty<VisualEditTerminal> property : crossScreendata.getTargets()) {
      if (property.get() == item) {
        property.set(null);
      }
    }
    crossScreendata.getConnections().remove(item);
  }

  @Override
  public boolean addScenario(CrossScreenObject object) {
    return false;
  }

  @Override
  public boolean removeScenario(CrossScreenObject object) {
    return false;
  }

  @Override
  public CrossScreenObject getCrossScreenData() {
    return crossScreendata;
  }

  @Override
  public int getMaxRow() {
    return 16;
  }

  @Override
  public int getMaxColumn() {
    return 16;
  }

  @Override
  public void moveScreen(int fromRow, int fromColumn, int toRow, int toColumn) {
    if (fromRow < 0 || fromRow >= getMaxRow() || fromColumn < 0 || fromColumn >= getMaxColumn()
        || toRow < 0 || toRow >= getMaxRow() || toColumn < 0 || toColumn >= getMaxColumn()) {
      return;
    }

    int fromIndex = getIndex(fromRow, fromColumn);
    int toIndex = getIndex(toRow, toColumn);
    ObjectProperty<VisualEditTerminal> from = crossScreendata.getTarget(fromIndex);
    VisualEditTerminal target = from.get();
    from.set(null);
    crossScreendata.insertTarget(target, toIndex);
  }

  protected int getIndex(int row, int column) {
    return row * getMaxColumn() + column;
  }

  @Override
  public Pair<Integer, Integer> posOfScreen(VisualEditTerminal terminal) {
    int index = crossScreendata.indexOfTarget(terminal);
    if (index < 0) {
      return new Pair<Integer, Integer>(-1, -1);
    } else {
      return new Pair<Integer, Integer>(index / getMaxColumn(), index % getMaxColumn());
    }
  }

  @Override
  public boolean isScreenMovable() {
    return true;
  }

  @Override
  public SimpleObservable getResetObservable() {
    return resetObservable;
  }
}
