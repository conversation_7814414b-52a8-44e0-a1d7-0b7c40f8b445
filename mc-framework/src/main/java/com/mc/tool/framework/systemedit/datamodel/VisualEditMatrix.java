package com.mc.tool.framework.systemedit.datamodel;

import com.mc.graph.interfaces.ConnectorIdentifier;
import java.util.Collection;
import javafx.collections.ObservableList;
import javafx.util.Pair;

/**
 * .
 */
public interface VisualEditMatrix extends VisualEditNode {
  /**
   * 获取跟终端的连接节点对.
   *
   * @param child 终端节点
   * @return 连接节点的id的对，pair的key为父节点的connector的id，pair的value为子节点的connector的id。
   */
  Collection<Pair<ConnectorIdentifier, ConnectorIdentifier>> getChildConnectorPair(
      VisualEditTerminal child);

  Collection<ConnectorIdentifier> getLeftConnectorIds();

  Collection<ConnectorIdentifier> getRightConnectorIds();

  ObservableList<VisualEditTerminal> getAllTxChildTerminal();

  ObservableList<VisualEditTerminal> getAllRxChildTerminal();

  void insertTerminal(VisualEditTerminal terminal, int matrixPort, int terminalPort);

  VisualEditTerminal getTerminal(ConnectorIdentifier matrixPort);

  ConnectorIdentifier getTermianlPort(ConnectorIdentifier matrixPort);

  void insertGroup(int index, VisualEditGroup group);

  void removeGroup(VisualEditGroup group);
}
