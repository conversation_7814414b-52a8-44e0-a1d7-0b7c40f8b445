package com.mc.tool.framework.operation.videowall.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.ScreenObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoWallObject;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.util.Pair;
import lombok.Getter;

/**
 * .
 */
public class VideoWallData implements VideoWallObject {

  @Expose
  @Getter
  private StringProperty name = new SimpleStringProperty("");

  @Expose
  @Getter
  private LayoutData layoutData = new LayoutData();

  @Expose
  @Getter
  private ObservableList<VideoData> videos = FXCollections.observableArrayList();

  @Expose
  @Getter
  private ObservableList<ScreenData> screens = FXCollections.observableArrayList();

  /**
   * 复制数据，不复制screen数据.
   *
   * @param input      接收复制的数据的videowalldata
   * @param copyScreen TODO
   */
  @Override
  public void copyTo(VideoWallObject input, boolean copyScreen) {
    if (!(input instanceof VideoWallData)) {
      return;
    }
    VideoWallData copy = (VideoWallData) input;
    copy.name.set(name.get());
    copy.layoutData.getRowsProperty().set(layoutData.getRows());
    copy.layoutData.getColumnsProperty().set(layoutData.getColumns());
    copy.layoutData.getResWidth().set(layoutData.getResWidth().get());
    copy.layoutData.getResHeight().set(layoutData.getResHeight().get());
    copy.layoutData.getFps().set(layoutData.getFps().get());

    copy.videos.clear();
    for (VideoObject videoData : videos) {
      VideoData newVideo = new VideoData();
      videoData.copyTo(newVideo);
      copy.videos.add(newVideo);
    }

    if (copyScreen) {
      copy.screens.clear();
      for (ScreenObject screenData : screens) {
        ScreenData newScreen = new ScreenData();
        screenData.copyTo(newScreen);
        copy.screens.add(newScreen);
      }
    }
  }

  @Override
  public LogicLayoutData getLogicLayoutData() {
    return null;
  }

  @Override
  public BooleanProperty getUseLogicLayout() {
    return null;
  }

  @Override
  public void setType(short type) {

  }

  /**
   * 获取屏幕所在的位置.
   *
   * @param data 屏幕数据
   * @return 列索引与行索引的pair，索引从0开始，如果没有该屏幕，返回-1.
   */
  @Override
  public Pair<Integer, Integer> getScreenLocation(ScreenObject data) {
    int index = screens.indexOf(data);
    if (index < 0) {
      return new Pair<>(-1, -1);
    }
    return new Pair<>(index % layoutData.getColumns(),
        index / layoutData.getColumns());
  }

  @Override
  public int getScreenIndex(ScreenObject screenData) {
    return screens.indexOf(screenData);
  }


  @Override
  public int getScreenCount() {
    return screens.size();
  }


  @Override
  public boolean addVideo(VideoObject video) {
    if (video instanceof VideoData) {
      videos.add((VideoData) video);
      return true;
    } else {
      return false;
    }
  }


  @Override
  public boolean addVideo(int index, VideoObject video) {
    if (video instanceof VideoData) {
      videos.add(index, (VideoData) video);
      return true;
    } else {
      return false;
    }
  }

  @Override
  public boolean addAll(int index, Collection<VideoObject> videos) {
    List<VideoData> newItems = new ArrayList<>();
    for (VideoObject item : videos) {
      if (!(item instanceof VideoData)) {
        return false;
      }
      newItems.add((VideoData) item);
    }
    this.videos.addAll(index, newItems);
    return true;
  }

  @Override
  public boolean setVideo(int index, VideoObject video) {
    if (video instanceof VideoData) {
      videos.set(index, (VideoData) video);
      return true;
    } else {
      return false;
    }
  }

}
