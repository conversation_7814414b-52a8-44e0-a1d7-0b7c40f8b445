package com.mc.tool.framework.systemedit.view;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Network configuration information containing IP address, subnet mask, and gateway.
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class NetworkConfigInfo {
  private String ipAddress;
  private String subnetMask;
  private String gateway;
}
