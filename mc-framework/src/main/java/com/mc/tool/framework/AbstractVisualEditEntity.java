package com.mc.tool.framework;

import com.google.common.base.Charsets;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.interfaces.Page;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.TypeWrapper;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.UnsupportedEncodingException;
import java.util.Collection;
import java.util.Collections;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.concurrent.Task;
import javafx.util.Pair;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public abstract class AbstractVisualEditEntity implements Entity {
  @Getter
  protected VisualEditModel visualEditModel;

  protected Page currentPage;

  protected abstract GsonBuilder createModelSerializationBuilder();

  protected BooleanProperty activeProperty = new SimpleBooleanProperty();

  public AbstractVisualEditEntity() {
  }

  protected String getSerilizeFileName() {
    return getName() + ".json";
  }

  protected void serialize() {
    GsonBuilder builder = createModelSerializationBuilder();
    Gson gson = builder.create();
    String result = gson.toJson(visualEditModel);

    setSerializeData(result);
  }

  protected void setSerializeData(String result) {
    FileOutputStream fos = null;
    try {
      fos = new FileOutputStream(getSerilizeFileName());
      fos.write(result.getBytes(Charsets.UTF_8));
    } catch (IOException exception) {
      log.warn("Fail to write data to {}!", getSerilizeFileName());
    } finally {
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException exception2) {
          log.warn("Fail to close the file!");
        }
      }
    }
  }

  protected void deserializeAndInit() {
    GsonBuilder builder = createModelSerializationBuilder();
    Gson gson = builder.create();

    Reader reader = null;
    try {
      VisualEditModel model = null;
      InputStream is = getDeserializeData();
      if (is != null) {
        reader = new InputStreamReader(is, "UTF-8");
        model = gson.fromJson(reader, VisualEditModel.class);
      }
      if (model != null) {
        visualEditModel = model;
      }
    } catch (UnsupportedEncodingException exception) {
      log.warn("Unsupported encoding!", exception);
    } catch (RuntimeException exception) {
      log.warn("Json parse fail!", exception);
    } finally {
      if (reader != null) {
        try {
          reader.close();
        } catch (IOException exception2) {
          log.warn("Fail to close reader!", exception2);
        }
      }
    }

    if (visualEditModel == null) {
      visualEditModel = new VisualEditModel();
    }
    visualEditModel.init();
  }

  protected InputStream getDeserializeData() {
    InputStream is = null;
    try {
      is = new FileInputStream(getSerilizeFileName());
    } catch (FileNotFoundException exception) {
      log.warn("Fail to find serialize file :", getSerilizeFileName());
    }
    return is;
  }

  @Override
  public boolean close() {
    serialize();
    for (Page page : getPages()) {
      page.close();
    }
    visualEditModel = null;
    currentPage = null;
    return true;
  }

  @Override
  public Page getCurrentPage() {
    return currentPage;
  }

  @Override
  public void setCurrentPage(Page page) {
    currentPage = page;
    Task<Void> task = new RefreshTask(page);
    ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    if (app != null) {
      app.getTaskManager().addBackgroundTask(task);
    }
  }

  @Override
  public Page getPageByName(String name) {
    for (Page page : getPages()) {
      if (page.getName().equals(name)) {
        return page;
      }
    }
    return null;
  }

  @Override
  public Collection<Pair<String, Collection<TypeWrapper>>> getMenuGroup() {
    return Collections.emptyList();
  }

  @Override
  public void onMenu(TypeWrapper menuType) {
  }

  @Override
  public void setActive(boolean active) {
    activeProperty.set(active);
    for (Page page : getPages()) {
      page.onEntityActiveChange(active);
    }
  }

  @Override
  public boolean isActive() {
    return activeProperty.get();
  }

  static class RefreshTask extends Task<Void> {
    private final Page page;

    public RefreshTask(Page page) {
      this.page = page;
    }

    @Override
    protected Void call() throws Exception {
      if (page != null) {
        page.refreshOnShow();
      }
      return null;

    }

  }
}
