package com.mc.tool.framework.systemedit.behavior;

import com.mc.graph.interfaces.CellBehavior;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.GraphController;
import com.mc.graph.util.SelectableNode;
import com.mc.tool.framework.systemedit.controller.CellXposUpdater;
import com.mc.tool.framework.systemedit.controller.CellYposUpdater;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.controller.SystemEditUtility;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import java.lang.ref.WeakReference;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import javafx.beans.value.ChangeListener;
import javafx.collections.ListChangeListener;
import javafx.event.EventHandler;
import javafx.scene.input.MouseEvent;
import lombok.extern.slf4j.Slf4j;

/**
 * 用于排列group、terminal cell的顺序的行为.
 *
 * <AUTHOR>
 */
@Slf4j
public class OrderCellBehavior implements CellBehavior {
  private CellSkin skin;
  private final WeakReference<SystemEditControllable> controllable;
  private final WeakReference<GraphController> controller;
  private EventHandler<MouseEvent> mousePressedEvent;
  private EventHandler<MouseEvent> mouseReleasedEvent;
  private EventHandler<MouseEvent> mouseDragedEvent;
  private ChangeListener<Number> yposChangeListener;
  private ListChangeListener<SelectableNode> selectedChangeListener;

  private Set<CellSkin> highLightedCells = new HashSet<>();

  /**
   * Constructor.
   *
   * @param controller   graph controller
   * @param controllable system edit controller
   * @param cellSkin     skin
   */
  public OrderCellBehavior(GraphController controller, SystemEditControllable controllable,
                           CellSkin cellSkin) {
    this.skin = cellSkin;
    this.controllable = new WeakReference<>(controllable);
    this.controller = new WeakReference<>(controller);
    init();
  }

  protected GraphController getGraphController() {
    return controller.get();
  }

  protected SystemEditControllable getSystemEditControllable() {
    return controllable.get();
  }

  @Override
  public void attach() {
    skin.getRegion().addEventHandler(MouseEvent.MOUSE_PRESSED, mousePressedEvent);
    skin.getRegion().addEventHandler(MouseEvent.MOUSE_RELEASED, mouseReleasedEvent);
    skin.getRegion().addEventHandler(MouseEvent.MOUSE_DRAGGED, mouseDragedEvent);
    getGraphController().getSelectionModel().getSelectedItems().addListener(selectedChangeListener);
  }

  @Override
  public void detach() {
    skin.getRegion().removeEventHandler(MouseEvent.MOUSE_PRESSED, mousePressedEvent);
    skin.getRegion().removeEventHandler(MouseEvent.MOUSE_RELEASED, mouseReleasedEvent);
    skin.getRegion().removeEventHandler(MouseEvent.MOUSE_DRAGGED, mouseDragedEvent);
    getGraphController().getSelectionModel().getSelectedItems()
        .removeListener(selectedChangeListener);
  }

  @Override
  public void createConnectorBehavior(ConnectorSkin skin) {

  }

  private void init() {
    selectedChangeListener = (change) -> {
      if (getSystemEditControllable() != null
          && getSystemEditControllable().getDeviceController() != null
          && !getSystemEditControllable().getDeviceController().getUserRight()
          .isDeviceItemMovable()) {
        return;
      }
      if (!getSystemEditControllable().getSelectedNodes().contains(skin)) {
        cleanStatus();
      }
      // 选中多个时不能移动
      boolean movable = true;
      movable = skin.isSelected()
          && getGraphController().getSelectionModel().getSelectedItems().size() == 1;
      skin.movableProperty().set(movable);
    };
    yposChangeListener = (observable, oldValue, newValue) -> {
      if (!(skin.getCell().getBindedObject() instanceof VisualEditNode)) {
        return;
      }
      // 更新位置
      VisualEditNode node = (VisualEditNode) skin.getCell().getBindedObject();
      VisualEditNode parent = node.getParent();
      if (parent == null) {
        return;
      }
      if (!parent.isChildIndexChangable()) {
        return;
      }
      int index = 0;
      int currentIndex = -1;
      int properIndex = -1;
      Collection<VisualEditNode> children;
      if (node.isRx()) {
        children = parent.getRxChildren();
      } else if (node.isTx()) {
        children = parent.getTxChildren();
      } else {
        log.warn("Unknown node direction type");
        return;
      }
      for (VisualEditNode child : children) {
        index = parent.indexOfChild(child);
        if (node == child) {
          currentIndex = index;
        } else if (child.getCellObject() == null) {
          continue;
        } else {
          double distance = child.getCellObject().getYProperty().get()
              - node.getCellObject().getYProperty().get();
          if (distance >= SystemEditDefinition.CELL_Y_OFFSET) {
            properIndex = index;
            break;
          } else if (currentIndex != -1 && distance >= -SystemEditDefinition.CELL_Y_OFFSET) {
            properIndex = index;
            break;
          }
        }
      }
      if (properIndex == -1) {
        properIndex = index;
      } else if (currentIndex != -1 && currentIndex < properIndex) {
        // 如果当前索引在前面，那么实际的索引要减1
        properIndex--;
      }

      if (properIndex != -1 && properIndex != currentIndex) {
        parent.moveChild(node, properIndex);
      }
    };

    mousePressedEvent = (event) -> {
      if (getSystemEditControllable() != null
          && getSystemEditControllable().getDeviceController() != null
          && !getSystemEditControllable().getDeviceController().getUserRight()
          .isDeviceItemMovable()) {
        return;
      }
      detachPostionBinding();
      skin.getCell().getYProperty().addListener(yposChangeListener);
    };

    mouseReleasedEvent = (event) -> {
      if (getSystemEditControllable() != null
          && getSystemEditControllable().getDeviceController() != null
          && !getSystemEditControllable().getDeviceController().getUserRight()
          .isDeviceItemMovable()) {
        return;
      }
      for (CellSkin skin : highLightedCells) {
        skin.getCell().hoverProperty().set(false);
      }
      highLightedCells.clear();
      // 移动cell
      CellSkin[] skins = getGraphController().getHittedCellskin(event);
      for (CellSkin cellSkin : skins) {
        if (cellSkin == this.skin) {
          continue;
        }
        if (!(cellSkin.getCell().getBindedObject() instanceof VisualEditNode)
            || !(this.skin.getCell().getBindedObject() instanceof VisualEditNode)) {
          continue;
        }
        VisualEditNode from = (VisualEditNode) this.skin.getCell().getBindedObject();
        VisualEditNode to = (VisualEditNode) cellSkin.getCell().getBindedObject();
        if (getSystemEditControllable().isMovable(from, to)) {
          getSystemEditControllable().moveTo(from, to);
          break;
        }
      }
      //
      skin.getCell().getYProperty().removeListener(yposChangeListener);
      if (!(skin.getCell().getBindedObject() instanceof VisualEditNode)) {
        return;
      }

      getSystemEditControllable().updateModelToGraph();
    };

    mouseDragedEvent = (event) -> {
      if (getSystemEditControllable() != null
          && getSystemEditControllable().getDeviceController() != null
          && !getSystemEditControllable().getDeviceController().getUserRight()
          .isDeviceItemMovable()) {
        return;
      }
      // 高亮所有鼠标经过的cell
      CellSkin[] skins = getGraphController().getHittedCellskin(event);
      Set<CellSkin> newHighLightSkin = new HashSet<>();
      for (CellSkin cellSkin : skins) {
        if (cellSkin == this.skin) {
          continue;
        }
        if (!(cellSkin.getCell().getBindedObject() instanceof VisualEditNode)
            || !(this.skin.getCell().getBindedObject() instanceof VisualEditNode)) {
          continue;
        }
        VisualEditNode from = (VisualEditNode) this.skin.getCell().getBindedObject();
        VisualEditNode to = (VisualEditNode) cellSkin.getCell().getBindedObject();
        if ((!cellSkin.getCell().hoverProperty().get() || highLightedCells.contains(cellSkin))
            && getSystemEditControllable().isMovable(from, to)) {
          cellSkin.getCell().hoverProperty().set(true);
          newHighLightSkin.add(cellSkin);
        }
      }
      Set<CellSkin> deHighLightSet = new HashSet<>(highLightedCells);
      deHighLightSet.removeAll(newHighLightSkin);
      for (CellSkin removedSkin : deHighLightSet) {
        removedSkin.getCell().hoverProperty().set(false);
      }
      // highLightedCells只记录之前没有高亮，现在要高亮的skin
      highLightedCells.clear();
      highLightedCells = newHighLightSkin;
    };
  }

  protected void detachPostionBinding() {
    CellXposUpdater updater = SystemEditUtility.getCellPropertyUpdater(skin.getCell(),
        SystemEditDefinition.CELL_XPOS_UPDATER, CellXposUpdater.class);
    if (updater != null) {
      updater.detach();
    }
    CellYposUpdater updater2 = SystemEditUtility.getCellPropertyUpdater(skin.getCell(),
        SystemEditDefinition.CELL_YPOS_UPDATER, CellYposUpdater.class);
    if (updater2 != null) {
      updater2.detach();
    }
  }

  protected void cleanStatus() {
    skin.getCell().getYProperty().removeListener(yposChangeListener);
    for (CellSkin skin : highLightedCells) {
      skin.getCell().hoverProperty().set(false);
    }
    highLightedCells.clear();
  }

}
