package com.mc.tool.caesar.vpm.util.macrokey.view;

import com.mc.tool.caesar.vpm.util.macrokey.datamodel.MacroParam;
import javafx.util.StringConverter;

class MacroParamStringConverter extends StringConverter<MacroParam> {

  @Override
  public String toString(MacroParam object) {
    if (object == null) {
      return "";
    } else {
      return object.getFullName();
    }
  }

  @Override
  public MacroParam fromString(String string) {
    return null;
  }
}
