package com.mc.tool.caesar.vpm.pages.systemedit.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.framework.systemedit.datamodel.AbstractVisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.MultimediaInterfaceType;
import com.mc.tool.framework.systemedit.datamodel.NetworkInterfaceType;
import com.mc.tool.framework.systemedit.datamodel.Resolution;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.TargetDeviceType;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.ReadOnlyBooleanProperty;
import javafx.beans.property.ReadOnlyIntegerProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public class CaesarGridLineTerminal extends AbstractVisualEditNode implements VisualEditTerminal {

  @Expose @Getter @Setter private int portIndex;

  @Getter private PortData portData;

  protected ObjectProperty<TargetDeviceType> targetDeviceType =
      new SimpleObjectProperty<>(TargetDeviceType.COMPUTER);
  protected ObjectProperty<NetworkInterfaceType> networkInterfaceType =
      new SimpleObjectProperty<>(NetworkInterfaceType.SPF);
  protected ObjectProperty<MultimediaInterfaceType> multimediaInterfaceType =
      new SimpleObjectProperty<>(MultimediaInterfaceType.GRID_LINE);
  protected IntegerProperty idProperty = new SimpleIntegerProperty();
  protected BooleanProperty onlineProperty = new SimpleBooleanProperty(false);
  protected BooleanProperty targetDeviceConnected = new SimpleBooleanProperty(false);

  protected ObservableList<ConnectorIdentifier> connectorIds = FXCollections.observableArrayList();

  protected IntegerProperty channel1UsedPort = new SimpleIntegerProperty(0);
  protected BooleanProperty channel1Used = new SimpleBooleanProperty(false);

  protected BooleanProperty channel2Used = new SimpleBooleanProperty(false);
  protected IntegerProperty channel2UsedPort = new SimpleIntegerProperty(0);

  public CaesarGridLineTerminal() {
    connectorIds.add(ConnectorIdentifier.getIdentifier(1));
  }

  /**
   * 设置端口数据.
   *
   * @param portData 端口数据
   */
  public void setPortData(PortData portData) {
    this.portData = portData;
    onlineProperty.set(
        portData != null && portData.isStatusAvailable() && portData.isStatusMatrix());

    updateStatus();
  }

  /** 更新状态. */
  public void updateStatus() {
    int channel1UsedPort = 0;
    int channel2UsedPort = 0;
    if (isOnline()) {
      channel1UsedPort = portData.getOutput();
      int connectedPort = portData.getType();
      PortData connectedPortData = portData.getConfigDataManager().getPortData(connectedPort - 1);
      if (connectedPortData != null) {
        channel2UsedPort = connectedPortData.getOutput();
      }
    }

    this.channel1Used.set(channel1UsedPort > 0);
    this.channel2Used.set(channel2UsedPort > 0);
    this.channel1UsedPort.set(channel1UsedPort);
    this.channel2UsedPort.set(channel2UsedPort);

    if (channel1Used.get() && channel2Used.get()) {
      targetDeviceType.set(TargetDeviceType.GRID_LINE_DUAL);
    } else if (channel1Used.get()) {
      targetDeviceType.set(TargetDeviceType.GRID_LINE_T);
    } else if (channel2Used.get()) {
      targetDeviceType.set(TargetDeviceType.GRID_LINE_R);
    } else {
      targetDeviceType.set(TargetDeviceType.GRID_LINE_N);
    }
  }

  public ReadOnlyBooleanProperty channel1UsedProperty() {
    return channel1Used;
  }

  public ReadOnlyBooleanProperty channel2UsedProperty() {
    return channel2Used;
  }

  public ReadOnlyIntegerProperty channel1UsedPortProperty() {
    return channel1UsedPort;
  }

  public ReadOnlyIntegerProperty channel2UsedPortProperty() {
    return channel2UsedPort;
  }

  @Override
  public boolean isOnline() {
    return onlineProperty.get();
  }

  @Override
  public boolean isValid() {
    return portIndex != 0;
  }

  @Override
  public BooleanProperty onlineProperty() {
    return onlineProperty;
  }

  @Override
  public Resolution getResolution(int index) {
    return null;
  }

  @Override
  public boolean isTargetDeviceConnected() {
    return targetDeviceConnected.get();
  }

  @Override
  public BooleanProperty targetDeviceConnectedProperty() {
    return targetDeviceConnected;
  }

  @Override
  public TargetDeviceType getTargetDeviceType() {
    return targetDeviceType.get();
  }

  @Override
  public void setTargetDeviceType(TargetDeviceType type) {}

  @Override
  public ObjectProperty<TargetDeviceType> targetDeviceTypeProperty() {
    return targetDeviceType;
  }

  @Override
  public NetworkInterfaceType getNetworkInterfaceType() {
    return networkInterfaceType.get();
  }

  @Override
  public void setNetworkInterfaceType(NetworkInterfaceType type) {}

  @Override
  public ObjectProperty<NetworkInterfaceType> networkInterfaceTypeProperty() {
    return networkInterfaceType;
  }

  @Override
  public MultimediaInterfaceType getMultimediaInterfaceType() {
    return multimediaInterfaceType.get();
  }

  @Override
  public void setMultimediaInterfaceType(MultimediaInterfaceType type) {}

  @Override
  public ObjectProperty<MultimediaInterfaceType> multimediaInterfaceTypeProperty() {
    return multimediaInterfaceType;
  }

  @Override
  public boolean canSeperate() {
    return false;
  }

  @Override
  public void seperate(boolean sep) {}

  @Override
  public String getNodeType() {
    return SystemEditDefinition.TERMINAL_CELL;
  }

  @Override
  public int getPortCount() {
    return 1;
  }

  @Override
  public ObservableList<VisualEditTerminal> getAllTerminalChild() {
    ObservableList<VisualEditTerminal> result = FXCollections.observableArrayList();
    result.add(this);
    return result;
  }

  @Override
  public ObservableList<VisualEditFunc> getAllFunctions() {
    return FXCollections.observableArrayList();
  }

  @Override
  public void init() {}

  @Override
  public ObservableList<ConnectorIdentifier> getConnectorId() {
    return connectorIds;
  }

  @Override
  public boolean isRx() {
    return true;
  }
}
