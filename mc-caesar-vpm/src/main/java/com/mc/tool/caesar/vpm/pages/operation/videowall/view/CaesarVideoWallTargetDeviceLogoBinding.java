package com.mc.tool.caesar.vpm.pages.operation.videowall.view;

import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminalWrapper;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.view.SystemeditConstants;
import javafx.beans.binding.ObjectBinding;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;

/**
 * .
 */
public class CaesarVideoWallTargetDeviceLogoBinding extends ObjectBinding<ImageView> {

  private final VisualEditTerminal terminal;
  private int index;

  /** . */
  public CaesarVideoWallTargetDeviceLogoBinding(VisualEditTerminal terminal) {
    this.terminal = terminal;
    if (terminal instanceof CaesarCpuTerminalWrapper) {
      index = ((CaesarCpuTerminalWrapper) terminal).getIndex();
    } else {
      index = 0;
    }
    super.bind(terminal.targetDeviceTypeProperty(), terminal.targetDeviceConnectedProperty(index));
  }

  @Override
  protected ImageView computeValue() {
    String offlineLogo =
        SystemeditConstants.RESOURCE_PATH_SHORT
            + (terminal.isTargetDeviceConnected(index)
                ? getLogo()
                : getLogo().replaceAll("online", "offline"));
    ImageView logo =
        new ImageView(
            new Image(
                Thread.currentThread().getContextClassLoader().getResourceAsStream(offlineLogo)));
    return logo;
  }

  protected String getLogo() {
    if (terminal.getTargetDeviceType() == null) {
      return "";
    }
    switch (terminal.getTargetDeviceType()) {
      case DVD:
        return SystemeditConstants.DVD_ONLINE_LOGO;
      case COMPUTER:
        return SystemeditConstants.COMPUTER_ONLINE_LOGO;
      case MONITOR:
        return SystemeditConstants.MONITOR_ONLINE_LOGO;
      case PROJECTOR:
        return SystemeditConstants.PROJECTOR_ONLINE_LOGO;
      default:
        return "";
    } // end switch
  }
}
