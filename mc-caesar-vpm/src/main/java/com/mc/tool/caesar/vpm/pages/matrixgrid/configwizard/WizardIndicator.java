package com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard;

import java.util.Collection;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.control.Label;
import javafx.scene.control.Tooltip;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import javafx.scene.shape.Circle;
import javafx.scene.shape.Rectangle;

/**
 * .
 */
public class WizardIndicator extends HBox {

  private ObservableList<String> items = FXCollections.observableArrayList();
  private IntegerProperty currentIndex = new SimpleIntegerProperty(-1);

  /** . */
  public WizardIndicator() {
    items.addListener((ListChangeListener<String>) change -> updateView());

    currentIndex.addListener((obs, oldVal, newVal) -> updateSelectedItem());
  }

  public IntegerProperty currentIndexProperty() {
    return currentIndex;
  }

  public void setItems(Collection<String> items) {
    this.items.setAll(items);
  }

  public void setCurrentIndex(int currentIndex) {
    this.currentIndex.set(currentIndex);
  }

  public int getCurrentIndex() {
    return currentIndex.get();
  }

  protected void updateView() {
    getChildren().clear();
    for (String item : items) {
      IndicatorItem indicatorItem = new IndicatorItem();
      indicatorItem.setPrefWidth(Integer.MAX_VALUE);
      indicatorItem.setName(item);
      getChildren().add(indicatorItem);
    }

    updateSelectedItem();
  }

  protected void updateSelectedItem() {
    int index = currentIndex.get();
    for (int i = 0; i < getChildren().size(); i++) {
      Node node = getChildren().get(i);
      if (node instanceof IndicatorItem) {
        IndicatorStatus status;
        if (i < index) {
          status = IndicatorStatus.BEFORE;
        } else if (i == index) {
          status = IndicatorStatus.CURRENT;
        } else {
          status = IndicatorStatus.AFTER;
        }
        ((IndicatorItem) node).setStatus(status);
      }
    }
  }

  enum IndicatorStatus {
    BEFORE,
    CURRENT,
    AFTER
  }

  static class IndicatorItem extends VBox {

    private IndicatorGraphic graphic = new IndicatorGraphic();
    private Label name = new Label();

    public IndicatorItem() {
      setAlignment(Pos.TOP_CENTER);
      setSpacing(10);

      HBox box = new HBox();
      box.setAlignment(Pos.CENTER);
      box.getChildren().add(name);

      Tooltip tooltip = new Tooltip();
      tooltip.textProperty().bind(name.textProperty());
      name.setTooltip(tooltip);
      this.getChildren().addAll(graphic, box);
    }

    public void setName(String text) {
      name.setText(text);
    }

    public void setStatus(IndicatorStatus status) {
      graphic.setStatus(status);
    }
  }

  static class IndicatorGraphic extends StackPane {

    private static final int HEIGHT = 12;
    private static final int NORMAL_RADIUS = 4;
    private static final int SELECTED_RADIUS = 6;
    private static final Color HIGHLIGHT_COLOR = Color.web("#f08519");
    private static final Color NORMAL_COLOR = Color.web("#c4c4c4");
    private Circle circle;
    private Rectangle left;
    private Rectangle right;

    private ObjectProperty<IndicatorStatus> status = new SimpleObjectProperty<>();

    public IndicatorGraphic() {
      setPrefHeight(HEIGHT);
      setAlignment(Pos.CENTER);

      left = new Rectangle();
      left.setHeight(2);
      left.widthProperty().bind(widthProperty().divide(2));
      left.setFill(HIGHLIGHT_COLOR);
      right = new Rectangle();
      right.setHeight(2);
      right.widthProperty().bind(widthProperty().divide(2));
      right.setFill(HIGHLIGHT_COLOR);
      HBox lineBox = new HBox();
      lineBox.setAlignment(Pos.CENTER);
      lineBox.getChildren().addAll(left, right);
      HBox.setHgrow(left, Priority.ALWAYS);
      HBox.setHgrow(right, Priority.ALWAYS);
      lineBox.setPrefWidth(Integer.MAX_VALUE);

      circle = new Circle();
      circle.setFill(HIGHLIGHT_COLOR);
      circle.setRadius(NORMAL_RADIUS);
      getChildren().addAll(lineBox, circle);

      status.addListener(
          (obs, oldVal, newVal) -> {
            switch (newVal) {
              case BEFORE:
                circle.setRadius(NORMAL_RADIUS);
                circle.setFill(HIGHLIGHT_COLOR);
                left.setFill(HIGHLIGHT_COLOR);
                right.setFill(HIGHLIGHT_COLOR);
                break;
              case CURRENT:
                circle.setRadius(SELECTED_RADIUS);
                circle.setFill(HIGHLIGHT_COLOR);
                left.setFill(HIGHLIGHT_COLOR);
                right.setFill(NORMAL_COLOR);
                break;
              case AFTER:
                circle.setRadius(NORMAL_RADIUS);
                circle.setFill(NORMAL_COLOR);
                left.setFill(NORMAL_COLOR);
                right.setFill(NORMAL_COLOR);
                break;
              default:
                break;
            }
          });
    }

    public void setStatus(IndicatorStatus status) {
      this.status.set(status);
    }
  }
}
