package com.mc.tool.caesar.vpm.device.broadcast;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.vpm.event.GridEvent;
import com.mc.tool.framework.utility.EventBusProvider;
import java.net.InetAddress;

/**
 * .
 */
public class GridBroadcastServer extends BroadcastServerBase {

  private boolean shutdown = false;
  private static GridBroadcastServer INSTANCE = new GridBroadcastServer();

  public static GridBroadcastServer getInstance() {
    return INSTANCE;
  }

  private GridBroadcastServer() {}

  @Override
  public int getPort() {
    return CaesarConstants.GRID_PORT;
  }

  @Override
  public boolean isShutDown() {
    return shutdown;
  }

  @Override
  public void setShutdown(boolean status) {
    shutdown = status;
  }

  @Override
  protected void onReceivedData(byte[] data, int length, InetAddress address) {
    GridEvent event = new GridEvent(data, length, address);
    EventBusProvider.getEventBus().post(event);
  }
}
