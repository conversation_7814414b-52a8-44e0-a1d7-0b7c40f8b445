package com.mc.tool.caesar.vpm.util.macrokey.datamodel;

import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.api.interfaces.Oidable;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;

/**
 * .
 */
public class ConstantParam implements DataObject, Oidable {
  public static final ConstantParam CURRENT_USER_VALUE =
      new ConstantParam(CaesarI18nCommonResource.getString("macro_key.current_user"), 32767);
  public static final ConstantParam LOGOUT_VALUE =
      new ConstantParam(CaesarI18nCommonResource.getString("macro_key.logout"), 0);
  public static final ConstantParam DISCONNECT_VALUE =
      new ConstantParam(CaesarI18nCommonResource.getString("macro_key.disconnected_cpu"), 0);
  public static final ConstantParam CURRENT_CONDEVICE_VALUE =
      new ConstantParam(CaesarI18nCommonResource.getString("macro_key.current_con_device"), 0);

  private final String name;
  private final int id;

  private ConstantParam(String name, int id) {
    this.name = name;
    this.id = id;
  }

  @Override
  public int getId() {
    return id;
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public int getOid() {
    return id - 1;
  }

  @Override
  public void delete() {}

  @Override
  public void delete(boolean paramBoolean) {}
}
