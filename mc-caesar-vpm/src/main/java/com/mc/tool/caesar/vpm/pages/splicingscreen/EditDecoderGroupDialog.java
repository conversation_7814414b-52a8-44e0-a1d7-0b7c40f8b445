package com.mc.tool.caesar.vpm.pages.splicingscreen;

import com.mc.common.beans.SimpleBooleanBinding;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.kaito.model.DecodeGroup;
import com.mc.tool.framework.utility.UndecoratedDialog;
import com.sun.javafx.collections.ObservableListWrapper;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.BitSet;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import javafx.beans.binding.BooleanBinding;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.scene.control.ButtonBar;
import javafx.scene.control.ButtonType;
import javafx.scene.control.CheckBox;
import javafx.scene.control.Control;
import javafx.scene.control.Label;
import javafx.scene.control.TextField;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import org.controlsfx.control.CheckListView;
import org.controlsfx.validation.ValidationResult;
import org.controlsfx.validation.ValidationSupport;
import org.controlsfx.validation.Validator;

/**
 * EditDecoderGroupDialog.
 */
public class EditDecoderGroupDialog extends UndecoratedDialog<NewDecoderGroup> {
  private final TextField gourpNameTextField;
  private final CheckBox croppableCheckBox;
  private final Label message = new Label();
  private final CheckListView<NewDecoder> decodeCardListView;
  private final Integer videowallGroupId;
  private final CaesarDeviceController controller;

  /**
   * EditDecoderGroupDialog.
   */
  public EditDecoderGroupDialog(Integer videowallGroupId, CaesarDeviceController controller) {
    this.videowallGroupId = videowallGroupId;
    this.controller = controller;
    setTitle(Bundle.getMessage("edit_decoder_group_dialog.title"));
    HBox hbox = new HBox();
    Label nameLabel = new Label(Bundle.getMessage("edit_decoder_group_dialog.name_label"));
    gourpNameTextField = new TextField();
    hbox.getChildren().addAll(nameLabel, gourpNameTextField);
    ValidationSupport nameValidationSupport = new ValidationSupport();
    nameValidationSupport.setValidationDecorator(null);
    nameValidationSupport.registerValidator(gourpNameTextField, new StringValidator());
    List<ValidationSupport> validations = new ArrayList<>();
    validations.add(nameValidationSupport);

    croppableCheckBox =
        new CheckBox(Bundle.getMessage("edit_decoder_group_dialog.croppable_check"));

    decodeCardListView = new CheckListView<>();
    ValidationSupport validationSupport = new ValidationSupport();
    validationSupport.setValidationDecorator(null);
    validationSupport.registerValidator(decodeCardListView, new CardListValidator());
    validations.add(validationSupport);
    BooleanBinding validationBinding = new SimpleBooleanBinding(false);
    BooleanProperty invalidSelect = new SimpleBooleanProperty(false);
    validationBinding = validationBinding.or(invalidSelect);
    decodeCardListView.getCheckModel().getCheckedItems().addListener(
        (ListChangeListener<? super NewDecoder>) changed -> {
          ObservableList<NewDecoder> checkedItems =
              decodeCardListView.getCheckModel().getCheckedItems();
          if (checkedItems.isEmpty()) {
            message.setText(Bundle.getMessage("edit_decoder_group_dialog.select_rx"));
            invalidSelect.set(true);
            return;
          }
          long count = checkedItems.stream().map(item -> {
            ConsoleData consoleData =
                controller.getDataModel().getConfigDataManager().getConsoleData4Id(item.getId());
            if (consoleData != null) {
              ExtenderData extenderData = consoleData.getExtenderData(0);
              if (extenderData != null) {
                return extenderData.getExtenderStatusInfo().getVideoResolutionType();
              }
            }
            return CaesarConstants.Extender.ExtenderVideoResolutionType.VIDEO_UNKNOWN;
          }).distinct().count();
          if (count > 1) {
            message.setText(Bundle.getMessage("edit_decoder_group_dialog.type_requirement"));
            invalidSelect.set(true);
          } else {
            message.setText("");
            invalidSelect.set(false);
          }
        });
    for (ValidationSupport support : validations) {
      validationBinding = validationBinding.or(support.invalidProperty());
    }
    validationSupport. validationResultProperty().addListener((observable, oldValue, newValue) -> {
      if (newValue.getErrors().isEmpty()) {
        message.setText("");
      } else {
        newValue.getErrors().stream().findFirst()
            .ifPresent(item -> message.setText(item.getText()));
      }
    });
    getDialogPane().setGraphic(null);
    getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);
    getDialogPane().lookupButton(ButtonType.OK).disableProperty().bind(validationBinding);

    setResultConverter(dialogButton -> {
      ButtonBar.ButtonData data = dialogButton == null ? null : dialogButton.getButtonData();
      if (data == ButtonBar.ButtonData.OK_DONE) {
        NewDecoderGroup group = new NewDecoderGroup();
        group.setName(gourpNameTextField.getText());
        List<Integer> rxList =
            decodeCardListView.getCheckModel().getCheckedItems().stream().map(NewDecoder::getId)
                .collect(Collectors.toList());
        group.setRights(getAvailableRights());
        group.setCroppable(croppableCheckBox.isSelected());
        group.setRxList(rxList);
        return group;
      } else {
        return null;
      }
    });

    VBox content = new VBox();
    content.getChildren().addAll(hbox, croppableCheckBox, decodeCardListView, message);
    getDialogPane().setContent(content);
    initData(videowallGroupId, controller);
  }

  private void initData(Integer videowallGroupId, CaesarDeviceController deviceController) {
    List<NewDecoder> list = deviceController.getKaitoApi()
        .kaitoDecodeCardAvailableVideowallGroupIdGet(videowallGroupId).stream()
        .map(item -> {
          ConsoleData conData =
              controller.getDataModel().getConfigDataManager().getConsoleData4Id(item.getRxId());
          CaesarConstants.Extender.ExtenderVideoResolutionType type =
              CaesarConstants.Extender.ExtenderVideoResolutionType.VIDEO_UNKNOWN;
          if (conData != null) {
            ExtenderData extenderData = conData.getExtenderData(0);
            if (extenderData != null) {
              type = extenderData.getExtenderStatusInfo().getVideoResolutionType();
            }
          }
          return new NewDecoder(item.getRxId(), item.getName(), type.getText());
        }).collect(Collectors.toList());
    decodeCardListView.getItems().addAll(list);
  }

  private String getAvailableRights() {
    Collection<CpuData> activeCpus =
        controller.getDataModel().getConfigDataManager().getActiveCpus();
    BitSet activeSet = new BitSet(2000);
    activeCpus.forEach(cpuData -> {
      if (cpuData != null) {
        activeSet.set(cpuData.getOid());
      }
    });
    List<DecodeGroup> decodeGroups =
        controller.getKaitoApi().kaitoDecodeGroupListVideowallGroupIdGet(videowallGroupId);
    for (DecodeGroup group : decodeGroups) {
      byte[] decodeRights = Base64.getMimeDecoder().decode(group.getRights());
      BitSet hasRights = BitSet.valueOf(decodeRights);
      for (int i = 0; i < hasRights.length(); i++) {
        if (hasRights.get(i) && activeSet.get(i)) {
          activeSet.set(i, false);
        }
      }
    }
    byte[] byteArray = Arrays.copyOf(activeSet.toByteArray(), 250);
    return Base64.getMimeEncoder().encodeToString(byteArray);
  }

  static class StringValidator implements Validator<String> {

    @Override
    public ValidationResult apply(Control target, String value) {
      ValidationResult vd = new ValidationResult();
      if (value.isEmpty()) {
        vd.addErrorIf(target, "empty", true);
      }
      return vd;
    }
  }

  static class CardListValidator implements Validator<ObservableListWrapper<NewDecoder>> {

    @Override
    public ValidationResult apply(Control target, ObservableListWrapper<NewDecoder> value) {
      ValidationResult vd = new ValidationResult();
      if (value.isEmpty()) {
        vd.addErrorIf(target, "empty", true);
      }
      return vd;
    }
  }
}
