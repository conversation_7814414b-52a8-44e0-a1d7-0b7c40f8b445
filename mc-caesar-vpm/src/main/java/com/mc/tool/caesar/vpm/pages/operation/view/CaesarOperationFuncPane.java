package com.mc.tool.caesar.vpm.pages.operation.view;

import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.vpm.pages.operation.controller.CaesarOperationFuncController;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbTxTerminal;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.systemedit.datamodel.EmptyVisualEditTerminal;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.AggregatedObservableArrayList;
import java.io.IOException;
import java.net.URL;
import javafx.beans.property.ObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.control.MultipleSelectionModel;
import javafx.scene.layout.Pane;
import javafx.scene.layout.VBox;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarOperationFuncPane extends VBox {

  private CaesarOperationFuncController controller = new CaesarOperationFuncController();

  protected SwitchFunctional switchFunctional = null;

  protected AggregatedObservableArrayList<VisualEditTerminal> videoSources =
      new AggregatedObservableArrayList<>("videoSources");

  protected ObservableList<VisualEditTerminal> emptyVideoSource =
      FXCollections.observableArrayList(new EmptyVisualEditTerminal("Empty", false));

  protected final VisualEditModel systemEditModel;

  protected WeakAdapter weakAdapter = new WeakAdapter();

  /** Constructor. */
  public CaesarOperationFuncPane(VisualEditModel model) {
    this.systemEditModel = model;
    videoSources.appendList(emptyVideoSource);

    URL location =
        Thread.currentThread()
            .getContextClassLoader()
            .getResource("com/mc/tool/caesar/vpm/pages/operation/operation_func_panel.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    loader.setRoot(this);
    loader.setController(controller);
    loader.setResources(CaesarI18nCommonResource.getResourceBundle());
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load operation_func_panel.xml", exception);
    }
  }

  public ObjectProperty<Node> getFunctionViewContent() {
    return controller.getFuncViewContent();
  }

  public ObservableList<Node> getToolBoxContent() {
    return controller.getToolBoxContent();
  }

  public void setToolBoxContent(ObservableList<Node> list) {
    controller.getToolBoxContent().setAll(list);
  }

  public ObservableList<Node> getSourceListContent() {
    return controller.getSourceListContent();
  }

  public void setSourceListContent(ObservableList<Node> list) {
    controller.getSourceListContent().setAll(list);
  }

  public ObservableList<Node> getPropertyContent() {
    return controller.getPropertyContent();
  }

  public void setPropertyContent(ObservableList<Node> list) {
    controller.getPropertyContent().setAll(list);
  }

  public Node getFunctionViewContainer() {
    return controller.getFunctionViewContainer();
  }

  public ViewControllable getViewControllable() {
    return null;
  }

  public void setOnSwitch(SwitchFunctional functional) {
    this.switchFunctional = functional;
  }

  protected void updateVideoSourceList(VisualEditFunc func) {
    if (func == null) {
      return;
    }
    videoSources.clearList();
    videoSources.appendList(emptyVideoSource);
    ObservableList<VisualEditTerminal> availableList = systemEditModel.getFunctionAvailableTx(func);
    availableList =
        availableList.filtered((terminal) -> !(terminal instanceof CaesarUsbTxTerminal));
    videoSources.appendList(availableList);
  }

  public MultipleSelectionModel<VisualEditTerminal> getSourceSeletionModel() {
    return null;
  }

  public Pane getPreviewContainer() {
    return null;
  }

  public void close() {}

  public void refreshOnShow() {}
}
