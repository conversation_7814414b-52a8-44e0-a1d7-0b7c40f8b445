package com.mc.tool.caesar.vpm.pages.systemedit;

import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.systemedit.controller.CaesarSystemEditController;
import com.mc.tool.caesar.vpm.util.ReloadUtility;
import com.mc.tool.framework.interfaces.Page;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import java.util.concurrent.ExecutionException;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.scene.image.Image;
import javafx.scene.layout.Pane;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarSystemEditPage implements Page {
  private final CaesarSystemEditPageView view;
  private final BooleanProperty visibility = new SimpleBooleanProperty(true);
  public static final String NAME = "systemedit";

  /**
   * Constructor.
   *
   * @param entity entity
   */
  public CaesarSystemEditPage(CaesarEntity entity) {
    view = new CaesarSystemEditPageView(entity.getVisualEditModel());
    view.getControllable().setDeviceController(entity.getController());
    view.getControllable().setEntity(entity);
  }

  public Image getSystemGraphImage() {
    return view.getControllable().getGraph().generateImage();
  }

  @Override
  public String getTitle() {
    return Bundle.NbBundle.getMessage("SystemEditPage.title");
  }

  @Override
  public String getName() {
    return NAME;
  }

  @Override
  public Pane getView() {
    return view;
  }

  @Override
  public String getStyleClass() {
    return "caesar-system-edit-page";
  }

  @Override
  public BooleanProperty getVisibleProperty() {
    return visibility;
  }

  @Override
  public void showObject(Object object) {}

  @Override
  public void close() {
    view.getControllable().close();
  }

  @Override
  public void refresh() {
    SystemEditControllable controllable = view.getControllable();
    if (controllable instanceof CaesarSystemEditController) {
      CaesarSystemEditController caesarSystemEditController =
          (CaesarSystemEditController) controllable;
      CaesarDeviceController deviceController = caesarSystemEditController.getDeviceController();
      if (deviceController == null) {
        return;
      }
      try {
        deviceController
            .submit(() -> ReloadUtility.refreshData(caesarSystemEditController, deviceController))
            .get();
      } catch (ExecutionException | InterruptedException exception) {
        log.warn("Fail to refresh.", exception);
      }
    }
  }

  @Override
  public void onEntityActiveChange(boolean active) {
    view.getControllable().onEntityActiveChange(active);
  }
}
