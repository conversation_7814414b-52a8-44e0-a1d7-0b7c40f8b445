package com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard;

import com.mc.common.util.PlatformUtility;
import com.mc.common.util.RunUtil;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.CaesarSwitchDataModelManager;
import com.mc.tool.caesar.api.datamodel.ConfigData;
import com.mc.tool.caesar.api.datamodel.MatrixData;
import com.mc.tool.caesar.api.datamodel.MatrixGridData;
import com.mc.tool.caesar.api.datamodel.NetworkData;
import com.mc.tool.caesar.api.datamodel.SystemData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.FileTransfer;
import com.mc.tool.caesar.api.utils.FtpSupport;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.activateconfig.ActivateConfigMetaCdm;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridConfigUtility.GridStatus;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridConfigUtility.MatrixData4HostConfig;
import com.mc.tool.caesar.vpm.util.ConfigListUtility;
import com.mc.tool.framework.utility.LogListCell;
import com.mc.tool.framework.utility.UndecoratedWizard;
import com.mc.tool.framework.utility.UndecoratedWizardPane;
import com.mc.tool.framework.utility.ViewLogger;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URL;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import javafx.beans.binding.Bindings;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.control.Button;
import javafx.scene.control.ListView;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class WizardActivate extends UndecoratedWizardPane implements Initializable {
  private static final String ZERO_IP = "s0.0.0.0".substring(1);
  private final CaesarDeviceController deviceController;

  @FXML private TableView<ActivateConfigMetaCdm> tableView;
  @FXML private TableColumn<ActivateConfigMetaCdm, String> fileCol;
  @FXML private TableColumn<ActivateConfigMetaCdm, String> nameCol;
  @FXML private TableColumn<ActivateConfigMetaCdm, String> infoCol;
  @FXML private TableColumn<ActivateConfigMetaCdm, String> ipCol;
  @FXML private TableColumn<ActivateConfigMetaCdm, String> versionCol;
  @FXML private ListView<ViewLogger.LogInfo> logList;
  @FXML private Button activateBtn;

  private ObservableList<ActivateConfigMetaCdm> activateItems = FXCollections.observableArrayList();

  private ObservableList<MatrixData4HostConfig> matrixItems = FXCollections.observableArrayList();
  private String matrixGridName = "";

  private final ConfigurationMerger merger = new ConfigurationMerger();

  private final ViewLogger viewLogger = new ViewLogger();

  private BooleanProperty activating = new SimpleBooleanProperty(false);

  @Getter private boolean hasActivated = false;

  /**
   * .
   *
   * @param deviceController device controller
   */
  public WizardActivate(CaesarDeviceController deviceController) {
    this.deviceController = deviceController;
    FXMLLoader loader = new FXMLLoader();
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    loader.setLocation(
        classLoader.getResource(
            "com/mc/tool/caesar/vpm/pages/matrixgrid/configwizard/activate_view.fxml"));
    loader.setResources(
        ResourceBundle.getBundle("com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.Bundle"));
    loader.setController(this);
    try {
      Node root = loader.load();
      setContent(root);
    } catch (IOException exception) {
      log.warn("Fail to load activate_view.fxml!", exception);
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    fileCol.setCellValueFactory((cell) -> cell.getValue().getFilenameProperty());
    fileCol.setPrefWidth(Integer.MAX_VALUE);

    nameCol.setCellValueFactory((cell) -> cell.getValue().getNameProperty());
    nameCol.setPrefWidth(Integer.MAX_VALUE);

    infoCol.setCellValueFactory((cell) -> cell.getValue().getInfoProperty());
    infoCol.setPrefWidth(Integer.MAX_VALUE);

    ipCol.setCellValueFactory((cell) -> cell.getValue().getAddressProperty());
    ipCol.setPrefWidth(Integer.MAX_VALUE);

    versionCol.setCellValueFactory((cell) -> cell.getValue().getVersionProperty());
    versionCol.setPrefWidth(Integer.MAX_VALUE);

    tableView.setItems(activateItems);
    tableView.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);

    logList.setItems(viewLogger.getLogs());
    logList.setCellFactory((view) -> new LogListCell());

    activateBtn
        .disableProperty()
        .bind(
            Bindings.isEmpty(tableView.getSelectionModel().getSelectedItems())
                .or(Bindings.isEmpty(matrixItems))
                .or(activating));
  }

  @Override
  public void onEnteringPage(UndecoratedWizard wizard) {
    super.onEnteringPage(wizard);

    wizard.invalidProperty().bind(activating);

    deviceController.execute(
        () -> {
          Collection<ActivateConfigMetaCdm> items =
              ConfigListUtility.readConfigList(deviceController.getDataModel());
          PlatformUtility.runInFxThread(() -> activateItems.setAll(items));
        });

    matrixItems.setAll(MatrixGridConfigUtility.getMatrixGridConfigFromWizard(wizard));
    matrixItems.addAll(MatrixGridConfigUtility.getMatrixGridDeleteItemsFromWizard(wizard));
    matrixGridName = MatrixGridConfigUtility.getMatrixGridNameFromWizard(wizard);

    for (MatrixData4HostConfig config : matrixItems) {
      config.setMerge(false);
    }
    if (matrixItems.size() > 1) {
      boolean mergeActive = false;
      for (int i = 1; i < matrixItems.size(); i++) {
        MatrixData4HostConfig config = matrixItems.get(i);
        if (config.gridStatus.get() == GridStatus.NEW) {
          config.setMerge(true);
          mergeActive = true;
        }
      }
      if (mergeActive) {
        matrixItems.get(0).setMerge(true);
      }
    }
  }

  @FXML
  public void onActivate(ActionEvent event) {
    hasActivated = true;
    deviceController.execute(this::activateImpl);
  }

  @FXML
  public void onSaveLog(ActionEvent event) {}

  private void activateImpl() {
    boolean isSuccessful = false;
    PlatformUtility.runInFxThread(() -> activating.set(true));
    try {
      Map<String, CaesarSwitchDataModel> connections = merge();
      viewLogger.info("Activating and restarting grid.");
      int matrixCounter = 0;

      String hostname = null;
      // 在各个设备上激活级联后的配置
      for (Map.Entry<String, CaesarSwitchDataModel> entry : connections.entrySet()) {
        CaesarSwitchDataModel connectionModel = entry.getValue();
        if (deviceController.getDataModel().equals(connectionModel)) {
          hostname = entry.getKey();
        } else {
          connectionModel.activateConfig(tableView.getSelectionModel().getSelectedIndex());
          viewLogger.info("Restarting " + entry.getKey());
          matrixCounter++;
        }
      }
      if (hostname != null) {
        deviceController
            .getDataModel()
            .activateConfig(tableView.getSelectionModel().getSelectedIndex());
        viewLogger.info("Restarting " + hostname);
        matrixCounter++;
      }
      // 等待全部断连
      List<String> disconnectionList = new ArrayList<>();
      int maxLoops = 60;
      while (disconnectionList.size() < matrixCounter && maxLoops != 0) {
        try {
          Thread.sleep(1000L);
          maxLoops--;
        } catch (InterruptedException ex) {
          log.error("Sleep is interrupted!", ex);
        }
        for (Map.Entry<String, CaesarSwitchDataModel> entry : connections.entrySet()) {
          entry.getValue().checkConnectivity();
          if (!entry.getValue().isConnected() && !disconnectionList.contains(entry.getKey())) {
            disconnectionList.add(entry.getKey());
          }
        }
      }
      disconnectionList.clear();
      // 等待全部重连
      int connectCounter = 0;
      maxLoops = 60;
      while (connectCounter < matrixCounter && maxLoops != 0) {
        connectCounter = 0;
        try {
          Thread.sleep(5000L);
          maxLoops--;
        } catch (InterruptedException ex) {
          log.error("Sleep is interrupted!", ex);
        }
        for (Map.Entry<String, CaesarSwitchDataModel> entry : connections.entrySet()) {
          entry.getValue().checkConnectivity();
          if (entry.getValue().isConnected()) {
            connectCounter++;
          }
        }
      }
      for (CaesarSwitchDataModel model : connections.values()) {
        model.getSwitchModuleData().reloadModules();
        model.reloadConfigData();
      }
      if (maxLoops == 0) {
        viewLogger.error("Fail to reconnect matrice!");
      } else {
        viewLogger.success("Configuration successful!");
        viewLogger.success("Recommend to save status.");
      }
      isSuccessful = true;
    } catch (ConfigException | BusyException exception) {
      viewLogger.warn("Fail to activate matrix grid config!", exception);
      isSuccessful = false;
    } finally {
      if (!isSuccessful) {
        viewLogger.error("Configuration failed. Please try again.");
      }
      PlatformUtility.runInFxThread(() -> activating.set(false));
    }
  }

  private Map<String, CaesarSwitchDataModel> merge() throws ConfigException, BusyException {
    Map<String, CaesarSwitchDataModel> externalConnections = new HashMap<>();
    Map<String, CaesarSwitchDataModel> connections = new HashMap<>();

    byte[] ipAddress =
        deviceController
            .getDataModel()
            .getConfigData()
            .getSystemConfigData()
            .getNetworkDataCurrent1()
            .getAddress();
    String hostname = IpUtil.getAddressString(ipAddress);
    CaesarSwitchDataModel destinationModel = deviceController.getDataModel();
    // 先清空matrixdata信息
    for (MatrixData md : destinationModel.getConfigData().getMatrixDatas()) {
      md.initDefaults();
    }
    int portOffset = 0;
    for (MatrixData4HostConfig accessData : matrixItems) {
      String ip = accessData.ip.get();
      if (!ip.equals(ZERO_IP)) {
        CaesarSwitchDataModel externalModel;
        if (ip.equals(hostname)) {
          externalModel = deviceController.getDataModel();
        } else {
          externalModel =
              CaesarSwitchDataModelManager.getInstance().getModel(ip, destinationModel.isDemo());
          externalModel.reloadConfigData();
          externalModel.checkConnectivity();
          externalConnections.put(ip, externalModel);
        }
        connections.put(ip, externalModel);
        if (accessData.isMerge()) {
          if (!destinationModel.equals(externalModel)) {
            viewLogger.info(
                MessageFormat.format("Merge configuration from {0} with {1}", ip, hostname));
            merger.merge(
                destinationModel, externalModel, accessData.keepId.get(), portOffset, viewLogger);
          }
          portOffset += accessData.port.get();
        }
        if (accessData.gridStatus.get() != GridStatus.DELETE) {
          fillMatrixData(destinationModel.getConfigData());
        }
      }
    }

    // 保存额外配置到文件
    int fileType = tableView.getSelectionModel().getSelectedItem().getFileType();
    String tempPath = System.getProperty("java.io.tmpdir");
    String zipFilePath = tempPath + "temp.zip";
    boolean saveExtraConfigSucceed = ConfigListUtility.saveExtraConfig(destinationModel, zipFilePath);

    // 保存合并后的配置在当前设备
    for (MatrixData4HostConfig accessData : matrixItems) {
      if (accessData.ip.get().equals(hostname)) {
        viewLogger.info("Sending data to: " + accessData.ip.get());
        // 上传额外数据
        if (saveExtraConfigSucceed) {
          try (FileInputStream fis = new FileInputStream(zipFilePath)) {
            FileTransfer.write(destinationModel.getController(), fileType | 0xf0, fis);
          } catch (IOException | ConfigException | BusyException exception) {
            log.warn("Fail write extra conf data to device for {} | 0xf0!", fileType, exception);
          }
        }

        String user = accessData.user.get();
        String pwd = accessData.pwd.get();
        String host = accessData.ip.get();
        ActivateConfigMetaCdm cdm = tableView.getSelectionModel().getSelectedItem();
        String filename = cdm.getFilename();
        String url = FtpSupport.createUrl(host, user, pwd, filename, "utf-8");
        destinationModel.writeFtp(url);
      }
    }
    // 保存合并后的配置到其他设备
    for (MatrixData4HostConfig accessData : matrixItems) {
      String ip = accessData.ip.get();
      if (!ip.equals(hostname) && externalConnections.containsKey(ip)) {
        CaesarSwitchDataModel model = externalConnections.get(ip);
        CaesarSwitchDataModel mergedModel = destinationModel;
        if (accessData.gridStatus.get() == GridStatus.DELETE) {
          viewLogger.info("Removing matrix: " + ip);
          removeFromGrid(model.getConfigData());
          mergedModel = model;
        } else {
          updateSystemData(
              mergedModel.getConfigData().getSystemConfigData().getSystemData(), accessData);
          RunUtil.waitForUiDone(() -> copyNetworkData(model, destinationModel));
        }
        viewLogger.info("Sending data to: " + ip);
        // 上传vp数据
        if (saveExtraConfigSucceed) {
          try (FileInputStream fis = new FileInputStream(zipFilePath)) {
            FileTransfer.write(model.getController(), fileType | 0xf0, fis);
          } catch (IOException | ConfigException | BusyException exception) {
            log.warn("Fail write vp data to device for {} | 0xf0!", fileType, exception);
          }
        }

        String user = accessData.user.get();
        String pwd = accessData.pwd.get();
        String host = ip;
        ActivateConfigMetaCdm cdm = tableView.getSelectionModel().getSelectedItem();
        String filename = cdm.getFilename();
        String url = FtpSupport.createUrl(host, user, pwd, filename, "utf-8");
        mergedModel.writeFtp(url);
      }
    }
    externalConnections.clear();
    return connections;
  }

  /**
   * 保存配置到MatrixData.
   *
   * @param configData 配置数据
   */
  private void fillMatrixData(ConfigData configData) {
    int index = 0;

    byte[] ipAddress = configData.getSystemConfigData().getNetworkDataCurrent1().getAddress();
    String hostname = IpUtil.getAddressString(ipAddress);
    MatrixGridData matrixGridData = configData.getSystemConfigData().getMatrixGridData();
    matrixGridData.setMatrixGridEnabled(true);
    for (MatrixData4HostConfig accessData : matrixItems) {
      if (accessData.gridStatus.get() != GridStatus.DELETE) {
        if (hostname.equals(accessData.ip.get())) {
          updateSystemData(configData.getSystemConfigData().getSystemData(), accessData);
        }
        MatrixData matrixData = configData.getMatrixData(index);
        matrixData.setStatusActive(true);
        matrixData.setDevice(accessData.name.get());
        matrixData.setPortCount(accessData.port.get());

        try {
          CaesarSwitchDataModel model =
              CaesarSwitchDataModelManager.getInstance().getModel(accessData.ip.get(), false);
          model.getSwitchModuleData().reloadModules();
          matrixData.setMatrixType(model.getSwitchModuleData().getModuleData(0).getType());
        } catch (ConfigException | BusyException | RuntimeException exception) {
          log.warn("Fail to get model for {}.", accessData.ip.get(), exception);
        }

        index++;
      }
    }
  }

  private void updateSystemData(SystemData systemData, MatrixData4HostConfig accessData) {
    systemData.setDevice(accessData.name.get());
    systemData.setName(matrixGridName);
    systemData.setSynchronize(false);
    systemData.setEchoOnly(false);
  }

  private void removeFromGrid(ConfigData configData) {
    MatrixGridData matrixData = configData.getSystemConfigData().getMatrixGridData();
    matrixData.setMatrixGridEnabled(false);
  }

  private void copyNetworkData(CaesarSwitchDataModel srcModel, CaesarSwitchDataModel dstModel) {
    final NetworkData srcNetworkData1 =
        srcModel.getConfigData().getSystemConfigData().getNetworkDataPreset1();
    final NetworkData dstNetworkData1 =
        dstModel.getConfigData().getSystemConfigData().getNetworkDataPreset1();

    final NetworkData srcNetworkData2 =
        srcModel.getConfigData().getSystemConfigData().getNetworkDataPreset2();
    final NetworkData dstNetworkData2 =
        dstModel.getConfigData().getSystemConfigData().getNetworkDataPreset2();

    dstNetworkData1.setDhcp(srcNetworkData1.isDhcp());
    dstNetworkData1.setAddress(srcNetworkData1.getAddress());
    dstNetworkData1.setNetmask(srcNetworkData1.getNetmask());
    dstNetworkData1.setGateway(srcNetworkData1.getGateway());

    dstNetworkData2.setDhcp(srcNetworkData2.isDhcp());
    dstNetworkData2.setAddress(srcNetworkData2.getAddress());
    dstNetworkData2.setNetmask(srcNetworkData2.getNetmask());
    dstNetworkData2.setGateway(srcNetworkData2.getGateway());
  }
}
