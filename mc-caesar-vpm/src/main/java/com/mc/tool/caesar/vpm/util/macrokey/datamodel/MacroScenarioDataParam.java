package com.mc.tool.caesar.vpm.util.macrokey.datamodel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import lombok.Data;

/**
 * .
 */
@Data
public class MacroScenarioDataParam implements MacroParam {
  private int index;
  private String scenarioName;
  private List<MacroParam> children = new ArrayList<>();

  MacroScenarioDataParam(int index, String name) {
    this.index = index;
    this.scenarioName = name;
  }

  @Override
  public Collection<MacroParam> getChildren() {
    return children;
  }

  @Override
  public void addChild(MacroParam param) {
    children.add(param);
  }

  @Override
  public void removeChild(MacroParam param) {
    children.remove(param);
  }

  @Override
  public String getFullName() {
    return String.format("[%d] %s", index, scenarioName);
  }

  @Override
  public boolean hasOid() {
    return false;
  }

  @Override
  public int getOid() {
    return -1;
  }
}
