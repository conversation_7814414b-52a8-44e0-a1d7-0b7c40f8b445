package com.mc.tool.caesar.vpm.userright;

import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarVideoWallFunc;
import com.mc.tool.framework.systemedit.datamodel.VideoWallFunc;

/**
 * .
 */
public class CaesarCommonUserRight extends CaesarAdminUserRight {

  protected int videoWallRights;

  public CaesarCommonUserRight(int videoWallRights) {
    this.videoWallRights = videoWallRights;
  }

  @Override
  public boolean isMultiScreenCreateDeletable() {
    return false;
  }

  @Override
  public boolean isCrossScreenCreateDeletable() {
    return false;
  }

  @Override
  public boolean isVideoWallConnectable(VideoWallFunc func) {
    return isVideoWallEditable(func);
  }

  @Override
  public boolean isVideoWallCreateDeletable() {
    return false;
  }

  @Override
  public boolean isVideoWallLayoutEditable(VideoWallFunc func) {
    return isVideoWallEditable(func);
  }

  @Override
  public boolean isVideoWallScenarioActivatable(VideoWallFunc func) {
    return isVideoWallEditable(func);
  }

  @Override
  public boolean isVideoWallScenarioCreateDeletable(VideoWallFunc func) {
    return isVideoWallEditable(func);
  }

  @Override
  public boolean isVideoWallWindowCreateDeletable(VideoWallFunc func) {
    return isVideoWallEditable(func);
  }

  @Override
  public boolean isVideoWallWindowMovablale(VideoWallFunc func) {
    return isVideoWallEditable(func);
  }

  @Override
  public boolean isVideoWallWindowResizable(VideoWallFunc func) {
    return isVideoWallEditable(func);
  }

  @Override
  public boolean isVideoWallEditable(VideoWallFunc func) {
    if (func instanceof CaesarVideoWallFunc) {
      CaesarVideoWallFunc videoWallFunc = (CaesarVideoWallFunc) func;
      int videoWallIndex = videoWallFunc.getVideoWallIndex();
      if (videoWallIndex < 0) {
        return false;
      }
      return (videoWallRights & (1 << videoWallIndex)) > 0;
    } else {
      return false;
    }
  }
}
