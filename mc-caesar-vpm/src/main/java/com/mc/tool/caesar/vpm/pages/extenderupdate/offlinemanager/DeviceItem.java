package com.mc.tool.caesar.vpm.pages.extenderupdate.offlinemanager;

import com.mc.tool.caesar.api.interfaces.DataObject;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public class DeviceItem<T extends DataObject> {
  @Getter private BooleanProperty selected = new SimpleBooleanProperty();

  @Getter @Setter private T data = null;
}
