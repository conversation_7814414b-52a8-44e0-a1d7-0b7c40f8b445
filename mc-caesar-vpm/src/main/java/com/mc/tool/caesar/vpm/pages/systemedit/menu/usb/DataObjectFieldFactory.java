package com.mc.tool.caesar.vpm.pages.systemedit.menu.usb;

import com.dooapp.fxform.view.FXFormNode;
import com.dooapp.fxform.view.FXFormNodeWrapper;
import com.mc.tool.caesar.api.interfaces.DataObject;
import javafx.scene.control.ComboBox;
import javafx.util.Callback;
import javafx.util.StringConverter;

/**
 * .
 */
public class DataObjectFieldFactory implements Callback<Void, FXFormNode> {

  @Override
  public FXFormNode call(Void param) {
    ComboBox<DataObject> comboBox = new ComboBox<>();
    comboBox.setConverter(new DataObjectStringConverter());
    return new FXFormNodeWrapper(comboBox, comboBox.valueProperty());
  }

  static class DataObjectStringConverter extends StringConverter<DataObject> {

    @Override
    public String toString(DataObject object) {
      return object.getName();
    }

    @Override
    public DataObject fromString(String string) {
      return null;
    }
  }
}
