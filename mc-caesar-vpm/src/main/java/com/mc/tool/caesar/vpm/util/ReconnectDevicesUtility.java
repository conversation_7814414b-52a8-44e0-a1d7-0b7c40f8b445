package com.mc.tool.caesar.vpm.util;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.datamodel.SystemConfigData;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.utility.TypeWrapper;
import com.mc.tool.framework.utility.UndecoratedAlert;
import java.util.concurrent.atomic.AtomicBoolean;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.scene.control.AlertEx.AlertExType;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class ReconnectDevicesUtility {

  private static final AtomicBoolean STOPPED_WAITING = new AtomicBoolean(false);
  private static final String FACTORY_IP = "s192.168.2.2".substring(1);

  /** 等待重连. */
  public static void waitingForReconnection(
      ApplicationBase app,
      Entity entity,
      CaesarDeviceController deviceController,
      Boolean isReset) {
    Task<Void> task =
        new Task<Void>() {
          @Override
          protected Void call() throws Exception {
            this.updateMessage(CaesarI18nCommonResource.getString("alert.task.message"));
            // 等待断连
            int waitStopCount = 0;
            while (deviceController.getDataModel().isConnected()
                && !STOPPED_WAITING.get()
                && waitStopCount < 60) {
              try {
                Thread.sleep(1000L);
              } catch (InterruptedException ex) {
                log.error("thread sleep error");
              }
              waitStopCount++;
            }
            log.info("disconnected");
            int loopCounter = 0;
            // 等待重新连接
            while (!deviceController.getDataModel().isConnected()
                && loopCounter < 600
                && !STOPPED_WAITING.get()) {
              try {
                Thread.sleep(1000L);
              } catch (InterruptedException ex) {
                log.error("thread sleep error");
              }
              loopCounter++;
              log.info("waiting for reconnect ...");
            }
            log.info("reconnected");
            if (!STOPPED_WAITING.get() && loopCounter == 600) {
              throw new Exception("connect failed");
            }
            log.info("reconnected");
            if (entity != null) {
              PlatformUtility.runInFxThread(
                  () -> entity.onMenu(new TypeWrapper("", CaesarEntity.MENU_REFRESH, "")));
            }
            return null;
          }
        };
    SystemConfigData systemConfigData =
        deviceController.getDataModel().getConfigData().getSystemConfigData();
    String ipAddress =
        systemConfigData.getNetworkDataPreset1().isDhcp()
            ? IpUtil.getAddressString(systemConfigData.getNetworkDataCurrent1().getAddress())
            : IpUtil.getAddressString(systemConfigData.getNetworkDataPreset1().getAddress());
    if (isReset && !ipAddress.equals(FACTORY_IP)) {
      Platform.runLater(
          () -> {
            UndecoratedAlert rebootAlert = new UndecoratedAlert(AlertExType.INFORMATION);
            rebootAlert.initOwner(app.getMainWindow());
            rebootAlert.setHeaderText(null);
            rebootAlert.setTitle(CaesarI18nCommonResource.getString("alert.reboot_alert.title"));
            rebootAlert.setContentText(
                CaesarI18nCommonResource.getString("alert.reboot_alert.text") + FACTORY_IP);
            rebootAlert.showAndWait();
          });
      return;
    }
    if (isReset || deviceController.getDefaultIp().equals(ipAddress)) {
      app.getTaskManager().addForegroundTask(task);
    } else {
      Platform.runLater(
          () -> {
            UndecoratedAlert rebootAlert = new UndecoratedAlert(AlertExType.INFORMATION);
            rebootAlert.initOwner(app.getMainWindow());
            rebootAlert.setHeaderText(null);
            rebootAlert.setTitle(CaesarI18nCommonResource.getString("alert.reboot_alert.title"));
            rebootAlert.setContentText(
                CaesarI18nCommonResource.getString("alert.reboot_alert.text") + ipAddress);
            rebootAlert.showAndWait();
          });
    }
  }
}
