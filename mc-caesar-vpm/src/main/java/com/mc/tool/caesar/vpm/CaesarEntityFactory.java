package com.mc.tool.caesar.vpm;

import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.vpm.device.FileUserInfoFactory;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.util.ConnectDeviceUtility;
import com.mc.tool.caesar.vpm.util.SearchDevicesUtility;
import com.mc.tool.framework.DefaultEntityFactory;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.TypeWrapper;
import java.io.File;
import java.util.Arrays;
import java.util.Collection;
import java.util.function.Consumer;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarEntityFactory extends DefaultEntityFactory {
  public static final String CAESAR_CONNECT_TYPE = "device.connect.caesar";
  public static final String CAESAR_SEARCH_TYPE = "device.search.caesar";
  public static final String CAESAR_CONNECT_WITH_IP_TYPE = "device.connect.caesar.ip";
  private final ApplicationBase app;

  public CaesarEntityFactory(ApplicationBase app) {
    this.app = app;
  }

  @Override
  public Collection<TypeWrapper> getSupportEntityTypes() {
    TypeWrapper wrapper =
        new TypeWrapper(
            I18nUtility.getI18nBundle("main").getString("framework.menu.connect"),
            CAESAR_CONNECT_TYPE,
            "");
    TypeWrapper wrapper2 =
        new TypeWrapper(
            I18nUtility.getI18nBundle("main").getString("framework.menu.search"),
            CAESAR_SEARCH_TYPE,
            "");
    return Arrays.asList(wrapper, wrapper2);
  }

  @Override
  public boolean isSupportEntity(String type) {
    return type.equals(CAESAR_CONNECT_TYPE) || type.equals(CAESAR_SEARCH_TYPE);
  }

  @Override
  public void createEntity(String type, Consumer<Entity> postAction) {
    switch (type) {
      case CAESAR_SEARCH_TYPE:
        SearchDevicesUtility.searchForDevices(app, postAction);
        break;
      case CAESAR_CONNECT_TYPE:
        ConnectDeviceUtility.connectToDevice(app, postAction, "");
        break;
      default:
        break;
    }
    if (type.startsWith(CAESAR_CONNECT_WITH_IP_TYPE)) {
      // 获取ip信息，连接
      String[] items = type.split(" ");
      if (items.length > 1) {
        ConnectDeviceUtility.connectToDevice(app, postAction, items[1]);
      }
    }
  }

  @Override
  public void createEntity(File file, Consumer<Entity> postAction) {
    ConnectDeviceUtility.loadFile(
        app,
        file,
        postAction,
        InjectorProvider.getInjector().getInstance(FileUserInfoFactory.class).createInputInfo());
  }

  @Override
  public boolean closeEntity(Entity entity) {
    if (entity instanceof CaesarEntity
        && ((CaesarEntity) entity).getController() != null
        && ((CaesarEntity) entity).getController().getDataModel() != null) {
      CaesarDeviceController controller = ((CaesarEntity) entity).getController();
      final CaesarSwitchDataModel dataModel = controller.getDataModel();
      Thread thread =
          new Thread(
              () -> {
                dataModel.closeExternalConnection();
                dataModel.closeConnection();
              });
      thread.setDaemon(true);
      thread.start();
      log.info("logout, ip: {}, user: {}", controller.getDefaultIp(), controller.getLoginUser());
    }
    return entity.close();
  }
}
