package com.mc.tool.caesar.vpm.pages.systemedit.datamodel;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.framework.systemedit.datamodel.MultimediaInterfaceType;
import java.lang.ref.WeakReference;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarConTerminal extends CaesarTerminalBase {
  private final ObjectProperty<WeakReference<ConsoleData>> conDataProperty =
      new SimpleObjectProperty<>(new WeakReference<>(null));

  /**
   * Constructor.
   *
   * @param consoleData console data
   */
  public CaesarConTerminal(ConsoleData consoleData) {
    setConsoleData(consoleData);
  }

  @Override
  public void reset() {
    super.reset();
    ConsoleData readConsoleData = conDataProperty.get().get();
    if (readConsoleData != null && readConsoleData.getNameProperty() != null) {
      nameProperty.unbindBidirectional(readConsoleData.getNameProperty());
    }
  }

  /**
   * 设置console数据.
   *
   * @param consoleData console数据
   */
  public void setConsoleData(ConsoleData consoleData) {
    ConsoleData oldConsoleData = this.conDataProperty.get().get();
    this.conDataProperty.set(new WeakReference<>(consoleData));

    ExtenderData extenderData = null;

    if (consoleData != null) {
      if (oldConsoleData != null && oldConsoleData.getNameProperty() != null) {
        nameProperty.unbindBidirectional(oldConsoleData.getNameProperty());
      }
      nameProperty.bindBidirectional(consoleData.getNameProperty());
      extenderData = consoleData.getExtenderData(0);
      if (extenderData == null) {
        log.warn("Empty extenderdata!");
      } else {
        idProperty.set(extenderData.getId());
      }
    }

    onlineProperty.set(
        consoleData != null
            && extenderData != null
            && extenderData.isStatusActive()
            && (extenderData.getPort() != 0 || extenderData.getRdPort() != 0));

    targetDeviceConnected.set(
        extenderData != null
            && extenderData.getExtenderStatusInfo().getVideoInput().getChStatus(0));
    targetDeviceConnected2.set(
        extenderData != null
            && extenderData.getExtenderStatusInfo().getVideoInput().getChStatus(1));
    port1ConnectedProperty.set(onlineProperty.get() && extenderData.getPort() != 0);
    port2ConnectedProperty.set(onlineProperty.get() && extenderData.getRdPort() != 0);

    if (extenderData != null) {
      if (extenderData.getExtenderStatusInfo().getSpecialExtType()
          == CaesarConstants.Extender.SpecialExtenderType.HW_ATC
          && extenderData.getExtenderStatusInfo().getHwSpecial().isIcron()) {
        multimediaInterfaceType.set(MultimediaInterfaceType.USB);
      } else if (extenderData.getExtenderStatusInfo().getInterfaceType()
          == CaesarConstants.Extender.ExtenderInterfaceType.INTERFACE_TYPE_DVI) {
        multimediaInterfaceType.set(MultimediaInterfaceType.DVI);
      } else if (extenderData.getExtenderStatusInfo().getInterfaceType()
          == CaesarConstants.Extender.ExtenderInterfaceType.INTERFACE_TYPE_DP) {
        multimediaInterfaceType.set(MultimediaInterfaceType.DP);
      } else {
        multimediaInterfaceType.set(MultimediaInterfaceType.HDMI);
      }
    }

    updateConnectors();
  }

  public CaesarConTerminal() {}

  @Override
  public boolean isRx() {
    return true;
  }

  @Override
  public boolean isTx() {
    return false;
  }

  public boolean isVp() {
    ConsoleData realConsoleData = conDataProperty.get().get();
    return isValid() && realConsoleData != null && realConsoleData.isStatusVpcon();
  }

  @Override
  public ExtenderData getExtenderData() {
    ConsoleData realConsoleData = conDataProperty.get().get();
    if (isValid() && realConsoleData != null) {
      return realConsoleData.getExtenderData(0);
    } else {
      return null;
    }
  }

  public ConsoleData getConsoleData() {
    return conDataProperty.get().get();
  }

  public ObjectProperty<WeakReference<ConsoleData>> conDataProperty() {
    return conDataProperty;
  }

  @Override
  public boolean canSeperate() {
    return getExtenderData() != null
        && getExtenderData().getExtenderStatusInfo().getInterfaceCount() > 1;
  }
}
