package com.mc.tool.caesar.vpm.pages.splicingscreen;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.kaito.model.DecodeCard;
import com.mc.tool.caesar.vpm.kaito.model.DecodeGroup;
import com.mc.tool.framework.utility.UndecoratedDialog;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.BitSet;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.control.ButtonBar;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;
import org.controlsfx.control.ListSelectionViewEx;

/**
 * AssignPermissionsDialog.
 */
public class AssignPermissionsDialog extends UndecoratedDialog<DecodeGroup> {

  private DecodeGroup result;
  private CaesarConstants.Extender.ExtenderVideoResolutionType videoResolutionType =
      CaesarConstants.Extender.ExtenderVideoResolutionType.VIDEO_UNKNOWN;
  private final int videowallGroupId;
  private final DecodeGroup decodeGroup;
  private final CaesarDeviceController controller;
  private final ListSelectionViewEx<CpuData> listSelectionView = new ListSelectionViewEx<>();
  private final ObservableList<CpuData> cpuDataSourceList = FXCollections.observableArrayList();
  private final ObservableList<CpuData> cpuDataTargetList = FXCollections.observableArrayList();

  private Label idLabel;
  private Label nameLabel;
  private Label croppableLabel;

  /**
   * Constructor.
   */
  public AssignPermissionsDialog(int videowallGroupId, DecodeGroup decodeGroup,
                                 CaesarDeviceController controller) {
    this.videowallGroupId = videowallGroupId;
    this.decodeGroup = decodeGroup;
    this.controller = controller;
    setTitle(Bundle.getMessage("assign_permissions_dialog.decoder_group_configuration"));
    initListSelectionView();
    VBox content = new VBox();
    content.setSpacing(10);
    content.getChildren().addAll(getDecodeGroupInfo(), listSelectionView);
    getDialogPane().setContent(content);
    getDialogPane().setGraphic(null);
    getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);
    setResultConverter(dialogButton -> {
      ButtonBar.ButtonData data = dialogButton == null ? null : dialogButton.getButtonData();
      if (data == ButtonBar.ButtonData.OK_DONE) {
        BitSet activeBitSet = new BitSet(2000);
        listSelectionView.getTargetItems().forEach(cd -> activeBitSet.set(cd.getOid()));
        byte[] byteArray = Arrays.copyOf(activeBitSet.toByteArray(), 250);
        String encodeToString = Base64.getMimeEncoder().encodeToString(byteArray);
        result.setRights(encodeToString);
        return result;
      } else {
        return null;
      }
    });
    controller.execute(this::initData);
  }

  private void initListSelectionView() {
    Label targetHeaderLabel =
        new Label(Bundle.getMessage("assign_permissions_selection_view.target_header"));
    HBox targetHeaderBox = new HBox(targetHeaderLabel);
    targetHeaderBox.getStyleClass().add("title-box");
    targetHeaderBox.setStyle(
        "-fx-background-color:#dbf1ef;-fx-border-width:1px;-fx-border-color:#e5e5e5;");
    listSelectionView.setTargetHeader(targetHeaderBox);
    Label sourceHeaderLabel =
        new Label(Bundle.getMessage("assign_permissions_selection_view.source_header"));
    HBox sourceHeaderBox = new HBox(sourceHeaderLabel);
    sourceHeaderBox.getStyleClass().add("title-box");
    sourceHeaderBox.setStyle("-fx-border-width:1px;-fx-border-color:#e5e5e5;");
    listSelectionView.setSourceHeader(sourceHeaderBox);
    listSelectionView.setSourceItems(cpuDataSourceList);
    listSelectionView.setTargetItems(cpuDataTargetList);
    listSelectionView.setCellFactory(col -> new SelectionViewCell());
    listSelectionView.setStyle("-fx-border-width:1px;-fx-border-color:#e5e5e5;");
  }

  private HBox getDecodeGroupInfo() {
    HBox idBox = new HBox();
    Region idRegion = new Region();
    idRegion.setPrefWidth(30);
    idBox.getChildren().addAll(new Label("ID:"), idLabel = new Label());
    HBox nameBox = new HBox();
    Region nameRegion = new Region();
    nameRegion.setPrefWidth(30);
    nameBox.getChildren()
        .addAll(new Label(Bundle.getMessage("assign_permissions_dialog.name_label")),
            nameLabel = new Label());
    HBox croppableBox = new HBox();
    croppableBox.getChildren()
        .addAll(new Label(Bundle.getMessage("assign_permissions_dialog.croppable_check")),
            croppableLabel = new Label());
    HBox infoBox = new HBox();
    infoBox.getChildren().addAll(idBox, idRegion, nameBox, nameRegion, croppableBox);
    return infoBox;
  }

  private void initData() {
    Collection<CpuData> activeCpus =
        controller.getDataModel().getConfigDataManager().getActiveCpus();
    List<CpuData> currentCpu = new ArrayList<>();
    List<CpuData> usedCpu = new ArrayList<>();
    List<DecodeGroup> decodeGroups =
        controller.getKaitoApi().kaitoDecodeGroupListVideowallGroupIdGet(videowallGroupId);
    refreshDecodeGroupInfo(decodeGroups);
    for (DecodeGroup group : decodeGroups) {
      byte[] decodeRights = Base64.getMimeDecoder().decode(group.getRights());
      BitSet hasRights = BitSet.valueOf(decodeRights);
      List<CpuData> hasRightCpus =
          activeCpus.stream().filter(cpuData -> hasRights.get(cpuData.getOid())).collect(
              Collectors.toList());
      if (Objects.equals(group.getId(), this.decodeGroup.getId())) {
        result = group;
        //2K RX解码组只能有2K TX的权限，4K30 RX解码组只能有2K TX与4K30 TX的权限，4K60 RX解码组可以有所有类型TX的权限
        setResolutionTypeLimit(group);
        List<CpuData> collect = hasRightCpus.stream().filter(item -> {
          ExtenderData extenderData = item.getExtenderData(0);
          if (extenderData != null) {
            CaesarConstants.Extender.ExtenderVideoResolutionType type =
                extenderData.getExtenderStatusInfo().getVideoResolutionType();
            return type.getValue() <= videoResolutionType.getValue();
          }
          return false;
        }).collect(Collectors.toList());
        currentCpu.addAll(collect);
      } else {
        usedCpu.addAll(hasRightCpus);
      }
    }
    List<CpuData> idleCpu = activeCpus.stream()
        .filter(cpuData -> {
          ExtenderData extenderData = cpuData.getExtenderData(0);
          if (extenderData != null) {
            CaesarConstants.Extender.ExtenderVideoResolutionType type =
                extenderData.getExtenderStatusInfo().getVideoResolutionType();
            return type.getValue() <= videoResolutionType.getValue() && !usedCpu.contains(cpuData)
                && !currentCpu.contains(cpuData);
          }
          return false;
        })
        .collect(Collectors.toList());
    cpuDataSourceList.addAll(idleCpu);
    cpuDataTargetList.addAll(currentCpu);
  }

  private void refreshDecodeGroupInfo(List<DecodeGroup> decodeGroups) {
    for (DecodeGroup group : decodeGroups) {
      if (Objects.equals(group.getId(), this.decodeGroup.getId())) {
        PlatformUtility.runInFxThread(() -> {
          idLabel.setText(group.getId().toString());
          nameLabel.setText(group.getName());
          croppableLabel.setText(
              group.isCroppable() ? Bundle.getMessage("assign_permissions_dialog.yes") :
                  Bundle.getMessage("assign_permissions_dialog.no"));
        });
      }
    }
  }

  /**
   * 设置解码组的分辨率限制.
   */
  private void setResolutionTypeLimit(DecodeGroup group) {
    if (group.getCards() != null && !group.getCards().isEmpty()) {
      DecodeCard decodeCard = group.getCards().get(0);
      ConsoleData consoleData =
          controller.getDataModel().getConfigDataManager().getConsoleData4Id(decodeCard.getRxId());
      if (consoleData != null) {
        ExtenderData extenderData = consoleData.getExtenderData(0);
        if (extenderData != null) {
          videoResolutionType =
              extenderData.getExtenderStatusInfo().getVideoResolutionType();
        }
      }
    }
  }

  private static class SelectionViewCell extends ListCell<CpuData> {

    @Override
    protected void updateItem(CpuData item, boolean empty) {
      super.updateItem(item, empty);
      if (empty || item == null) {
        setText("");
      } else {
        ExtenderData extenderData = item.getExtenderData(0);
        if (extenderData != null) {
          setText(String.format("[%02d](%s)%s", getIndex() + 1, extenderData.getExtenderStatusInfo()
              .getVideoResolutionType().getText(), item.getName()));
        } else {
          setText(String.format("[%02d]%s", getIndex() + 1, item.getName()));
        }
      }
    }
  }
}
