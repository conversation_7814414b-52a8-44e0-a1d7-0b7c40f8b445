package com.mc.tool.caesar.vpm.util;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.MultiviewData;
import com.mc.tool.caesar.api.datamodel.SwitchData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.datamodel.extargs.ExtDpMode;
import com.mc.tool.caesar.api.utils.ExtendedSwitchUtility;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarGridLineTerminal;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.TypeWrapper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import org.jetbrains.annotations.NotNull;

/**
 * .
 */
public class TerminalUtility {

  public static final String PRIVATE = "PRIVATE";
  public static final String FULL = "FULL";
  public static final String VIDEO = "VIDEO";

  /**
   * 获取TX与RX可连接类型.
   */
  @NotNull
  public static List<TypeWrapper> getConnectableType(CaesarSwitchDataModel dataModel,
                                                     ConsoleData rx, CpuData tx) {
    final ConnectionFlags flags = new ConnectionFlags();
    handleConTerminal(dataModel, rx, tx, flags);

    return buildTypeList(flags);
  }

  /**
   * 获取TX与RX可连接类型.
   */
  @NotNull
  public static List<TypeWrapper> getConnectableType(CaesarDeviceController deviceController,
                                                     VisualEditTerminal rx, VisualEditTerminal tx) {
    final ConnectionFlags flags = new ConnectionFlags();
    if (rx instanceof CaesarConTerminal && tx instanceof CaesarCpuTerminal) {
      handleConTerminal(deviceController, (CaesarConTerminal) rx, (CaesarCpuTerminal) tx, flags);
    } else if (rx instanceof CaesarGridLineTerminal && tx instanceof CaesarCpuTerminal) {
      handleGridLineTerminal(deviceController, (CaesarGridLineTerminal) rx, (CaesarCpuTerminal) tx, flags);
    }

    return buildTypeList(flags);
  }

  private static void handleConTerminal(CaesarDeviceController deviceController,
                                             CaesarConTerminal con, CaesarCpuTerminal cpu,
                                             ConnectionFlags flags) {
    if (con == null || cpu == null || cpu.getExtenderData() == null || con.getExtenderData() == null) {
      resetConnectionFlags(flags);
      return;
    }
    // 预览终端不能连接TX
    if (con.getExtenderData() != null
        && con.getExtenderData().getExtenderStatusInfo().getSpecialExtType()
        == CaesarConstants.Extender.SpecialExtenderType.HW_PREVIEW) {
      resetConnectionFlags(flags);
      return;
    }
    // 4k60TX不能连接非4k60RX
    if (isResolutionIncompatible(cpu.getResolutionType(), con.getResolutionType())) {
      resetConnectionFlags(flags);
      return;
    }
    ExtenderData cpuExt = cpu.getExtenderData();
    ExtenderData conExt = con.getExtenderData();
    // DP模式不一致
    if (cpuExt.getDpModeProperty().getValue() != conExt.getDpModeProperty().getValue()) {
      resetConnectionFlags(flags);
      return;
    }
    // 空管外设不能与其他外设连接
    boolean isCpuHwAtc = cpuExt.getExtenderStatusInfo().getSpecialExtType()
        == CaesarConstants.Extender.SpecialExtenderType.HW_ATC;
    boolean isConHwAtc = conExt.getExtenderStatusInfo().getSpecialExtType()
        == CaesarConstants.Extender.SpecialExtenderType.HW_ATC;
    if (isCpuHwAtc && !isConHwAtc || !isCpuHwAtc && isConHwAtc) {
      resetConnectionFlags(flags);
      return;
    }
    CaesarSwitchDataModel dataModel = deviceController.getDataModel();
    UserData userData = dataModel.getConfigData().getUserData(0);
    flags.fullAllowed =
        ExtendedSwitchUtility.isFullAccessAllowed(dataModel, con.getConsoleData(), cpu.getCpuData(), userData);
    flags.privateAllowed =
        ExtendedSwitchUtility.isPrivateModeAllowed(dataModel, con.getConsoleData(), cpu.getCpuData(), userData);
    flags.videoAllowed =
        ExtendedSwitchUtility.isVideoAccessAllowed(dataModel, con.getConsoleData(), cpu.getCpuData(), userData, null);

  }

  private static void handleConTerminal(CaesarSwitchDataModel dataModel,
                                        ConsoleData con, CpuData cpu,
                                        ConnectionFlags flags) {
    ExtenderData cpuExtenderData = cpu.getExtenderData(0);
    ExtenderData conExtenderData = con.getExtenderData(0);
    if (cpuExtenderData != null && conExtenderData != null) {
      // 4k60TX不能连接非4k60RX
      if (isResolutionIncompatible(cpuExtenderData.getExtenderStatusInfo().getVideoResolutionType(),
          conExtenderData.getExtenderStatusInfo().getVideoResolutionType())) {
        resetConnectionFlags(flags);
        return;
      }
      // 单DP TX不能连接双DP RX
      if (ExtDpMode.SINGLE_DP_MODE.equals(cpuExtenderData.getDpModeProperty().getValue())
          && ExtDpMode.DOUBLE_DP_MODE.equals(conExtenderData.getDpModeProperty().getValue())) {
        resetConnectionFlags(flags);
        return;
      }
      // 双DP TX不能连接单DP RX
      if (ExtDpMode.DOUBLE_DP_MODE.equals(cpuExtenderData.getDpModeProperty().getValue())
          && ExtDpMode.SINGLE_DP_MODE.equals(conExtenderData.getDpModeProperty().getValue())) {
        resetConnectionFlags(flags);
        return;
      }
    }

    UserData userData = dataModel.getConfigData().getUserData(0);
    flags.fullAllowed =
        ExtendedSwitchUtility.isFullAccessAllowed(dataModel, con, cpu, userData);
    flags.privateAllowed =
        ExtendedSwitchUtility.isPrivateModeAllowed(dataModel, con, cpu, userData);
    flags.videoAllowed =
        ExtendedSwitchUtility.isVideoAccessAllowed(dataModel, con, cpu, userData, null);
  }

  /**
   * 清除连接类型.
   */
  private static void resetConnectionFlags(ConnectionFlags flags) {
    flags.fullAllowed = false;
    flags.privateAllowed = false;
    flags.videoAllowed = false;
  }

  private static void handleGridLineTerminal(CaesarDeviceController deviceController,
                                                  CaesarGridLineTerminal terminal, CaesarCpuTerminal cpu,
                                                  ConnectionFlags flags) {
    if (isGridLineAvailable(terminal)) {
      SwitchData switchData = deviceController.getDataModel()
          .getConfigData()
          .getSystemConfigData()
          .getSwitchData();

      flags.fullAllowed = true;
      flags.videoAllowed = switchData.isCpuWatch() || cpu.getCpuData().getConsoleData() == null;
    }
  }

  private static boolean isResolutionIncompatible(
      CaesarConstants.Extender.ExtenderVideoResolutionType txType,
      CaesarConstants.Extender.ExtenderVideoResolutionType rxType) {
    return txType == CaesarConstants.Extender.ExtenderVideoResolutionType.VIDEO_4K_60HZ
        && rxType != CaesarConstants.Extender.ExtenderVideoResolutionType.VIDEO_4K_60HZ;
  }

  private static boolean isGridLineAvailable(CaesarGridLineTerminal terminal) {
    return !terminal.channel1UsedProperty().get() && !terminal.channel2UsedProperty().get();
  }

  @NotNull
  private static List<TypeWrapper> buildTypeList(ConnectionFlags flags) {
    List<TypeWrapper> types = new ArrayList<>(3);
    if (flags.fullAllowed) {
      types.add(createTypeWrapper("ConnectType.full", FULL));
    }
    if (flags.privateAllowed) {
      types.add(createTypeWrapper("ConnectType.private", PRIVATE));
    }
    if (flags.videoAllowed) {
      types.add(createTypeWrapper("ConnectType.video", VIDEO));
    }
    return types;
  }

  @NotNull
  private static TypeWrapper createTypeWrapper(String messageKey, String type) {
    return new TypeWrapper(Bundle.NbBundle.getMessage(messageKey), type, "");
  }

  private static class ConnectionFlags {
    boolean fullAllowed = false;
    boolean privateAllowed = false;
    boolean videoAllowed = false;
  }

  /**
   * 获取Multiview通道列表.
   */
  public static Collection<TypeWrapper> getMultiviewChannel(VisualEditTerminal rx) {
    // 如果不是CaesarConTerminal类型，直接返回空列表
    if (!(rx instanceof CaesarConTerminal)) {
      return Collections.emptyList();
    }

    CaesarConTerminal conTerminal = (CaesarConTerminal) rx;

    // 检查是否是多画面终端
    if (!conTerminal.getConsoleData().isMultiview()) {
      return Collections.emptyList();
    }

    // 获取多画面数据
    MultiviewData multiviewData = conTerminal.getConsoleData().getMultiviewData();
    if (multiviewData == null) {
      return Collections.emptyList();
    }

    // 根据布局类型获取通道数量
    int channelCount = multiviewData.getLayoutType().getValue();

    // 创建通道列表
    return createChannelList(channelCount);
  }

  /**
   * 创建指定数量的通道列表.
   */
  private static List<TypeWrapper> createChannelList(int channelCount) {
    List<TypeWrapper> channels = new ArrayList<>(channelCount);
    for (int i = 0; i < channelCount; i++) {
      channels.add(new TypeWrapper("Channel-" + i, String.valueOf(i), null));
    }
    return channels;
  }
}
