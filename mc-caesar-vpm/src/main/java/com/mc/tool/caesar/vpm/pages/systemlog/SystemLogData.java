package com.mc.tool.caesar.vpm.pages.systemlog;

import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleStringProperty;

/**
 * .
 */
public class SystemLogData {

  private SimpleStringProperty name = new SimpleStringProperty();
  private SimpleStringProperty type = new SimpleStringProperty();
  private SimpleStringProperty ports = new SimpleStringProperty();
  private SimpleBooleanProperty isSelected = new SimpleBooleanProperty(false);

  private byte level1;
  private byte level2;
  private String address;

  /** . */
  public SystemLogData(byte level1, byte level2, String address) {
    this.level1 = level1;
    this.level2 = level2;
    this.address = address;
  }

  public String getAddress() {
    return this.address;
  }

  public void setAddress(String address) {
    this.address = address;
  }

  public int getLevel1() {
    return level1;
  }

  public void setLevel1(byte level1) {
    this.level1 = level1;
  }

  public byte getLevel2() {
    return level2;
  }

  public void setLevel2(byte level2) {
    this.level2 = level2;
  }

  public SimpleStringProperty getNameProperty() {
    return name;
  }

  public String getName() {
    return name.get();
  }

  public void setName(String name) {
    this.name.set(name);
  }

  public SimpleStringProperty getTypeProperty() {
    return type;
  }

  public String getType() {
    return type.get();
  }

  public void setType(String type) {
    this.type.set(type);
  }

  public SimpleStringProperty getPortsProperty() {
    return ports;
  }

  public String getPorts() {
    return ports.get();
  }

  public void setPorts(String ports) {
    this.ports.set(ports);
  }

  public Boolean getSelected() {
    return isSelected.get();
  }

  public void setSelected(Boolean selected) {
    this.isSelected.set(selected);
  }

  public SimpleBooleanProperty getSelectedProperty() {
    return isSelected;
  }
}
