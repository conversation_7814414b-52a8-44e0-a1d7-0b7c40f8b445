package com.mc.tool.caesar.vpm.pages.operation.crossscreen.view;

import com.mc.tool.caesar.vpm.pages.operation.crossscreen.view.ScreenLayout.ScreenWindow;
import com.mc.tool.framework.operation.crossscreen.controller.CrossScreenControllable;
import com.mc.tool.framework.operation.crossscreen.controller.ScreenItem;
import com.mc.tool.framework.systemedit.datamodel.CrossScreenFunc;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.VisualEditConnection;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ChangeListener;
import javafx.collections.ListChangeListener;
import javafx.geometry.Rectangle2D;
import javafx.geometry.Side;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.image.Image;
import javafx.scene.input.DragEvent;
import javafx.scene.input.TransferMode;
import javafx.scene.layout.Background;
import javafx.scene.layout.BackgroundFill;
import javafx.scene.layout.BackgroundImage;
import javafx.scene.layout.BackgroundPosition;
import javafx.scene.layout.BackgroundRepeat;
import javafx.scene.layout.BackgroundSize;
import javafx.scene.paint.Color;

/**
 * .
 */
public class CaesarScreenNewItem extends ScreenItem {
  private static final int SNAPSHOT_X = 28;
  private static final int SNAPSHOT_Y = 34;
  private static final int SNAPSHOT_WIDTH = 140;
  private static final int SNAPSHOT_HEIGHT = 76;
  private ChangeListener<String> nameChangeListener;
  private ObjectProperty<Background> screenBg;

  public CaesarScreenNewItem(
      int row,
      int column,
      ObjectProperty<VisualEditTerminal> target,
      VisualEditModel model,
      CrossScreenFunc func,
      CrossScreenControllable controllable) {
    super(row, column, target, model, func, controllable);
  }

  protected ObjectProperty<Background> getScreenBg() {
    if (screenBg == null) {
      screenBg = new SimpleObjectProperty<>();
    }
    return screenBg;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    screenImage.managedProperty().bind(screenImage.visibleProperty());
    screenText.managedProperty().bind(screenText.visibleProperty());
    screenImage.visibleProperty().bind(target.isNotNull());
    screenText.visibleProperty().bind(target.isNotNull());

    this.setOnDragDetected(this::onDragDetected);
    this.setOnDragOver(this::onDragOver);
    this.setOnDragDropped(this::onDragDrop);
    canvas.setOnDragOver(this::onCanvasDragOver);
    canvas.setOnDragDropped(this::onCanvasDragDrop);
    canvas.setOnDragExited(event -> updateHover(null));

    updateText();
    updateImage();

    nameChangeListener = (obs, oldVal, newVal) -> updateText();

    target.addListener(
        getWeakAdapter()
            .wrap(
                (observable, oldValue, newValue) -> {
                  updateText();
                  updateImage();
                  if (oldValue != null) {
                    oldValue.nameProperty().removeListener(nameChangeListener);
                  }
                  if (newValue != null) {
                    newValue.nameProperty().addListener(nameChangeListener);
                  }
                }));

    model
        .getConnections()
        .addListener(
            getWeakAdapter()
                .wrap(
                    (ListChangeListener<VisualEditConnection>)
                        change -> {
                          updateText();
                          updateImage();
                        }));

    func.getCrossScreenData()
        .getControlSource()
        .addListener(
            getWeakAdapter()
                .wrap(
                    (observable, oldValue, newValue) -> {
                      updateText();
                      updateImage();
                    }));

    screenImage.backgroundProperty().bind(getScreenBg());

    updateHover(null);
  }

  protected VisualEditTerminal getConnectedTerminal(ScreenWindow window) {
    return null;
  }

  protected void connectWindow(ScreenWindow window, VisualEditTerminal terminal) {}

  protected Image getSnapshot(ScreenWindow window) {
    return null;
  }

  protected ScreenLayout getLayout() {
    final ScreenLayout screenLayout = new ScreenLayout();
    ScreenWindow window1 = new ScreenWindow();
    window1.setXpos(0);
    window1.setYpos(0);
    window1.setWidth(0.5);
    window1.setHeight(0.5);
    window1.setName("1");
    screenLayout.addWindow(window1);

    ScreenWindow window2 = new ScreenWindow();
    window2.setXpos(0.5);
    window2.setYpos(0);
    window2.setWidth(0.5);
    window2.setHeight(0.5);
    window2.setName("2");
    screenLayout.addWindow(window2);

    ScreenWindow window3 = new ScreenWindow();
    window3.setXpos(0);
    window3.setYpos(0.5);
    window3.setWidth(0.5);
    window3.setHeight(0.5);
    window3.setName("3");
    screenLayout.addWindow(window3);

    ScreenWindow window4 = new ScreenWindow();
    window4.setXpos(0.5);
    window4.setYpos(0.5);
    window4.setWidth(0.5);
    window4.setHeight(0.5);
    window4.setName("4");
    screenLayout.addWindow(window4);
    return screenLayout;
  }

  protected void updateText() {
    VisualEditTerminal terminal = target.get();
    if (terminal != null) {
      ScreenLayout layout = getLayout();
      // 如果只有一个窗口，只显示一行，多个窗口的话显示多行
      if (layout.getWindowCount() <= 1) {
        ScreenWindow window = layout.getFirstWindow();
        VisualEditTerminal connectedTerminal = getConnectedTerminal(window);
        if (connectedTerminal != null) {
          screenText.setText(terminal.getName() + "[" + connectedTerminal.getName() + "]");
        } else {
          screenText.setText(terminal.getName());
        }
      } else {
        StringBuilder builder = new StringBuilder();
        builder.append(terminal.getName());
        for (ScreenWindow window : layout.getWindows()) {
          VisualEditTerminal connectedTerminal = getConnectedTerminal(window);
          String connectedName = "";
          if (connectedTerminal != null) {
            connectedName = connectedTerminal.getName();
          }
          builder
              .append('\n')
              .append(window.getName())
              .append('[')
              .append(connectedName)
              .append(']');
        }
        screenText.setText(builder.toString());
      }
    } else {
      screenText.setText("");
    }
  }

  @Override
  protected void updateImage() {
    String noControlImage = "com/mc/tool/framework/operation/crossscreen/screen.png";
    String controlImage = "com/mc/tool/framework/operation/crossscreen/screen_control.png";
    boolean isControl =
        func.getCrossScreenData().getControlSource().get() == target.get() && target.get() != null;
    String image = isControl ? controlImage : noControlImage;
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();

    final BackgroundFill backgroundFill = new BackgroundFill(Color.web("#f8f9fb"), null, null);

    BackgroundImage screenImageBg;
    Image screenImageInstance = new Image(classLoader.getResourceAsStream(image));
    screenImageBg =
        new BackgroundImage(
            screenImageInstance,
            BackgroundRepeat.NO_REPEAT,
            BackgroundRepeat.NO_REPEAT,
            BackgroundPosition.CENTER,
            new BackgroundSize(-1, -1, false, false, false, false));
    // 缩略图
    List<BackgroundImage> images = new ArrayList<>();
    images.add(screenImageBg);
    for (ScreenWindow window : getLayout().getWindows()) {
      Image snapshot = getSnapshot(window);
      if (snapshot == null) {
        continue;
      }
      Rectangle2D rect = getWindowRect(window, SNAPSHOT_X, SNAPSHOT_Y);
      BackgroundImage snapshotBg =
          new BackgroundImage(
              snapshot,
              BackgroundRepeat.NO_REPEAT,
              BackgroundRepeat.NO_REPEAT,
              new BackgroundPosition(
                  Side.LEFT, rect.getMinX(), false, Side.TOP, rect.getMinY(), false),
              new BackgroundSize(rect.getWidth(), rect.getHeight(), false, false, false, false));
      images.add(snapshotBg);
    }
    Background background =
        new Background(
            new BackgroundFill[] {backgroundFill}, images.toArray(new BackgroundImage[0]));

    getScreenBg().set(background);
  }

  protected static Rectangle2D getWindowRect(ScreenWindow window, double xoffset, double yoffset) {
    Rectangle2D rectangle2d =
        new Rectangle2D(
            xoffset + SNAPSHOT_WIDTH * window.getXpos(),
            yoffset + SNAPSHOT_HEIGHT * window.getYpos(),
            SNAPSHOT_WIDTH * window.getWidth(),
            SNAPSHOT_HEIGHT * window.getHeight());
    return rectangle2d;
  }

  protected void updateHover(ScreenWindow window) {
    GraphicsContext gc = canvas.getGraphicsContext2D();
    gc.setFill(Color.web("#333333"));
    gc.fillRect(0, 0, SNAPSHOT_WIDTH, SNAPSHOT_HEIGHT);

    for (ScreenWindow item : getLayout().getWindows()) {
      Rectangle2D rect = getWindowRect(item, 0, 0);
      if (item.equals(window)) {
        gc.setFill(Color.web("#f08519", 0.5));
        gc.fillRect(rect.getMinX(), rect.getMinY(), rect.getWidth(), rect.getHeight());
      } else {
        gc.setStroke(Color.web("#f8f9fb"));
        gc.strokeRect(rect.getMinX(), rect.getMinY(), rect.getWidth(), rect.getHeight());
      }
    }
  }

  protected void onCanvasDragOver(DragEvent event) {
    VisualEditTerminal terminal = getTerminal(event);
    if (terminal.isTx() && target.get() != null) {
      event.acceptTransferModes(TransferMode.ANY);
      double xpos = event.getX();
      double ypos = event.getY();
      updateHover(findWindow(xpos, ypos));
    }
    event.consume();
  }

  protected ScreenWindow findWindow(double xpos, double ypos) {
    for (ScreenWindow window : getLayout().getWindows()) {
      Rectangle2D rect = getWindowRect(window, 0, 0);
      if (xpos > rect.getMinX()
          && xpos < rect.getMaxX()
          && ypos > rect.getMinY()
          && ypos < rect.getMaxY()) {
        return window;
      }
    }
    return null;
  }

  protected void onCanvasDragDrop(DragEvent event) {
    VisualEditTerminal terminal = getTerminal(event);
    if ((terminal != null && terminal.isTx()
            || terminal == null
                && event
                    .getDragboard()
                    .getString()
                    .equals(SystemEditDefinition.EMPTY_TERMINAL_GUID))
        && target.get() != null) {
      ScreenWindow window = findWindow(event.getX(), event.getY());
      if (window != null) {
        connectWindow(window, terminal);
      }
    }
    updateHover(null);
    event.setDropCompleted(true);
    event.consume();
  }
}
