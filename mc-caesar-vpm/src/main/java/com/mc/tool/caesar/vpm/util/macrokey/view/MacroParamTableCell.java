package com.mc.tool.caesar.vpm.util.macrokey.view;

import com.mc.tool.caesar.vpm.util.macrokey.datamodel.MacroParam;
import javafx.collections.ObservableList;
import javafx.geometry.Side;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Menu;
import javafx.scene.control.MenuItem;
import javafx.scene.control.cell.ComboBoxTableCell;
import javafx.util.StringConverter;

/**
 * .
 */
public class MacroParamTableCell<S> extends ComboBoxTableCell<S, MacroParam> {

  public MacroParamTableCell(
      StringConverter<MacroParam> converter, ObservableList<MacroParam> items) {
    super(converter, items);
  }

  @Override
  public void startEdit() {
    super.startEdit();
    // 如果有子项
    boolean useMenu = false;
    for (MacroParam param : getItems()) {
      if (param.getChildren().size() > 0) {
        useMenu = true;
        break;
      }
    }
    if (useMenu) {
      setText(getConverter().toString(getItem()));
      setGraphic(null);
      ContextMenu menu = new ContextMenu();
      for (MacroParam param : getItems()) {
        MenuItem item = createMenuItem(param);
        item.setStyle("-fx-pref-width:" + getBoundsInParent().getWidth());
        menu.getItems().add(item);
      }
      menu.show(this, Side.BOTTOM, 0, 0);
    }
  }

  private MenuItem createMenuItem(MacroParam param) {
    if (param.getChildren().size() > 0) {
      Menu menu = new Menu(param.getSimpleName());
      for (MacroParam child : param.getChildren()) {
        menu.getItems().add(createMenuItem(child));
      }
      return menu;
    } else {
      MenuItem item = new MenuItem(param.getSimpleName());
      item.setOnAction((event) -> commitEdit(param));
      return item;
    }
  }
}
