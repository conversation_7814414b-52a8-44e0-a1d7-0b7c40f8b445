package com.mc.tool.caesar.vpm.pages.hotkeyconfiguration;

import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.ViewControllable;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.control.Tab;
import javafx.scene.control.TabPane;
import javafx.scene.layout.VBox;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class CaesarHotkeyConfigurationPageView extends VBox
    implements Initializable, ViewControllable {

  @Getter @FXML private TabPane tabPane;

  @Getter private Tab conHotkeyTab;
  @Getter private Tab userMacroKeyTab;
  @Getter private Tab consoleMacroKeyTab;
  @Getter private ConHotkeyView conHotkeyView;
  @Getter private UserMacroKeyView userMacroKeyView;
  @Getter private ConsoleMacroKeyView consoleMacroKeyView;

  private DeviceControllable deviceController = null;

  /** Constructor. */
  public CaesarHotkeyConfigurationPageView() {
    try {
      URL location =
          getClass()
              .getResource(
                  "/com/mc/tool/caesar/vpm/pages/hotkeyconfiguration/"
                      + "hotkeyconfiguration_view.fxml");
      FXMLLoader loader = new FXMLLoader(location);
      loader.setController(this);
      loader.setRoot(this);
      loader.load();
    } catch (IOException ex) {
      log.warn("Can not load hotkeyconfiguration_view.fxml", ex);
    }
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    this.deviceController = deviceController;
    conHotkeyView = new ConHotkeyView();
    conHotkeyView.setDeviceController(deviceController);
    conHotkeyTab =
        new Tab(CaesarI18nCommonResource.getString("page.con_hotkey_configuration"), conHotkeyView);
    tabPane.getTabs().add(conHotkeyTab);

    userMacroKeyView = new UserMacroKeyView();
    userMacroKeyView.setDeviceController(deviceController);
    userMacroKeyTab =
        new Tab(CaesarI18nCommonResource.getString("page.user_macro_view"), userMacroKeyView);
    tabPane.getTabs().add(userMacroKeyTab);

    consoleMacroKeyView = new ConsoleMacroKeyView();
    consoleMacroKeyView.setDeviceController(deviceController);
    consoleMacroKeyTab =
        new Tab(CaesarI18nCommonResource.getString("page.con_macro_view"), consoleMacroKeyView);
    tabPane.getTabs().add(consoleMacroKeyTab);
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceController;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    // TODO Auto-generated method stub

  }
}
