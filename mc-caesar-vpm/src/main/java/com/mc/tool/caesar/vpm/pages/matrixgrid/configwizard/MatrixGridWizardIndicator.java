package com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard;

import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import java.util.Arrays;

/**
 * .
 */
public class MatrixGridWizardIndicator extends WizardIndicator {

  /** . */
  public MatrixGridWizardIndicator() {
    setItems(
        Arrays.asList(
            CaesarI18nCommonResource.getString("matrixgrid.wizard.indicator.prepare"),
            CaesarI18nCommonResource.getString("matrixgrid.wizard.indicator.host"),
            CaesarI18nCommonResource.getString("matrixgrid.wizard.indicator.gridname"),
            CaesarI18nCommonResource.getString("matrixgrid.wizard.indicator.gridsystem"),
            CaesarI18nCommonResource.getString("matrixgrid.wizard.indicator.idprocess"),
            CaesarI18nCommonResource.getString("matrixgrid.wizard.indicator.activate")));
  }
}
