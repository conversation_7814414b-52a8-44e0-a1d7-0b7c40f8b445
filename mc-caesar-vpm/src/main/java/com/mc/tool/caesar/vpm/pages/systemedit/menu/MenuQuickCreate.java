package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.tool.caesar.api.CaesarValidatorFactory;
import com.mc.tool.caesar.api.interfaces.Nameable;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.Bundle.NbBundle;
import com.mc.tool.caesar.vpm.pages.systemedit.view.TerminalListSelectionView;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.UndecoratedDialog;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import javafx.beans.binding.Bindings;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.control.ButtonBar;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.control.MenuItem;
import javafx.scene.control.TextField;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import javafx.stage.Modality;
import org.controlsfx.validation.ValidationSupport;
import org.controlsfx.validation.Validator;

/**
 * .
 */
public class MenuQuickCreate extends MenuItem {

  protected final SystemEditControllable controllable;
  protected List<VisualEditNode> nodes;
  protected final ObservableList<VisualEditNode> sourceList = FXCollections.observableArrayList();
  protected final ObservableList<VisualEditNode> targetList = FXCollections.observableArrayList();

  protected TerminalListSelectionView view;
  protected TextField nameField;
  protected UndecoratedDialog<List<VisualEditNode>> dialog;
  protected Label groupNameLabel;
  protected Label sourceHeaderLabel;
  protected Label targetHeaderLabel;
  protected Label errorMessageLabel;

  protected final ObjectProperty<Collection<? extends Nameable>> userNamesProperty =
      new SimpleObjectProperty<>(Collections.emptyList());

  /** 快速分组. */
  public MenuQuickCreate(SystemEditControllable controllable) {
    this.controllable = controllable;
    initTerminal();
    this.setOnAction(event -> onAction());
    this.disableProperty().set(getMenuDisableValue());
  }

  protected boolean getMenuDisableValue() {
    return nodes.isEmpty();
  }

  protected void initTerminal() {
    nodes = new ArrayList<>();
  }

  private void onAction() {
    view = newListSelectionView();
    Node content = initializeLayout();
    newDialog(content);
    initBundle();
    handleAction();
  }

  /** dialog showAndWait. */
  protected void handleAction() {
    dialog.showAndWait();
  }

  /** groupNameLabel, dialog. */
  protected void initBundle() {
    groupNameLabel.setText("");
    dialog.setTitle("");
  }

  protected void setNameFieldText() {
    nameField.setText("");
  }

  private Node initializeLayout() {
    groupNameLabel = new Label();
    nameField = new TextField();
    errorMessageLabel = new Label();
    errorMessageLabel.setTextFill(Color.RED);
    setNameFieldText();
    HBox hbox = new HBox();
    hbox.getChildren().addAll(groupNameLabel, nameField, errorMessageLabel);
    hbox.setAlignment(Pos.CENTER_LEFT);
    hbox.setSpacing(5);
    hbox.setPrefHeight(25);
    hbox.setPadding(new Insets(0, 0, 0, 10));
    VBox vbox = new VBox();
    vbox.getChildren().addAll(hbox, view);
    return vbox;
  }

  private void newDialog(Node content) {
    ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    dialog = new UndecoratedDialog<>();
    dialog.initOwner(app.getMainWindow());
    dialog.initModality(Modality.APPLICATION_MODAL);
    dialog.getDialogPane().setContent(content);
    dialog.getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);
    ValidationSupport validationSupport = new ValidationSupport();
    validationSupport.setValidationDecorator(null);
    Validator<String> notSameNameValidator =
        CaesarValidatorFactory.nameValidator(
            userNamesProperty,
            NbBundle.getMessage("menu.grouping.same_name_error"),
            NbBundle.getMessage("menu.grouping.illegal_name_len"));
    validationSupport.registerValidator(nameField, notSameNameValidator);
    dialog
        .getDialogPane()
        .lookupButton(ButtonType.OK)
        .disableProperty()
        .bind(validationSupport.invalidProperty().or(Bindings.isEmpty(targetList)));
    validationSupport
        .validationResultProperty()
        .addListener(
            (observable, oldValue, newValue) -> {
              if (newValue.getErrors().size() == 0) {
                errorMessageLabel.setText("");
              } else if (newValue.getErrors().size() > 0) {
                newValue.getErrors().stream()
                    .findFirst()
                    .ifPresent(item -> errorMessageLabel.setText(item.getText()));
              }
            });
    dialog.setResultConverter(
        (dialogButton) -> {
          ButtonBar.ButtonData data = dialogButton == null ? null : dialogButton.getButtonData();
          if (data == ButtonBar.ButtonData.OK_DONE) {
            return view.getTargetItems();
          }
          return Collections.emptyList();
        });
  }

  private TerminalListSelectionView newListSelectionView() {
    TerminalListSelectionView view = new TerminalListSelectionView();
    sourceHeaderLabel = new Label(NbBundle.getMessage("menu.grouping.selectable"));
    HBox sourceHeaderBox = new HBox(sourceHeaderLabel);
    sourceHeaderBox.setStyle(
        "-fx-background-color:#dbf1ef;-fx-border-width:1px;"
            + "-fx-border-color:#e5e5e5;-fx-alignment:center;"
            + "-fx-min-height:30px;"
            + "-fx-max-height:30px;");
    view.setSourceHeader(sourceHeaderBox);
    targetHeaderLabel = new Label(NbBundle.getMessage("menu.grouping.selected"));
    HBox targetHeaderBox = new HBox(targetHeaderLabel);
    targetHeaderBox.setStyle(
        "-fx-border-width:1px;-fx-border-color:#e5e5e5;"
            + "-fx-alignment:center;-fx-min-height:30px;"
            + "-fx-max-height:30px;"
            + "-fx-background-color:#f2f2f2;");
    view.setTargetHeader(targetHeaderBox);

    view.setCellFactory(col -> new SelectionViewCell());
    sourceList.setAll(nodes);
    view.setSourceItems(sourceList);
    view.setTargetItems(targetList);
    return view;
  }

  private static class SelectionViewCell extends ListCell<VisualEditNode> {

    @Override
    protected void updateItem(VisualEditNode item, boolean empty) {
      super.updateItem(item, empty);
      if (empty || item == null) {
        setText("");
      } else {
        setText(item.getName());
      }
    }
  }
}
