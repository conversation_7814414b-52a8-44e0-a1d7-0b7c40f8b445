package com.mc.tool.caesar.vpm.pages.multiscreencontrol;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.AbstractData;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData.MultiScreenConInfo;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.utility.UndecoratedAlert;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.ResourceBundle;
import javafx.beans.binding.Bindings;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredList;
import javafx.collections.transformation.SortedList;
import javafx.event.ActionEvent;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.Button;
import javafx.scene.control.CheckBox;
import javafx.scene.control.ChoiceBox;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.Spinner;
import javafx.scene.control.SpinnerValueFactory;
import javafx.scene.control.Tab;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextField;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.stage.Modality;
import javafx.stage.Stage;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.glyphfont.FontAwesome;
import org.controlsfx.glyphfont.Glyph;

/**
 * .
 */
@Slf4j
public class MultiScreenControlPageController implements Initializable, ViewControllable {

  @FXML private Button newButton;
  @FXML private Button deleteButton;
  @FXML private Button applyButton;
  @FXML private Button cancelButton;
  @FXML private TextField filterField;
  @FXML private TableView<MultiScreenData> tableView;
  @FXML private TableColumn<MultiScreenData, String> indexCol;
  @FXML private TableColumn<MultiScreenData, String> nameCol;
  @FXML private TableColumn<MultiScreenData, Number> horCol;
  @FXML private TableColumn<MultiScreenData, Number> verCol;
  @FXML private Label dataName;
  @FXML private TextField nameLabel;
  @FXML private Label dataHorizontal;
  @FXML private TextField horLabel;
  @FXML private Label dataVertical;
  @FXML private TextField verLabel;
  @FXML private Label dataCtrlConId;
  @FXML private Label dataMode;
  @FXML private ChoiceBox<String> controlConChoiceBox;
  @FXML private Spinner<Integer> spinner;
  @FXML private CheckBox modeCheckBox;
  @FXML private Tab tab;

  private MultiScreenControlListSelectionView<String> view =
      new MultiScreenControlListSelectionView<>();
  private Button upButton =
      new Button("", Glyph.create("FontAwesome|" + FontAwesome.Glyph.ANGLE_UP));
  private Button downButton =
      new Button("", new FontAwesome().create(FontAwesome.Glyph.ANGLE_DOWN));

  ObservableList<MultiScreenData> screendataList = FXCollections.observableArrayList();
  ObservableList<String> condataNameList = FXCollections.observableArrayList();
  private CaesarDeviceController deviceController = null;
  SpinnerValueFactory<Integer> svf = new SpinnerValueFactory.IntegerSpinnerValueFactory(0, 20);
  SimpleIntegerProperty num1 = new SimpleIntegerProperty(1);
  SimpleIntegerProperty num2 = new SimpleIntegerProperty(1);
  Stage window = new Stage();

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
    } else {
      log.warn("Error device controller type : {} !", deviceController.getClass().getName());
      return;
    }
    initDataList();
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    initNewAtctionWindow();
    dataName.setText(
        Bundle.NbBundle.getMessage(
            MultiScreenControlPageController.class, "MultiScreenControl.nameLabel.text"));
    dataHorizontal.setText(
        Bundle.NbBundle.getMessage(
            MultiScreenControlPageController.class, "MultiScreenControl.dataHorizontal.text"));
    dataVertical.setText(
        Bundle.NbBundle.getMessage(
            MultiScreenControlPageController.class, "MultiScreenControl.dataVertical.text"));
    dataCtrlConId.setText(
        Bundle.NbBundle.getMessage(
            MultiScreenControlPageController.class, "MultiScreenControl.dataCtrlConId.text"));
    dataMode.setText(
        Bundle.NbBundle.getMessage(
            MultiScreenControlPageController.class, "MultiScreenControl.dataMode.text"));
    tab.setText(
        Bundle.NbBundle.getMessage(
            MultiScreenControlPageController.class, "MultiScreenControl.tab.text"));
    newButton.setText(
        Bundle.NbBundle.getMessage(
            MultiScreenControlPageController.class, "MultiScreenControl.newButton.text"));
    deleteButton.setText(
        Bundle.NbBundle.getMessage(
            MultiScreenControlPageController.class, "MultiScreenControl.deleteButton.text"));
    applyButton.setText(
        Bundle.NbBundle.getMessage(
            MultiScreenControlPageController.class, "MultiScreenControl.applyButton.text"));
    cancelButton.setText(
        Bundle.NbBundle.getMessage(
            MultiScreenControlPageController.class, "MultiScreenControl.cancelButton.text"));

    indexCol.setText("#");
    nameCol.setText(
        Bundle.NbBundle.getMessage(
            MultiScreenControlPageController.class, "MultiScreenControl.tableView.nameCol.text"));
    horCol.setText(
        Bundle.NbBundle.getMessage(
            MultiScreenControlPageController.class, "MultiScreenControl.tableView.horCol.text"));
    verCol.setText(
        Bundle.NbBundle.getMessage(
            MultiScreenControlPageController.class, "MultiScreenControl.tableView.verCol.text"));

    indexCol.setCellFactory(col -> new IndexCell());
    nameCol.setCellValueFactory(item -> item.getValue().getNameProperty());
    horCol.setCellValueFactory(item -> item.getValue().getHorizontalNumberProperty());
    verCol.setCellValueFactory(item -> item.getValue().getVerticalNumberProperty());

    FilteredList<MultiScreenData> filteredData = new FilteredList<>(screendataList, p -> true);
    filterField
        .textProperty()
        .addListener(
            (observable, oldValue, newValue) ->
                filteredData.setPredicate(
                    data -> {
                      if (newValue == null || newValue.isEmpty()) {
                        return true;
                      }
                      String lowerCaseFilter = newValue.toLowerCase();
                      if (data.getName().toLowerCase().contains(lowerCaseFilter)) {
                        return true;
                      }
                      return false;
                    }));
    SortedList<MultiScreenData> sortedData = new SortedList<>(filteredData);
    sortedData.comparatorProperty().bind(tableView.comparatorProperty());

    tableView
        .getSelectionModel()
        .selectedItemProperty()
        .addListener(
            (observable, oldValue, newValue) -> {
              upButton
                  .disableProperty()
                  .bind(
                      Bindings.isEmpty(
                          ((MultiScreenControlListSelectionViewSkin<String>) view.getSkin())
                              .getTargetListView()
                              .getSelectionModel()
                              .getSelectedItems()));
              downButton
                  .disableProperty()
                  .bind(
                      Bindings.isEmpty(
                          ((MultiScreenControlListSelectionViewSkin<String>) view.getSkin())
                              .getTargetListView()
                              .getSelectionModel()
                              .getSelectedItems()));

              view.setDisable(false);
              controlConChoiceBox.setDisable(false);
              if (newValue != null) {
                int horNum = newValue.getHorizontalNumber();
                int verNum = newValue.getVerticalNumber();
                ((MultiScreenControlListSelectionViewSkin<String>) view.getSkin())
                    .setTargetMaxSize(horNum * verNum);
                ((MultiScreenControlListSelectionViewSkin<String>) view.getSkin())
                    .setIsEqual(false);
                nameLabel.setText(newValue.getName());
                horLabel.setText("" + horNum);
                verLabel.setText("" + verNum);
                spinner.getValueFactory().setValue(newValue.getUsFrame());
                modeCheckBox.setSelected(newValue.getType() != 0);
                view.getTargetItems().clear();
                if (newValue.getStatus() != 0) {
                  view.setDisable(true);
                  controlConChoiceBox.setDisable(true);
                }
                for (MultiScreenConInfo conInfo : newValue.getConInfos()) {
                  Collection<ConsoleData> conDatas =
                      deviceController.getDataModel().getConfigDataManager().getActiveConsoles();
                  for (ConsoleData con : conDatas) {
                    if (con.getId() == conInfo.getId()) {
                      view.getTargetItems().add(con.getName());
                      if (con.getId() == newValue.getCtrlConId()) {
                        controlConChoiceBox.getSelectionModel().select(con.getName());
                      }
                    }
                  }
                }

                Collection<ConsoleData> conDatas =
                    deviceController.getDataModel().getConfigDataManager().getActiveConsoles();
                if (!condataNameList.isEmpty()) {
                  condataNameList.clear();
                }
                for (ConsoleData data : conDatas) {
                  if (data.getMultiScreenIndex() == 0) {
                    condataNameList.add(data.getName());
                    if (condataNameList.isEmpty()) {
                      System.out.println("There is no con available");
                    }
                  }
                }
                view.getSourceItems().clear();
                view.getSourceItems().addAll(condataNameList);
              } else {
                clearData();
              }
            });
    tableView.setItems(sortedData);
    spinner.setValueFactory(svf);

    // ---------------------------//
    tab.setContent(view);
    HBox buttonBox = new HBox();
    buttonBox.getChildren().addAll(upButton, downButton);
    view.setTargetFooter(buttonBox);
    controlConChoiceBox.itemsProperty().bind(view.targetItemsProperty());

    upButton.setOnAction(
        event -> {
          MultiScreenControlListSelectionViewSkin<String> listView =
              (MultiScreenControlListSelectionViewSkin<String>) view.getSkin();
          if (listView.getTargetListView().getSelectionModel().getSelectedItems().isEmpty()) {
            return;
          }
          int select = listView.getTargetListView().getSelectionModel().getSelectedIndex();
          ObservableList<String> items = view.getTargetItems();
          if (select != 0) {
            final String back = items.get(select);
            final String front = items.get(select - 1);
            items.set(select, front);
            items.set(select - 1, back);
            listView.getTargetListView().getSelectionModel().clearSelection();
          }
        });
    downButton.setOnAction(
        event -> {
          MultiScreenControlListSelectionViewSkin<String> listView =
              (MultiScreenControlListSelectionViewSkin<String>) view.getSkin();
          if (listView.getTargetListView().getSelectionModel().getSelectedItems().isEmpty()) {
            return;
          }
          int select = listView.getTargetListView().getSelectionModel().getSelectedIndex();
          ObservableList<String> items = view.getTargetItems();
          if (select != items.size() - 1) {
            String front = items.get(select);
            String back = items.get(select + 1);
            items.set(select, back);
            items.set(select + 1, front);
            listView.getTargetListView().getSelectionModel().clearSelection();
          }
        });

    newButton.setOnAction(
        event -> {
          if (!screendataList.isEmpty()) {
            for (MultiScreenData data : screendataList) {
              if (data.getStatus() == 0) {
                UndecoratedAlert alert = new UndecoratedAlert(AlertExType.INFORMATION);
                if (newButton != null && newButton.getScene() != null) {
                  alert.initOwner(newButton.getScene().getWindow());
                }
                alert.setTitle("Warning");
                alert.setHeaderText("提醒");
                alert.setContentText("请先保存之前未保存数据");
                alert.showAndWait();
                return;
              }
            }
          }
          newAction();
        });

    deleteButton.setOnAction(
        event -> {
          MultiScreenData msd = tableView.getSelectionModel().getSelectedItem();
          if (msd != null) {
            if (msd.getStatus() == 0) {
              screendataList.remove(msd);
              return;
            }
            List<ConsoleData> consoleDataChangeList = new ArrayList<>();
            Threshold oldThreshold;
            oldThreshold = msd.getThreshold();
            msd.setThreshold(AbstractData.THRESHOLD_UI_LOCAL_CHANGES);
            for (int i = 0; i < msd.getHorizontalNumber() * msd.getVerticalNumber(); i++) {
              ConsoleData console =
                  deviceController
                      .getDataModel()
                      .getConfigDataManager()
                      .getConsoleData4Id(msd.getConInfo(i).getId());
              consoleDataChangeList.add(console);
            }
            msd.delete();
            msd.setThreshold(oldThreshold);
            List<MultiScreenData> multiScreenDataChangeList = new ArrayList<>();
            multiScreenDataChangeList.add(msd);
            if (!consoleDataChangeList.isEmpty()) {
              for (ConsoleData consoleData : consoleDataChangeList) {
                if (consoleData != null) {
                  consoleData.setMultiScreenIndex(0);
                } else {
                  System.out.println("delete console Date is null");
                }
              }
            }
            boolean successful = true;
            try {
              deviceController.getDataModel().sendMultiscreenData(multiScreenDataChangeList);
              deviceController.getDataModel().sendConsoleData(consoleDataChangeList);
            } catch (DeviceConnectionException | BusyException ex) {
              successful = false;
              for (MultiScreenData multiScreenData : multiScreenDataChangeList) {
                if (multiScreenData.equals(AbstractData.THRESHOLD_UI_LOCAL_CHANGES)) {
                  multiScreenData.rollback();
                }
              }
            } finally {
              for (MultiScreenData msdata : multiScreenDataChangeList) {
                Threshold oldThreshold1 = msdata.getThreshold();
                msdata.setThreshold(AbstractData.THRESHOLD_UI_LOCAL_CHANGES);
                if (successful) {
                  msd.setStatus(0);
                }
                msd.setThreshold(oldThreshold1);
                msd.commit(AbstractData.THRESHOLD_UI_LOCAL_CHANGES);
                if (successful) {
                  System.out.println("MultiScreenData delete successful");
                } else {
                  System.out.println("MultiScreenData delete failed");
                }
              }
            }
          }
          tableView.getSelectionModel().clearSelection();
          initDataList();
        });
    deleteButton
        .disableProperty()
        .bind(Bindings.isEmpty(tableView.getSelectionModel().getSelectedItems()));

    applyButton.setOnAction(
        event -> {
          MultiScreenData data = tableView.getSelectionModel().getSelectedItem();
          if (data.getStatus() == 0) {
            commitNewData(data);
          } else {
            updateData(data);
          }
        });
    applyButton
        .disableProperty()
        .bind(Bindings.isEmpty(tableView.getSelectionModel().getSelectedItems()));

    cancelButton.setOnAction(
        event -> {
          tableView.getSelectionModel().clearSelection();
          initDataList();
        });
  }

  private void commitNewData(MultiScreenData data) {
    List<ConsoleData> consoleChangeList = new ArrayList<>();
    int maxNum = data.getHorizontalNumber() * data.getVerticalNumber();
    Collection<ConsoleData> consoleDatas =
        deviceController.getDataModel().getConfigDataManager().getActiveConsoles();
    ObservableList<String> items = view.getTargetItems();
    final Threshold oldThreshold;
    oldThreshold = data.getThreshold();
    data.setThreshold(AbstractData.THRESHOLD_UI_LOCAL_CHANGES);
    int num = 0;
    for (String conName : items) {
      for (ConsoleData con : consoleDatas) {
        if (con.getName().equals(conName) && num < maxNum) {
          con.setMultiScreenIndex(data.getOid() + 1);
          consoleChangeList.add(con);
          String item = controlConChoiceBox.getSelectionModel().getSelectedItem();
          if (item != null && item.equals(conName)) {
            data.setCtrlConId(con.getId());
          }
          data.setConInfoId(num++, con.getId());
        }
      }
    }
    data.setName(nameLabel.getText());
    data.setUsFrame(spinner.getValue());
    data.setType(modeCheckBox.isSelected() ? 1 : 0);
    data.setStatus(CaesarConstants.MultiScreen.Status.ACTIVE);
    data.setThreshold(oldThreshold);
    List<MultiScreenData> multiScreenChangeList = new ArrayList<>();
    multiScreenChangeList.add(data);
    if (!multiScreenChangeList.isEmpty()) {
      try {
        deviceController.getDataModel().sendMultiscreenData(multiScreenChangeList);
        deviceController.getDataModel().sendConsoleData(consoleChangeList);
        initDataList();
      } catch (DeviceConnectionException | BusyException ex) {
        log.warn("Can not send MultiscreenData", ex.getMessage());
        System.out.println("Can not send MultiscreenData");
      }
    }
  }

  private void updateData(MultiScreenData data) {
    int type = modeCheckBox.isSelected() ? 1 : 0;
    if (data.getName().equals(nameLabel.getText())
        && data.getType() == type
        && data.getUsFrame() == spinner.getValue()) {
      return;
    }
    Threshold oldThreshold;
    oldThreshold = data.getThreshold();
    data.setThreshold(AbstractData.THRESHOLD_UI_LOCAL_CHANGES);
    if (!data.getName().equals(nameLabel.getText())) {
      data.setName(nameLabel.getText());
    }
    if (data.getType() != type) {
      data.setType(type);
    }
    if (data.getUsFrame() != spinner.getValue()) {
      data.setUsFrame(spinner.getValue());
    }
    data.setThreshold(oldThreshold);
    List<MultiScreenData> multiScreenChangeList = new ArrayList<>();
    multiScreenChangeList.add(data);
    if (!multiScreenChangeList.isEmpty()) {
      try {
        deviceController.getDataModel().sendMultiscreenData(multiScreenChangeList);
        initDataList();
      } catch (DeviceConnectionException | BusyException ex) {
        log.warn("Can not send MultiscreenData", ex.getMessage());
        System.out.println("Can not send MultiscreenData");
      }
    }
  }

  private void initDataList() {
    Collection<MultiScreenData> datas =
        deviceController.getDataModel().getConfigDataManager().getActiveMultiScreen();
    if (!screendataList.isEmpty()) {
      screendataList.clear();
    }
    for (MultiScreenData data : datas) {
      screendataList.add(data);
    }
  }

  private void clearData() {
    nameLabel.textProperty().unbind();
    nameLabel.setText("");
    horLabel.setText("");
    verLabel.setText("");
    modeCheckBox.setSelected(false);
    spinner.getValueFactory().setValue(0);
    controlConChoiceBox.getItems().clear();
    view.getTargetItems().clear();
    view.getSourceItems().clear();
  }

  /** . */
  public void newAction() {
    window.showAndWait();
  }

  private void initNewAtctionWindow() {
    window.setTitle(
        Bundle.NbBundle.getMessage(
            MultiScreenControlPageController.class, "MultiScreenControl.NewAtctionWindow.title"));
    window.initModality(Modality.APPLICATION_MODAL);
    window.setResizable(false);
    window.setMinWidth(300);
    window.setMinHeight(150);

    Label msg = new Label();
    msg.setText(
        Bundle.NbBundle.getMessage(
            MultiScreenControlPageController.class, "MultiScreenControl.NewAtctionWindow.msg"));

    ObservableList<Integer> options = FXCollections.observableArrayList();
    int len = 16;
    for (int ii = 1; ii < len + 1; ii++) {
      options.add(ii);
    }

    ComboBox<Integer> cb1 = new ComboBox<>(options);
    cb1.getSelectionModel().selectFirst();
    cb1.getSelectionModel().selectedItemProperty().addListener(new ComboBoxChangeListenerH());

    ComboBox<Integer> cb2 = new ComboBox<>(options);
    cb2.getSelectionModel().selectFirst();
    cb2.getSelectionModel().selectedItemProperty().addListener(new ComboBoxChangeListenerV());

    Button commitNew =
        new Button(
            Bundle.NbBundle.getMessage(
                MultiScreenControlPageController.class,
                "MultiScreenControl.NewAtctionWindow.commitNew"));
    commitNew.setDefaultButton(true);
    commitNew.setOnAction(new CommitEventHandler());
    Button cancelNew =
        new Button(
            Bundle.NbBundle.getMessage(
                MultiScreenControlPageController.class,
                "MultiScreenControl.NewAtctionWindow.cancelNew"));
    cancelNew.setOnAction(new CancelEventHandler());

    GridPane gridpane = new GridPane();
    gridpane.setPadding(new Insets(15));
    gridpane.setHgap(5);
    gridpane.setVgap(5);
    gridpane.add(msg, 0, 0);
    gridpane.add(cb1, 1, 1);
    gridpane.add(cb2, 2, 1);
    gridpane.add(commitNew, 2, 2);
    gridpane.add(cancelNew, 3, 2);

    Scene scene = new Scene(gridpane);
    window.setScene(scene);
  }

  /** create a new MultiScreenData. */
  public void newMultiScreenData(int horNum, int verNum) {
    MultiScreenData msd =
        deviceController.getDataModel().getConfigDataManager().getFreeMultiScreenData();
    if (msd != null) {
      Threshold oldThreshold = msd.getThreshold();
      msd.setThreshold(AbstractData.THRESHOLD_UI_LOCAL_CHANGES);
      msd.setName("default");
      msd.setHorizontalNumber(horNum);
      msd.setVerticalNumber(verNum);
      msd.setThreshold(oldThreshold);
      screendataList.add(msd);
      tableView.getSelectionModel().select(msd);
    }
  }

  private static class IndexCell extends TableCell<MultiScreenData, String> {
    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);

      if (!empty) {
        int rowIndex = this.getIndex() + 1;
        this.setText(String.valueOf(rowIndex));
      }
    }
  }

  private class CommitEventHandler implements EventHandler<ActionEvent> {

    @Override
    public void handle(ActionEvent event) {
      int horNum = num1.get();
      int verNum = num2.get();
      window.close();
      if (horNum * verNum <= 16) {
        newMultiScreenData(horNum, verNum);
      } else {
        UndecoratedAlert alert = new UndecoratedAlert(AlertExType.WARNING);
        if (tableView != null && tableView.getScene() != null) {
          alert.initOwner(tableView.getScene().getWindow());
        }
        alert.setTitle(
            Bundle.NbBundle.getMessage(
                MultiScreenControlPageController.class,
                "MultiScreenControl.NewAtctionWindow.commitNewError.title"));
        alert.setContentText(
            Bundle.NbBundle.getMessage(
                MultiScreenControlPageController.class,
                "MultiScreenControl.NewAtctionWindow.commitNewError.text"));
        alert.showAndWait();
      }
    }
  }

  private class CancelEventHandler implements EventHandler<ActionEvent> {

    @Override
    public void handle(ActionEvent event) {
      window.close();
    }
  }

  private class ComboBoxChangeListenerH implements ChangeListener<Integer> {

    @Override
    public void changed(
        ObservableValue<? extends Integer> observable, Integer oldValue, Integer newValue) {
      num1.set(newValue);
    }
  }

  private class ComboBoxChangeListenerV implements ChangeListener<Integer> {

    @Override
    public void changed(
        ObservableValue<? extends Integer> observable, Integer oldValue, Integer newValue) {
      num2.set(newValue);
    }
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceController;
  }
}
