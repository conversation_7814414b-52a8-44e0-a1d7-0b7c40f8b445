package com.mc.tool.caesar.vpm.pages.systemedit.view;

import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarTerminalBase;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.TerminalPropertyConverter;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.PortComparator;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.util.function.Consumer;
import javafx.beans.Observable;
import javafx.beans.binding.Bindings;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.collections.transformation.FilteredListEx;
import javafx.collections.transformation.SortedList;
import javafx.event.EventHandler;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.util.Callback;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class TerminalListView extends TableView<VisualEditTerminal> {

  private TableColumn<VisualEditTerminal, String> indexCol;
  private TableColumn<VisualEditTerminal, Number> idCol;
  private TableColumn<VisualEditTerminal, String> nameCol;
  private TableColumn<VisualEditTerminal, String> portCol;
  private TableColumn<VisualEditTerminal, String> typeCol;
  private TableColumn<VisualEditTerminal, String> serialCol;

  private FilteredListEx<VisualEditTerminal> filteredListEx;

  private EventHandler<MouseEvent> idClickEvent;
  private EventHandler<MouseEvent> nameClickEvent;

  /** terminal列表. */
  public TerminalListView() {

    indexCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.list.index"));
    indexCol.prefWidthProperty().bind(widthProperty().multiply(0.1));
    indexCol.setId("index-col");
    idCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.list.id"));
    idCol.prefWidthProperty().bind(widthProperty().multiply(0.1));
    idCol.setId("id-col");
    nameCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.list.device"));
    nameCol.prefWidthProperty().bind(widthProperty().multiply(0.2));
    nameCol.setId("name-col");
    portCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.list.port"));
    portCol.prefWidthProperty().bind(widthProperty().multiply(0.2));
    portCol.setId("port-col");
    typeCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.list.type"));
    typeCol.prefWidthProperty().bind(widthProperty().multiply(0.15));
    typeCol.setId("type-col");
    serialCol = new TableColumn<>(CaesarI18nCommonResource.getString("systemedit.list.serial"));
    serialCol.prefWidthProperty().bind(widthProperty().multiply(0.25));
    serialCol.setId("serial-col");

    getColumns().addAll(indexCol, idCol, nameCol, portCol, typeCol, serialCol);
    setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);

    indexCol.setCellFactory(new IndexCellFactory());
    indexCol.setStyle("-fx-alignment:CENTER");
    indexCol.setSortable(false);

    idCol.setCellValueFactory(
        cell -> {
          if (cell.getValue() instanceof CaesarCpuTerminal) {
            CaesarCpuTerminal caesarCpuTerminal = (CaesarCpuTerminal) cell.getValue();
            return Bindings.createIntegerBinding(
                () -> {
                  if (caesarCpuTerminal.getCpuData() != null) {
                    return caesarCpuTerminal.getCpuData().getIdProperty().get();
                  }
                  return null;
                },
                caesarCpuTerminal.cpuDataProperty());
          } else if (cell.getValue() instanceof CaesarConTerminal) {
            CaesarConTerminal caesarConTerminal = (CaesarConTerminal) cell.getValue();
            return Bindings.createIntegerBinding(
                () -> {
                  if (caesarConTerminal.getConsoleData() != null) {
                    return caesarConTerminal.getConsoleData().getIdProperty().get();
                  }
                  return null;
                },
                caesarConTerminal.conDataProperty());
          }
          return new SimpleIntegerProperty();
        });
    idCol.setStyle("-fx-alignment:CENTER");

    nameCol.setCellValueFactory(
        (cell) -> TerminalPropertyConverter.getNameProperty(cell.getValue()));

    portCol.setCellValueFactory(
        (cell) -> TerminalPropertyConverter.getPortProperty(cell.getValue()));
    portCol.setComparator(new PortComparator());

    serialCol.setCellValueFactory(
        (cell) -> TerminalPropertyConverter.getSerialProperty(cell.getValue()));

    typeCol.setCellValueFactory(
        (cell) -> TerminalPropertyConverter.getTypeProperty(cell.getValue()));
  }

  /**
   * 初始化.
   *
   * @param model model
   * @param idDoubleClickHandler id项双击处理
   * @param nameDoubleClickHandler 名称项双击处理
   */
  public void init(
      VisualEditModel model,
      Consumer<VisualEditTerminal> idDoubleClickHandler,
      Consumer<VisualEditTerminal> nameDoubleClickHandler) {
    // 添加点击事件
    idClickEvent =
        (event) -> {
          if (event.getClickCount() >= 2
              && event.getButton() == MouseButton.PRIMARY
              && event.getSource() instanceof TableCell) {
            TableCell<VisualEditTerminal, Number> tableCell =
                (TableCell<VisualEditTerminal, Number>) event.getSource();
            Object obj = tableCell.getTableRow().getItem();
            if (obj instanceof VisualEditTerminal) {
              idDoubleClickHandler.accept((VisualEditTerminal) obj);
            }
          }
        };
    nameClickEvent =
        (event) -> {
          if (event.getClickCount() >= 2
              && event.getButton() == MouseButton.PRIMARY
              && event.getSource() instanceof TableCell) {
            TableCell<VisualEditTerminal, String> tableCell =
                (TableCell<VisualEditTerminal, String>) event.getSource();
            Object obj = tableCell.getTableRow().getItem();
            if (obj instanceof VisualEditTerminal) {
              nameDoubleClickHandler.accept((VisualEditTerminal) obj);
            }
          }
        };

    idCol.setCellFactory(
        (col) -> {
          TableCell<VisualEditTerminal, Number> cell =
              ((Callback<
                          TableColumn<VisualEditTerminal, Number>,
                          TableCell<VisualEditTerminal, Number>>)
                      ((Callback) TableColumn.DEFAULT_CELL_FACTORY))
                  .call(col);
          cell.addEventHandler(MouseEvent.MOUSE_CLICKED, idClickEvent);
          return cell;
        });

    nameCol.setCellFactory(
        (col) -> {
          TableCell<VisualEditTerminal, String> cell =
              ((Callback<
                          TableColumn<VisualEditTerminal, String>,
                          TableCell<VisualEditTerminal, String>>)
                      ((Callback) TableColumn.DEFAULT_CELL_FACTORY))
                  .call(col);
          cell.addEventHandler(MouseEvent.MOUSE_CLICKED, nameClickEvent);
          return cell;
        });

    // 初始化列表
    filteredListEx =
        new FilteredListEx<>(
            model.getAllTerminals(),
            (item) -> {
              if (item instanceof CaesarTerminalBase) {
                CaesarTerminalBase terminalBase = (CaesarTerminalBase) item;
                return terminalBase.getPort1ConnectedProperty().get()
                    || terminalBase.getPort2ConnectedProperty().get();
              }
              return false;
            });

    filteredListEx.setExtractor(
        (item) -> {
          if (item instanceof CaesarTerminalBase) {
            CaesarTerminalBase terminalBase = (CaesarTerminalBase) item;
            return new Observable[] {
              terminalBase.getPort1ConnectedProperty(), terminalBase.getPort2ConnectedProperty()
            };
          } else {
            return new Observable[0];
          }
        });

    SortedList<VisualEditTerminal> sortedList = new SortedList<>(filteredListEx);
    sortedList.comparatorProperty().bind(comparatorProperty());
    setItems(sortedList);
  }

  static class IndexCellFactory
      implements Callback<
          TableColumn<VisualEditTerminal, String>, TableCell<VisualEditTerminal, String>> {

    @Override
    public TableCell<VisualEditTerminal, String> call(
        TableColumn<VisualEditTerminal, String> param) {
      TableCell<VisualEditTerminal, String> cell = new TableCell<>();
      cell.textProperty()
          .bind(
              Bindings.createStringBinding(
                  () -> {
                    if (cell.isEmpty()) {
                      return null;
                    } else {
                      return String.format("%04d", cell.getIndex() + 1);
                    }
                  },
                  cell.indexProperty(),
                  cell.emptyProperty()));
      return cell;
    }
  }
}
