package com.mc.tool.caesar.vpm.pages.systemedit.property;

import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarConPropertySender<T> implements CaesarPropertySender<T> {
  private final CaesarDeviceController controller;
  private final String propertyName;
  private final ConsoleData conData;

  /**
   * Constructor.
   *
   * @param controller device controller
   * @param conData console data
   * @param propertyName property name
   */
  public CaesarConPropertySender(
      CaesarDeviceController controller, ConsoleData conData, String propertyName) {
    this.controller = controller;
    this.propertyName = propertyName;
    this.conData = conData;
  }

  @Override
  public void sendValue(T value) {
    if (controller == null || controller.getDataModel() == null) {
      return;
    }
    controller.execute(
        () -> {
          try {
            conData.setProperty(propertyName, value);
            controller.getDataModel().sendConsoleData(Collections.singletonList(conData));
          } catch (DeviceConnectionException | BusyException exception) {
            log.error("Fail to send value!", exception);
          }
        });
  }
}
