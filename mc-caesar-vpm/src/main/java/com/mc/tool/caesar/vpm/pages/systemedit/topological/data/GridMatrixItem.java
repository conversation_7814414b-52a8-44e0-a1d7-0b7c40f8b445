package com.mc.tool.caesar.vpm.pages.systemedit.topological.data;

import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.graph.interfaces.ExtraActionInfo;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class GridMatrixItem implements CellBindedObject {
  public static final int CONNECTOR = 1;

  @Getter private StringProperty nameProperty = new SimpleStringProperty();

  @Getter @Setter private int position = 0;

  @Getter private Map<GridMatrixItem, Set<GridMatrixLink>> relatedItems = new HashMap<>();

  ObservableList<ConnectorIdentifier> connectors = FXCollections.observableArrayList();

  public GridMatrixItem() {
    connectors.add(ConnectorIdentifier.getIdentifier(CONNECTOR));
  }

  /**
   * 添加相关的矩阵项.
   *
   * @param item 矩阵项
   * @param localPort 连接的本地端口
   * @param remotePort 连接的远端端口
   */
  public void addRelatedMatrix(GridMatrixItem item, int localPort, int remotePort) {
    Set<GridMatrixLink> set = relatedItems.get(item);
    if (set == null) {
      set = new HashSet<>();
    }
    GridMatrixLink link = new GridMatrixLink(localPort, remotePort);
    set.add(link);
    relatedItems.put(item, set);
  }

  @Override
  public int hashCode() {
    return nameProperty.get().hashCode() ^ position;
  }

  @Override
  public boolean equals(Object obj) {
    if (obj instanceof GridMatrixItem) {
      GridMatrixItem input = (GridMatrixItem) obj;
      return input.nameProperty.get().equals(nameProperty.get()) && input.position == position;
    } else {
      return false;
    }
  }

  ///  继承自CellBindedObject

  @Override
  public ObservableList<ConnectorIdentifier> getConnectorId() {
    return connectors;
  }

  @Override
  public Collection<ExtraActionInfo> getExtraActions() {
    return Collections.emptyList();
  }

  ///
  public String getName() {
    return nameProperty.get();
  }
}
