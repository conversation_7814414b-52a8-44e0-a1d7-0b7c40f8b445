package com.mc.tool.caesar.vpm.kaito.model;

import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * DecodeGroupUpdateInfo.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class DecodeGroupUpdateInfo {
  private String name;
  private String rights;
  private boolean croppable;
  private List<Integer> cards = new ArrayList<>();
}
