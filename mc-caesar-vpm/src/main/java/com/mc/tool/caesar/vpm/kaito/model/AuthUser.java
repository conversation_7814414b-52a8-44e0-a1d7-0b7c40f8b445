package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import lombok.Setter;

/**
 * 认证用户.
 */
@Setter
@JsonPropertyOrder({
    AuthUser.JSON_PROPERTY_ID,
    AuthUser.JSON_PROPERTY_NAME,
    AuthUser.JSON_PROPERTY_GENDER,
    AuthUser.JSON_PROPERTY_MOBILE,
    AuthUser.JSON_PROPERTY_STATUS,
    AuthUser.JSON_PROPERTY_BOUND_USER,
    AuthUser.JSON_PROPERTY_FACE_FEATURE,
    AuthUser.JSON_PROPERTY_FINGERPRINT_FEATURE,
    AuthUser.JSON_PROPERTY_REGISTER_TIME,
    AuthUser.JSON_PROPERTY_LAST_LOGIN_TIME
})
public class AuthUser {
  public static final String JSON_PROPERTY_ID = "id";
  private Integer id;

  public static final String JSON_PROPERTY_NAME = "name";
  private String name;

  public static final String JSON_PROPERTY_GENDER = "gender";
  private String gender;

  public static final String JSON_PROPERTY_MOBILE = "mobile";
  private String mobile;

  public static final String JSON_PROPERTY_STATUS = "status";
  private Integer status;

  public static final String JSON_PROPERTY_BOUND_USER = "bound_user";
  private Integer boundUser;

  public static final String JSON_PROPERTY_FACE_FEATURE = "face_feature";
  private String faceFeature;

  public static final String JSON_PROPERTY_FINGERPRINT_FEATURE = "fingerprint_feature";
  private String fingerprintFeature;

  public static final String JSON_PROPERTY_REGISTER_TIME = "register_time";
  private String registerTime;

  public static final String JSON_PROPERTY_LAST_LOGIN_TIME = "last_login_time";
  private String lastLoginTime;

  /**
   * 设置用户ID.
   */
  public AuthUser id(Integer id) {
    this.id = id;
    return this;
  }

  /**
   * 用户ID.
   *
   * @return id
   **/
  @NotNull
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude()
  public Integer getId() {
    return id;
  }

  /**
   * 设置用户名.
   */
  public AuthUser name(String name) {
    this.name = name;
    return this;
  }

  /**
   * 用户名.
   *
   * @return name
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public String getName() {
    return name;
  }

  /**
   * 设置性别.
   */
  public AuthUser gender(String gender) {
    this.gender = gender;
    return this;
  }

  /**
   * 性别.
   *
   * @return gender
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_GENDER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public String getGender() {
    return gender;
  }

  /**
   * 设置电话号码.
   */
  public AuthUser mobile(String mobile) {
    this.mobile = mobile;
    return this;
  }

  /**
   * 电话号码.
   *
   * @return mobile
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_MOBILE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public String getMobile() {
    return mobile;
  }

  /**
   * 状态.
   */
  public AuthUser status(Integer status) {
    this.status = status;
    return this;
  }

  /**
   * 用户当前状态，0为正常，1为冻结.
   *
   * @return status
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getStatus() {
    return status;
  }

  /**
   * 设置绑定的KVM用户.
   */
  public AuthUser boundUser(Integer boundUser) {
    this.boundUser = boundUser;
    return this;
  }

  /**
   * 绑定的KVM用户的ID,如果没有绑定，值为-1.
   *
   * @return boundUser
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_BOUND_USER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getBoundUser() {
    return boundUser;
  }

  /**
   * 设置人脸特征.
   */
  public AuthUser faceFeature(String faceFeature) {
    this.faceFeature = faceFeature;
    return this;
  }

  /**
   * 用户人脸特征的base64编码字符串，如果没有人脸特征，值为空字符串.
   *
   * @return faceFeature
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_FACE_FEATURE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public String getFaceFeature() {
    return faceFeature;
  }

  /**
   * 设置指纹特征.
   */
  public AuthUser fingerprintFeature(String fingerprintFeature) {
    this.fingerprintFeature = fingerprintFeature;
    return this;
  }

  /**
   * 用户指纹特征的base64编码字符串，如果没有指纹特征，值为空字符串.
   *
   * @return fingerprintFeature
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_FINGERPRINT_FEATURE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public String getFingerprintFeature() {
    return fingerprintFeature;
  }

  /**
   * 设置注册时间.
   */
  public AuthUser registerTime(String registerTime) {
    this.registerTime = registerTime;
    return this;
  }

  /**
   * 注册时间.
   *
   * @return registerTime
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_REGISTER_TIME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public String getRegisterTime() {
    return registerTime;
  }

  /**
   * 设置上次登录时间.
   */
  public AuthUser lastLoginTime(String lastLoginTime) {
    this.lastLoginTime = lastLoginTime;
    return this;
  }

  /**
   * 上次登录时间.
   *
   * @return lastLoginTime
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_LAST_LOGIN_TIME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLastLoginTime() {
    return lastLoginTime;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AuthUser authUser = (AuthUser) o;
    return Objects.equals(this.id, authUser.id)
        && Objects.equals(this.name, authUser.name)
        && Objects.equals(this.gender, authUser.gender)
        && Objects.equals(this.mobile, authUser.mobile)
        && Objects.equals(this.status, authUser.status)
        && Objects.equals(this.boundUser, authUser.boundUser)
        && Objects.equals(this.faceFeature, authUser.faceFeature)
        && Objects.equals(this.fingerprintFeature, authUser.fingerprintFeature)
        && Objects.equals(this.registerTime, authUser.registerTime)
        && Objects.equals(this.lastLoginTime, authUser.lastLoginTime);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, name, gender, mobile, status, boundUser, faceFeature,
        fingerprintFeature, registerTime, lastLoginTime);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("AuthUser {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    gender: ").append(toIndentedString(gender)).append("\n");
    sb.append("    mobile: ").append(toIndentedString(mobile)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    boundUser: ").append(toIndentedString(boundUser)).append("\n");
    sb.append("    faceFeature: ").append(toIndentedString(faceFeature)).append("\n");
    sb.append("    fingerprintFeature: ").append(toIndentedString(fingerprintFeature)).append("\n");
    sb.append("    registerTime: ").append(toIndentedString(registerTime)).append("\n");
    sb.append("    lastLoginTime: ").append(toIndentedString(lastLoginTime)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

