package com.mc.tool.caesar.vpm.validation.constraints;

import com.mc.tool.caesar.vpm.validation.constraints.impl.UniqueVirtualIpImpl;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import javax.validation.Constraint;
import javax.validation.Payload;

/**
 * 验证虚拟IP不能与网络配置1和网络配置3的IP地址相同.
 * 此注解应用于类级别，验证整个Bean的虚拟IP字段.
 */
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE,
    ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {UniqueVirtualIpImpl.class})
public @interface UniqueVirtualIp {
  
  /**
   * 获取校验错误的文本.
   *
   * @return 校验文本
   */
  String message() default "{com.mc.tool.caesar.vpm.validation.constraints.uniquevirtualip.message}";

  /**
   * Groups.
   *
   * @return groups
   */
  Class<?>[] groups() default {};

  /**
   * Payload.
   *
   * @return payloads
   */
  Class<? extends Payload>[] payload() default {};
}
