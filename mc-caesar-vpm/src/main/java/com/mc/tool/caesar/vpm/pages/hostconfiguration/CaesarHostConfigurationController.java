package com.mc.tool.caesar.vpm.pages.hostconfiguration;

import com.dooapp.fxform.FXForm;
import com.dooapp.fxform.model.Element;
import com.dooapp.fxform.model.impl.BufferedElement;
import com.dooapp.fxform.view.FXFormSkin;
import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.datamodel.SystemConfigData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDemoDeviceController;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.activateconfig.ActivateConfigPageView;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.interfaces.ViewControllable;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.List;
import java.util.ResourceBundle;
import java.util.function.UnaryOperator;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.collections.ListChangeListener;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.CheckBox;
import javafx.scene.control.ScrollPane;
import javafx.scene.control.Tab;
import javafx.scene.control.TabPane;
import javafx.scene.control.TextField;
import javafx.scene.control.TextFormatter;
import javafx.scene.input.MouseEvent;
import javax.validation.ConstraintViolation;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import tornadofx.control.DateTimePicker;

/**
 * .
 */
@Slf4j
public class CaesarHostConfigurationController implements Initializable, ViewControllable {

  @Getter @Setter private FXForm<GeneralConfigurationBean> generalForm;
  @Setter private FXForm<NetworkConfigurationBean> networkForm;
  @Setter private FXForm<SnmpConfigurationBean> snmpForm;
  @Setter private FXForm<RedundancyBean> rdForm;

  @Setter private FXForm<EventConfigurationBean> eventForm;
  @Getter @Setter private ActivateConfigPageView activateConfigPane;
  @FXML private TabPane tabPane;
  @FXML private ScrollPane generalPane;
  @FXML private ScrollPane networkPane;
  @FXML private ScrollPane snmpPane;
  @FXML private ScrollPane redundancyPane;
  @FXML private ScrollPane eventPane;
  @FXML private Tab activateConfigTap;
  @FXML private Button generalCommitButton;
  @FXML private Button generalReloadButton;
  @FXML private Button networkCommitButton;
  @FXML private Button networkReloadButton;
  @FXML private Button snmpCommitButton;
  @FXML private Button snmpReloadButton;
  @FXML private Button redundancyCommitButton;
  @FXML private Button redundancyReloadButton;
  @FXML private Button eventCommitButton;
  @FXML private Button eventReloadButton;

  private WeakAdapter weakAdapter = new WeakAdapter();

  private CaesarDeviceController deviceController = null;

  private BooleanProperty disable = new SimpleBooleanProperty(true);
  private BooleanProperty isSynchronize = new SimpleBooleanProperty(false);
  private BooleanProperty isEchoOnly = new SimpleBooleanProperty(false);
  private BooleanProperty isGrid = new SimpleBooleanProperty(false);

  private BooleanProperty isEnableRemoteLog = new SimpleBooleanProperty(false);

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
      updateUserRightStatus();
      updateGridStatus();
      this.deviceController
          .getDataModel()
          .addPropertyChangeListener(
              new String[] {SystemConfigData.PROPERTY_FORCE_BITS},
              evt -> PlatformUtility.runInFxThread(this::updateGridStatus));

      if (this.deviceController instanceof CaesarDemoDeviceController
          || this.deviceController.getDataModel().getConfigData().getMatrixStatus().isDoubleBackup()) {
        ((TextField) networkForm.getEditor(NetworkConfigurationBean.IP_ADDRESS_2).getNode())
            .setTextFormatter(new TextFormatter<>(new IpFilter()));
        ((TextField) networkForm.getEditor(NetworkConfigurationBean.NETMASK_2).getNode())
            .setTextFormatter(new TextFormatter<>(new IpFilter()));
        ((TextField) networkForm.getEditor(NetworkConfigurationBean.GATEWAY_2).getNode())
            .setTextFormatter(new TextFormatter<>(new IpFilter()));
      }
    } else {
      log.warn("Error device controller type : {} !", deviceController.getClass().getName());
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    // 通用配置
    generalCommitButton.setOnAction(
        (event) -> {
          boolean timeChange = generalForm.isElementChange("time");
          generalForm.commit();
          deviceController.execute(
              () -> {
                try {
                  deviceController.getDataModel().sendSystemData();
                  if (timeChange) {
                    deviceController.getDataModel().setTime(generalForm.getSource().getTime());
                  }
                } catch (DeviceConnectionException | BusyException | ConfigException ex) {
                  log.warn("False to send system data!", ex);
                }
              });
        });
    generalReloadButton.setOnAction(
        (event) -> {
          generalForm.reload();
          // 避免刷新后disable状态异常
          Boolean mouseConnect =
              (Boolean) generalForm.findElementByName("isKeyboardMouseConnect").getValue();
          generalForm.getLabel("timeoutShare").getNode().setDisable(!mouseConnect);
          generalForm.getTooltip("timeoutShare").getNode().setDisable(!mouseConnect);
          generalForm.getEditor("timeoutShare").getNode().setDisable(!mouseConnect);
        });

    generalCommitButton.setDisable(true);
    generalReloadButton.setDisable(true);
    generalForm
        .isChange()
        .addListener(
            weakAdapter.wrap(
                (obs, oldVal, newVal) -> {
                  if (generalForm.isChange().get()
                      && generalForm.getConstraintViolations().isEmpty()) {
                    generalCommitButton.setDisable(false);
                    generalReloadButton.setDisable(false);
                  } else if (!generalForm.getConstraintViolations().isEmpty()) {
                    generalCommitButton.setDisable(true);
                    generalReloadButton.setDisable(false);
                  } else {
                    generalCommitButton.setDisable(true);
                    generalReloadButton.setDisable(true);
                  }
                }));
    generalForm
        .getConstraintViolations()
        .addListener(
            weakAdapter.wrap(
                (ListChangeListener<ConstraintViolation>)
                    change -> {
                      if (generalForm.isChange().get()
                          && generalForm.getConstraintViolations().isEmpty()) {
                        generalCommitButton.setDisable(false);
                        generalReloadButton.setDisable(false);
                      } else if (!generalForm.getConstraintViolations().isEmpty()) {
                        generalCommitButton.setDisable(true);
                        generalReloadButton.setDisable(false);
                      } else {
                        generalCommitButton.setDisable(true);
                        generalReloadButton.setDisable(true);
                      }
                    }));

    // 数字框输入限制
    ((TextField) generalForm.getEditor("timeoutDisplay").getNode())
        .setTextFormatter(new TextFormatter<>(new PosNumFilter()));
    ((TextField) generalForm.getEditor("timeoutDisconnect").getNode())
        .setTextFormatter(new TextFormatter<>(new PosNumFilter()));
    ((TextField) generalForm.getEditor("timeoutShare").getNode())
        .setTextFormatter(new TextFormatter<>(new PosNumFilter()));
    ((TextField) generalForm.getEditor("autoRejectTime").getNode())
        .setTextFormatter(new TextFormatter<>(new PosNumFilter()));
    ((TextField) generalForm.getEditor("timeoutLogout").getNode())
        .setTextFormatter(new TextFormatter<>(new NumFilter()));
    ((TextField) generalForm.getEditor("logServerIp").getNode())
        .setTextFormatter(new TextFormatter<>(new IpFilter()));

    // 获取本地时间
    EventHandler<MouseEvent> clickHandler =
        (event) -> ((DateTimePicker) generalForm.getEditor("time").getNode())
                .setDateTimeValue(LocalDateTime.now());
    generalForm.getTooltip("time").getNode()
        .addEventHandler(MouseEvent.MOUSE_CLICKED, weakAdapter.wrap(clickHandler));

    // 同步强制用户登录进入OSD与允许用户OSD权限管理的勾选
    Element isLoginElement = generalForm.findElementByName("isLogin");
    Element isUserLockElement = generalForm.findElementByName("isUserLock");
    ((BufferedElement<?>) isLoginElement)
        .addListener(
            weakAdapter.wrap(
                (observable, oldValue, newValue) ->
                    ((CheckBox) generalForm.getEditor(isUserLockElement).getNode())
                        .setSelected((Boolean) isLoginElement.getValue())));
    ((BufferedElement<?>) isUserLockElement)
        .addListener(
            weakAdapter.wrap(
                (observable, oldValue, newValue) ->
                    ((CheckBox)
                            ((FXFormSkin) generalForm.getSkin())
                                .getEditor(isLoginElement)
                                .getNode())
                        .setSelected((Boolean) isUserLockElement.getValue())));

    //
    Element isCpuConnectElement = generalForm.findElementByName("isCpuConnect");
    Element isConDisconnectElement = generalForm.findElementByName("isConDisconnect");
    Element isCpuWatchElement = generalForm.findElementByName("isCpuWatch");
    ((BufferedElement<?>) isCpuConnectElement)
        .addListener(
            weakAdapter.wrap(
                (observable, oldValue, newValue) -> {
                  if (newValue.equals(true)) {
                    ((CheckBox) generalForm.getEditor(isCpuWatchElement).getNode())
                        .setSelected(true);
                    ((CheckBox) generalForm.getEditor(isConDisconnectElement).getNode())
                        .setSelected(false);
                  }
                }));
    ((BufferedElement<?>) isCpuWatchElement)
        .addListener(
            weakAdapter.wrap(
                (observable, oldValue, newValue) -> {
                  if (newValue.equals(false)) {
                    ((CheckBox) generalForm.getEditor(isConDisconnectElement).getNode())
                        .setSelected(false);
                    ((CheckBox) generalForm.getEditor(isCpuConnectElement).getNode())
                        .setSelected(false);
                  }
                }));
    ((BufferedElement<?>) isConDisconnectElement)
        .addListener(
            weakAdapter.wrap(
                (observable, oldValue, newValue) -> {
                  if (newValue.equals(true)) {
                    ((CheckBox) generalForm.getEditor(isCpuWatchElement).getNode())
                        .setSelected(true);
                    ((CheckBox) generalForm.getEditor(isCpuConnectElement).getNode())
                        .setSelected(false);
                  }
                }));

    // 键盘鼠标权限枪占控制权限释放时间的可编辑性
    Element isKeyboardMouseConnectElement = generalForm.findElementByName("isKeyboardMouseConnect");
    Boolean isKeyboardMouseConnectValue = (Boolean) isKeyboardMouseConnectElement.getValue();
    Element timeoutShareElement = generalForm.findElementByName("timeoutShare");
    generalForm.getLabel(timeoutShareElement).getNode().setDisable(!isKeyboardMouseConnectValue);
    generalForm.getTooltip(timeoutShareElement).getNode().setDisable(!isKeyboardMouseConnectValue);
    generalForm.getEditor(timeoutShareElement).getNode().setDisable(!isKeyboardMouseConnectValue);
    ((BufferedElement<?>) isKeyboardMouseConnectElement)
        .addListener(
            weakAdapter.wrap(
                (observable, oldValue, newValue) -> {
                  if (newValue != null) {
                    generalForm.getLabel(timeoutShareElement).getNode()
                        .setDisable(!isKeyboardMouseConnectValue);
                    generalForm.getTooltip(timeoutShareElement).getNode()
                        .setDisable(!isKeyboardMouseConnectValue);
                    generalForm.getEditor(timeoutShareElement).getNode()
                        .setDisable(!isKeyboardMouseConnectValue);
                  }
                }));

    // CheckBox不勾选时，其Label，Tooltip为Disable
    List<Element> booleanElements = generalForm.findBooleanElement();
    for (Element element : booleanElements) {
      if (generalForm.getLabel(element) != null) {
        generalForm.getLabel(element).getNode().disableProperty()
            .bind(((CheckBox) generalForm.getEditor(element).getNode()).selectedProperty().not());
      }
      if (generalForm.getTooltip(element) != null) {
        generalForm.getTooltip(element).getNode().disableProperty()
            .bind(((CheckBox) generalForm.getEditor(element).getNode()).selectedProperty().not());
      }
    }

    // 远程日志
    isEnableRemoteLog.bind(
        ((CheckBox) generalForm.getEditor("isEnableRemoteLog").getNode()).selectedProperty());
    Element logServerIpElement = generalForm.findElementByName("logServerIp");
    generalForm.getLabel(logServerIpElement).getNode().disableProperty()
        .bind(isEnableRemoteLog.not());
    generalForm.getEditor(logServerIpElement).getNode().disableProperty()
        .bind(isEnableRemoteLog.not());

    generalPane.setContent(generalForm);

    // 网络配置
    networkCommitButton.setOnAction(
        (event) -> {
          networkForm.commit();
          deviceController.execute(
              () -> {
                try {
                  deviceController.getDataModel().sendSystemData();
                } catch (DeviceConnectionException | BusyException ex) {
                  log.warn("False to send system data!", ex);
                }
              });
        });
    networkReloadButton.setOnAction((event) -> networkForm.reload());

    networkCommitButton.setDisable(true);
    networkReloadButton.setDisable(true);
    networkForm
        .isChange()
        .addListener(
            weakAdapter.wrap(
                (obs, oldVal, newVal) -> {
                  if (networkForm.isChange().get()
                      && networkForm.getConstraintViolations().isEmpty()) {
                    networkCommitButton.setDisable(false);
                    networkReloadButton.setDisable(false);
                  } else if (!networkForm.getConstraintViolations().isEmpty()) {
                    networkCommitButton.setDisable(true);
                    networkReloadButton.setDisable(false);
                  } else {
                    networkCommitButton.setDisable(true);
                    networkReloadButton.setDisable(true);
                  }
                }));
    networkForm
        .getConstraintViolations()
        .addListener(
            weakAdapter.wrap(
                (ListChangeListener<ConstraintViolation>)
                    change -> {
                      if (change.next()) {
                        if (networkForm.isChange().get()
                            && networkForm.getConstraintViolations().isEmpty()) {
                          networkCommitButton.setDisable(false);
                          networkReloadButton.setDisable(false);
                        } else if (!networkForm.getConstraintViolations().isEmpty()) {
                          networkCommitButton.setDisable(true);
                          networkReloadButton.setDisable(false);
                        } else {
                          networkCommitButton.setDisable(true);
                          networkReloadButton.setDisable(true);
                        }
                      }
                    }));

    // 输入框限制输入
    ((TextField) networkForm.getEditor(NetworkConfigurationBean.IP_ADDRESS_1).getNode())
        .setTextFormatter(new TextFormatter<>(new IpFilter()));
    ((TextField) networkForm.getEditor(NetworkConfigurationBean.NETMASK_1).getNode())
        .setTextFormatter(new TextFormatter<>(new IpFilter()));
    ((TextField) networkForm.getEditor(NetworkConfigurationBean.GATEWAY_1).getNode())
        .setTextFormatter(new TextFormatter<>(new IpFilter()));

    networkForm.getEditor(NetworkConfigurationBean.MAC_ADDRESS_1).getNode()
        .disableProperty().bind(disable);

    networkPane.setContent(networkForm);

    // SNMP配置
    snmpCommitButton.setOnAction(
        (event) -> {
          snmpForm.commit();
          deviceController.execute(
              () -> {
                try {
                  deviceController.getDataModel().sendSystemData();
                } catch (DeviceConnectionException | BusyException ex) {
                  log.warn("False to send system data!", ex);
                }
              });
        });
    snmpReloadButton.setOnAction((event) -> snmpForm.reload());

    snmpCommitButton.setDisable(true);
    snmpReloadButton.setDisable(true);
    snmpForm
        .isChange()
        .addListener(
            weakAdapter.wrap(
                (obs, oldVal, newVal) -> {
                  if (snmpForm.isChange().get() && snmpForm.getConstraintViolations().isEmpty()) {
                    snmpCommitButton.setDisable(false);
                    snmpReloadButton.setDisable(false);
                  } else if (!snmpForm.getConstraintViolations().isEmpty()) {
                    snmpCommitButton.setDisable(true);
                    snmpReloadButton.setDisable(false);
                  } else {
                    snmpCommitButton.setDisable(true);
                    snmpReloadButton.setDisable(true);
                  }
                }));
    snmpForm
        .getConstraintViolations()
        .addListener(
            weakAdapter.wrap(
                (ListChangeListener<ConstraintViolation>)
                    change -> {
                      if (change.next()) {
                        if (snmpForm.isChange().get()
                            && snmpForm.getConstraintViolations().isEmpty()) {
                          snmpCommitButton.setDisable(false);
                          snmpReloadButton.setDisable(false);
                        } else if (!snmpForm.getConstraintViolations().isEmpty()) {
                          snmpCommitButton.setDisable(true);
                          snmpReloadButton.setDisable(false);
                        } else {
                          snmpCommitButton.setDisable(true);
                          snmpReloadButton.setDisable(true);
                        }
                      }
                    }));

    // 端口不可更改
    snmpForm.getEditor("snmpPort").getNode().disableProperty().bind(disable);
    snmpForm.getEditor("snmp1TrapPort").getNode().disableProperty().bind(disable);
    snmpForm.getEditor("snmp2TrapPort").getNode().disableProperty().bind(disable);
    // 输入框限制输入
    ((TextField) snmpForm.getEditor("snmp1Address").getNode())
        .setTextFormatter(new TextFormatter<>(new IpFilter()));
    ((TextField) snmpForm.getEditor("snmp2Address").getNode())
        .setTextFormatter(new TextFormatter<>(new IpFilter()));
    // CheckBox不勾选时，其Label，Tooltip为Disable
    List<Element> booleanSnmpElements = snmpForm.findBooleanElement();
    for (Element element : booleanSnmpElements) {
      if (snmpForm.getTooltip(element) != null) {
        snmpForm.getTooltip(element).getNode().disableProperty()
            .bind(((CheckBox) snmpForm.getEditor(element).getNode()).selectedProperty().not());
      }
      if (snmpForm.getLabel(element) != null) {
        snmpForm.getLabel(element).getNode().disableProperty()
            .bind(((CheckBox) snmpForm.getEditor(element).getNode()).selectedProperty().not());
      }
    }

    snmpPane.setContent(snmpForm);

    // 双机冗余
    redundancyCommitButton.setOnAction(
        (event) -> {
          rdForm.commit();
          deviceController.execute(
              () -> {
                try {
                  deviceController.getDataModel().sendSystemData();
                } catch (DeviceConnectionException | BusyException ex) {
                  log.warn("False to send system data!", ex);
                }
              });
        });
    redundancyReloadButton.setOnAction((event) -> rdForm.reload());

    redundancyCommitButton.setDisable(true);
    redundancyReloadButton.setDisable(true);
    rdForm
        .isChange()
        .addListener(
            weakAdapter.wrap(
                (obs, oldVal, newVal) -> {
                  if (rdForm.isChange().get() && rdForm.getConstraintViolations().isEmpty()) {
                    redundancyCommitButton.setDisable(false);
                    redundancyReloadButton.setDisable(false);
                  } else if (!rdForm.getConstraintViolations().isEmpty()) {
                    redundancyCommitButton.setDisable(true);
                    redundancyReloadButton.setDisable(false);
                  } else {
                    redundancyCommitButton.setDisable(true);
                    redundancyReloadButton.setDisable(true);
                  }
                }));
    rdForm
        .getConstraintViolations()
        .addListener(
            weakAdapter.wrap(
                (ListChangeListener<ConstraintViolation>)
                    change -> {
                      if (rdForm.isChange().get() && rdForm.getConstraintViolations().isEmpty()) {
                        redundancyCommitButton.setDisable(false);
                        redundancyReloadButton.setDisable(false);
                      } else if (!rdForm.getConstraintViolations().isEmpty()) {
                        redundancyCommitButton.setDisable(true);
                        redundancyReloadButton.setDisable(false);
                      } else {
                        redundancyCommitButton.setDisable(true);
                        redundancyReloadButton.setDisable(true);
                      }
                    }));

    rdForm.getTooltip("isRedundancy").getNode().disableProperty().bind(
        ((CheckBox) rdForm.getEditor("isRedundancy").getNode()).selectedProperty().not());
    rdForm.getTooltip("isSynchronize").getNode().disableProperty().bind(
        ((CheckBox) rdForm.getEditor("isSynchronize").getNode()).selectedProperty().not());
    isSynchronize.bind(
        ((CheckBox) rdForm.getEditor("isSynchronize").getNode()).selectedProperty());
    rdForm.getTooltip("isEchoOnly").getNode().disableProperty().bind(
        ((CheckBox) rdForm.getEditor("isEchoOnly").getNode()).selectedProperty().not());
    isEchoOnly.bind(
        ((CheckBox) rdForm.getEditor("isEchoOnly").getNode()).selectedProperty());
    rdForm.getLabel("masterIp").getNode().disableProperty().bind(
        isSynchronize.not().and(isEchoOnly.not()));
    rdForm.getEditor("masterIp").getNode().disableProperty().bind(
        isSynchronize.not().and(isEchoOnly.not()));
    rdForm.getTooltip("isDefaultBackup").getNode().disableProperty().bind(
        isSynchronize.not().and(isEchoOnly.not()));
    rdForm.getEditor("isDefaultBackup").getNode().disableProperty().bind(
        isSynchronize.not().and(isEchoOnly.not()));
    rdForm.getLabel("virtualIp").getNode().disableProperty().bind(
        isSynchronize.not().and(isEchoOnly.not()));
    rdForm.getEditor("virtualIp").getNode().disableProperty().bind(
        isSynchronize.not().and(isEchoOnly.not()));
    rdForm.getLabel("virtualRouteId").getNode().disableProperty().bind(
        isSynchronize.not().and(isEchoOnly.not()));
    rdForm.getEditor("virtualRouteId").getNode().disableProperty().bind(
        isSynchronize.not().and(isEchoOnly.not()));


    redundancyPane.setContent(rdForm);

    // 事件配置
    eventCommitButton.setOnAction(
        (event) -> {
          eventForm.commit();
          deviceController.execute(
              () -> {
                try {
                  deviceController.getDataModel().sendSystemData();
                } catch (DeviceConnectionException | BusyException ex) {
                  log.warn("False to send system data!", ex);
                }
              });
        });
    eventReloadButton.setOnAction((event) -> eventForm.reload());

    eventCommitButton.setDisable(true);
    eventReloadButton.setDisable(true);
    eventForm
        .isChange()
        .addListener(
            weakAdapter.wrap(
                (obs, oldVal, newVal) -> {
                  if (eventForm.isChange().get()
                      && eventForm.getConstraintViolations().isEmpty()) {
                    eventCommitButton.setDisable(false);
                    eventReloadButton.setDisable(false);
                  } else if (!eventForm.getConstraintViolations().isEmpty()) {
                    eventCommitButton.setDisable(true);
                    eventReloadButton.setDisable(false);
                  } else {
                    eventCommitButton.setDisable(true);
                    eventReloadButton.setDisable(true);
                  }
                }));
    eventForm
        .getConstraintViolations()
        .addListener(
            weakAdapter.wrap(
                (ListChangeListener<ConstraintViolation>)
                    change -> {
                      if (change.next()) {
                        if (eventForm.isChange().get()
                            && eventForm.getConstraintViolations().isEmpty()) {
                          eventCommitButton.setDisable(false);
                          eventReloadButton.setDisable(false);
                        } else if (!eventForm.getConstraintViolations().isEmpty()) {
                          eventCommitButton.setDisable(true);
                          eventReloadButton.setDisable(false);
                        } else {
                          eventCommitButton.setDisable(true);
                          eventReloadButton.setDisable(true);
                        }
                      }
                    }));
    BooleanProperty enable =
        ((CheckBox) eventForm.getEditor("enable").getNode()).selectedProperty();
    eventForm.getEditor("osdWarning").getNode().disableProperty().bind(enable.not());
    eventForm.getLabel("osdWarning").getNode().disableProperty().bind(enable.not());
    eventForm.getEditor("buzzerWarning").getNode().disableProperty().bind(enable.not());
    eventForm.getLabel("buzzerWarning").getNode().disableProperty().bind(enable.not());
    eventForm.getEditor("audioThreshold").getNode().disableProperty().bind(enable.not());
    eventPane.setContent(eventForm);

    // 激活配置
    activateConfigTap.setContent(activateConfigPane);

    //
    generalForm.getEditor("hostName").getNode().disableProperty().bind(isGrid);
    generalForm.getEditor("configurationName").getNode().disableProperty().bind(isGrid);
    networkForm.disableProperty().bind(isGrid);
  }

  @Override
  public CaesarDeviceController getDeviceController() {
    return deviceController;
  }

  @Override
  public void close() {
  }

  @Override
  public void setEntity(Entity entity) {
  }

  private void updateUserRightStatus() {
    if (deviceController == null) {
      return;
    }
    if (!deviceController.getCaesarUserRight().isActivateConfigVisible()) {
      tabPane.getTabs().remove(activateConfigTap);
    }
  }

  private void updateGridStatus() {
    isGrid.set(
        getDeviceController()
            .getDataModel()
            .getConfigData()
            .getSystemConfigData()
            .isMatrixGridEnabled());
  }

  private static class PosNumFilter implements UnaryOperator<TextFormatter.Change> {

    @Override
    public TextFormatter.Change apply(TextFormatter.Change change) {
      if (!change.getText().matches("[0-9]*")) {
        change.setText("");
      }
      return change;
    }
  }

  private static class NumFilter implements UnaryOperator<TextFormatter.Change> {

    @Override
    public TextFormatter.Change apply(TextFormatter.Change change) {
      if (!change.getText().matches("(-|[0-9])*")) {
        change.setText("");
      }
      return change;
    }
  }

  private static class IpFilter implements UnaryOperator<TextFormatter.Change> {

    @Override
    public TextFormatter.Change apply(TextFormatter.Change change) {
      if (!change.getText().matches("((\\.)|[0-9])*")) {
        change.setText("");
      }
      return change;
    }
  }
}
