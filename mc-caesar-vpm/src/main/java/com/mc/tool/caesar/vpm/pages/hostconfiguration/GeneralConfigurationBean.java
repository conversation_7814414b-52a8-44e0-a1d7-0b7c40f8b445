package com.mc.tool.caesar.vpm.pages.hostconfiguration;

import com.mc.common.validation.constraints.StringFormat;
import com.mc.common.validation.constraints.StringLength;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.SystemConfigData;
import com.mc.tool.caesar.api.utils.IpUtil;
import java.time.LocalDateTime;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class GeneralConfigurationBean {

  private SystemConfigData systemConfigData;
  private String hostName;
  private String configurationName;
  private String notes;

  private ObjectProperty<LocalDateTime> time = new SimpleObjectProperty<>();

  private Boolean isLogin;
  private Boolean isUserLock;
  private Boolean isConLock;
  private Boolean isUserAccess;
  private Boolean isConAccess;
  private Boolean isCpuDisconnect;
  private Integer timeoutDisplay;
  private Integer timeoutLogout;

  private Boolean isCpuWatch;
  private Boolean isCpuConnect;
  private Boolean isConDisconnect;
  private Boolean isKeyboardMouseConnect;
  private Integer timeoutDisconnect;
  private Integer timeoutShare;

  private Boolean isUart;
  private Boolean isComEcho;
  private Boolean isLanEcho;
  private Boolean isForcePushGet;
  private Integer autoRejectTime;

  private Boolean isDebug;
  private Boolean isInfo;
  private Boolean isNotice;
  private Boolean isWarning;
  private Boolean isError;

  private Boolean isEnableRemoteLog;
  private Boolean isEnableLocalLog;
  private String logServerIp;
  private Boolean isFaceLogin;
  private ObjectProperty<ConnectMode> connectMode = new SimpleObjectProperty<>(ConnectMode.NONE);

  /**
   * Constructor.
   */
  public GeneralConfigurationBean(SystemConfigData systemConfigData, LocalDateTime time) {
    this.systemConfigData = systemConfigData;
    this.time.set(time);
    initConnectMode();
    connectMode.addListener((observable, oldValue, newValue) -> {
      if (newValue != oldValue) {
        switch (newValue) {
          case FULL_CONNECT:
            systemConfigData.setDefaultMode(1);
            break;
          case VIDEO_CONNECT:
            systemConfigData.setDefaultMode(2);
            break;
          default:
            systemConfigData.setDefaultMode(0);
            break;
        }
      }
    });
  }

  @StringLength(max = CaesarConstants.NAME_LEN)
  public String getHostName() {
    hostName = systemConfigData.getSystemData().getDevice();
    return hostName;
  }

  public void setHostName(String device) {
    systemConfigData.getSystemData().setDevice(device);
  }

  @StringLength(max = CaesarConstants.NAME_LEN)
  public String getConfigurationName() {
    configurationName = systemConfigData.getSystemData().getName();
    return configurationName;
  }

  public void setConfigurationName(String device) {
    systemConfigData.getSystemData().setName(device);
  }

  @StringLength(max = CaesarConstants.MATRIX_INFO_LEN)
  public String getNotes() {
    notes = systemConfigData.getSystemData().getInfo();
    return notes;
  }

  public void setNotes(String device) {
    systemConfigData.getSystemData().setInfo(device);
  }

  public LocalDateTime getTime() {
    return time.get();
  }

  public void setTime(LocalDateTime time) {
    this.time.set(time);
  }

  public Boolean getIsLogin() {
    isLogin = systemConfigData.getAccessData().isLogin();
    return isLogin;
  }

  public void setIsLogin(Boolean enabled) {
    systemConfigData.getAccessData().setLogin(enabled);
  }

  public Boolean getIsUserLock() {
    isUserLock = systemConfigData.getAccessData().isUserLock();
    return isUserLock;
  }

  public void setIsUserLock(Boolean enabled) {
    systemConfigData.getAccessData().setUserLock(enabled);
  }

  public Boolean getIsConLock() {
    isConLock = systemConfigData.getAccessData().isConLock();
    return isConLock;
  }

  public void setIsConLock(Boolean enabled) {
    systemConfigData.getAccessData().setConLock(enabled);
  }

  public Boolean getIsUserAccess() {
    isUserAccess = systemConfigData.getAccessData().isUserAccess();
    return isUserAccess;
  }

  public void setIsUserAccess(Boolean enabled) {
    systemConfigData.getAccessData().setUserAccess(enabled);
  }

  public Boolean getIsConAccess() {
    isConAccess = systemConfigData.getAccessData().isConAccess();
    return isConAccess;
  }

  public void setIsConAccess(Boolean enabled) {
    systemConfigData.getAccessData().setConAccess(enabled);
  }

  public Boolean getIsCpuDisconnect() {
    isCpuDisconnect = systemConfigData.getAccessData().isCpuDisconnect();
    return isCpuDisconnect;
  }

  public void setIsCpuDisconnect(Boolean enabled) {
    systemConfigData.getAccessData().setCpuDisconnect(enabled);
  }

  @Min(value = 0)
  @NotNull(message = "{com.mc.tool.caesar.vpm.pages.hostconfiguration.constraints.notnull.message}")
  public Integer getTimeoutDisplay() {
    timeoutDisplay = systemConfigData.getAccessData().getTimeoutDisplay();
    return timeoutDisplay;
  }

  public void setTimeoutDisplay(Integer timeoutDisplay) {
    systemConfigData.getAccessData().setTimeoutDisplay(timeoutDisplay);
  }

  @Min(value = -1)
  @NotNull(message = "{com.mc.tool.caesar.vpm.pages.hostconfiguration.constraints.notnull.message}")
  public Integer getTimeoutLogout() {
    timeoutLogout = systemConfigData.getAccessData().getTimeoutLogout();
    return timeoutLogout;
  }

  public void setTimeoutLogout(Integer timeoutLogout) {
    systemConfigData.getAccessData().setTimeoutLogout(timeoutLogout);
  }

  public Boolean getIsCpuWatch() {
    isCpuWatch = systemConfigData.getSwitchData().isCpuWatch();
    return isCpuWatch;
  }

  public void setIsCpuWatch(Boolean enabled) {
    systemConfigData.getSwitchData().setCpuWatch(enabled);
  }

  public Boolean getIsCpuConnect() {
    isCpuConnect = systemConfigData.getSwitchData().isCpuConnect();
    return isCpuConnect;
  }

  public void setIsCpuConnect(Boolean enabled) {
    systemConfigData.getSwitchData().setCpuConnect(enabled);
  }

  public Boolean getIsConDisconnect() {
    isConDisconnect = systemConfigData.getSwitchData().isConDisconnect();
    return isConDisconnect;
  }

  public void setIsConDisconnect(Boolean enabled) {
    systemConfigData.getSwitchData().setConDisconnect(enabled);
  }

  public Boolean getIsKeyboardMouseConnect() {
    isKeyboardMouseConnect = systemConfigData.getSwitchData().isKeyboardMouseConnect();
    return isKeyboardMouseConnect;
  }

  public void setIsKeyboardMouseConnect(Boolean enabled) {
    systemConfigData.getSwitchData().setKeyboardMouseConnect(enabled);
  }

  @Min(value = 0)
  @NotNull(message = "{com.mc.tool.caesar.vpm.pages.hostconfiguration.constraints.notnull.message}")
  public Integer getTimeoutDisconnect() {
    timeoutDisconnect = systemConfigData.getSwitchData().getTimeoutDisconnect();
    return timeoutDisconnect;
  }

  public void setTimeoutDisconnect(Integer timeoutDisconnect) {
    systemConfigData.getSwitchData().setTimeoutDisconnect(timeoutDisconnect);
  }

  @Min(value = 0)
  @NotNull(message = "{com.mc.tool.caesar.vpm.pages.hostconfiguration.constraints.notnull.message}")
  public Integer getTimeoutShare() {
    timeoutShare = systemConfigData.getSwitchData().getTimeoutShare();
    return timeoutShare;
  }

  public void setTimeoutShare(Integer timeoutShare) {
    systemConfigData.getSwitchData().setTimeoutShare(timeoutShare);
  }

  //
  public Boolean getIsUart() {
    isUart = systemConfigData.getSystemData().isUartEnable();
    return isUart;
  }

  public void setIsUart(Boolean enabled) {
    systemConfigData.getSystemData().setUartEnable(enabled);
  }

  public Boolean getIsComEcho() {
    isComEcho = systemConfigData.getSystemData().isComEcho();
    return isComEcho;
  }

  public void setIsComEcho(Boolean enabled) {
    systemConfigData.getSystemData().setComEcho(enabled);
  }

  public Boolean getIsLanEcho() {
    isLanEcho = systemConfigData.getSystemData().isLanEcho();
    return isLanEcho;
  }

  public void setIsLanEcho(Boolean enabled) {
    systemConfigData.getSystemData().setLanEcho(enabled);
  }

  public void setIsForcePushGet(Boolean enabled) {
    isForcePushGet = enabled;
    systemConfigData.getSystemData().setForcePushGet(enabled);
  }

  public Boolean getIsForcePushGet() {
    isForcePushGet = systemConfigData.getSystemData().isForcePushGet();
    return isForcePushGet;
  }

  public void setAutoRejectTime(Integer value) {
    autoRejectTime = value;
    systemConfigData.setAutoRejectTime(value);
  }

  @Min(value = 3)
  @Max(value = 127)
  @NotNull(message = "{com.mc.tool.caesar.vpm.pages.hostconfiguration.constraints.notnull.message}")
  public Integer getAutoRejectTime() {
    autoRejectTime = systemConfigData.getAutoRejectTime();
    return autoRejectTime;
  }

  // SysLog
  public Boolean getIsDebug() {
    isDebug = systemConfigData.isInternalLogLevelDebug();
    return isDebug;
  }

  /**
   * 设置系统Debug日志.
   */
  public void setIsDebug(Boolean enabled) {
    if (systemConfigData.isInternalLogLevelDebug() != enabled) {
      String masterIp = systemConfigData.getSystemData().getMasterIpProperty().getValueSafe();
      log.info("host({}) set Internal Log Level Debug {}", masterIp, enabled);
      systemConfigData.setInternalLogLevelDebug(enabled);
    }
  }

  public Boolean getIsInfo() {
    isInfo = systemConfigData.isInternalLogLevelInfo();
    return isInfo;
  }

  /**
   * 设置系统Info日志.
   */
  public void setIsInfo(Boolean enabled) {
    if (systemConfigData.isInternalLogLevelInfo() != enabled) {
      String masterIp = systemConfigData.getSystemData().getMasterIpProperty().getValueSafe();
      log.info("host({}) set Internal Log Level Info {}", masterIp, enabled);
      systemConfigData.setInternalLogLevelInfo(enabled);
    }
  }

  public Boolean getIsNotice() {
    isNotice = systemConfigData.isInternalLogLevelNotice();
    return isNotice;
  }

  /**
   * 设置系统Notice日志.
   */
  public void setIsNotice(Boolean enabled) {
    if (systemConfigData.isInternalLogLevelNotice() != enabled) {
      String masterIp = systemConfigData.getSystemData().getMasterIpProperty().getValueSafe();
      log.info("host({}) set Internal Log Level Notice {}", masterIp, enabled);
      systemConfigData.setInternalLogLevelNotice(enabled);
    }
  }

  public Boolean getIsWarning() {
    isWarning = systemConfigData.isInternalLogLevelWarning();
    return isWarning;
  }

  /**
   * 设置系统Warning日志.
   */
  public void setIsWarning(Boolean enabled) {
    if (systemConfigData.isInternalLogLevelWarning() != enabled) {
      String masterIp = systemConfigData.getSystemData().getMasterIpProperty().getValueSafe();
      log.info("host({}) set Internal Log Level Warning {}", masterIp, enabled);
      systemConfigData.setInternalLogLevelWarning(enabled);
    }
  }

  public Boolean getIsError() {
    isError = systemConfigData.isInternalLogLevelError();
    return isError;
  }

  /**
   * 设置系统Error日志.
   */
  public void setIsError(Boolean enabled) {
    if (systemConfigData.isInternalLogLevelError() != enabled) {
      String masterIp = systemConfigData.getSystemData().getMasterIpProperty().getValueSafe();
      log.info("host({}) set Internal Log Level Error {}", masterIp, enabled);
      systemConfigData.setInternalLogLevelError(enabled);
    }
  }

  public Boolean getIsEnableRemoteLog() {
    isEnableRemoteLog = systemConfigData.isEnableRemoteLog();
    return isEnableRemoteLog;
  }

  public Boolean getIsEnableLocalLog() {
    isEnableLocalLog = systemConfigData.isEnableLocalLog();
    return isEnableLocalLog;
  }

  public Boolean getIsFaceLogin() {
    isFaceLogin = systemConfigData.isFaceLogin();
    return isFaceLogin;
  }

  /**
   * 设置人脸登录配置.
   */
  public void setIsFaceLogin(Boolean enable) {
    if (systemConfigData.isFaceLogin() != enable) {
      systemConfigData.setFaceLogin(enable);
    }
  }

  /**
   * 设置远程日志.
   */
  public void setIsEnableRemoteLog(Boolean enabled) {
    if (systemConfigData.isEnableRemoteLog() != enabled) {
      String masterIp = systemConfigData.getSystemData().getMasterIpProperty().getValueSafe();
      log.info("host({}) set Remote Log {}", masterIp, enabled);
      systemConfigData.setEnabledRemoteLog(enabled);
    }
  }

  /**
   * 设置本地日志.
   */
  public void setIsEnableLocalLog(Boolean enabled) {
    if (systemConfigData.isEnableLocalLog() != enabled) {
      String masterIp = systemConfigData.getSystemData().getMasterIpProperty().getValueSafe();
      log.info("host({}) set Local Log {}", masterIp, enabled);
      systemConfigData.setEnableLocalLog(enabled);
    }
  }

  /**
   * getLogServerAddress. 匹配IP地址，并排除0.*.*.*和*.*.*.255
   */
  @StringFormat(
      format =
          "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\."
              + "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\."
              + "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\."
              + "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)")
  public String getLogServerIp() {
    logServerIp = IpUtil.getAddressString(systemConfigData.getSyslogData().getAddress());
    return logServerIp;
  }

  /**
   * setIpAddress.
   */
  public void setLogServerIp(String ip) {
    systemConfigData.getSyslogData().setAddress(IpUtil.getAddressByte(ip));
  }

  /**
   * getConnectMode.
   */
  public void initConnectMode() {
    if (systemConfigData.getDefaultMode() == 0) {
      connectMode.set(ConnectMode.NONE);
    } else if (systemConfigData.getDefaultMode() == 1) {
      connectMode.set(ConnectMode.FULL_CONNECT);
    } else if (systemConfigData.getDefaultMode() == 2) {
      connectMode.set(ConnectMode.VIDEO_CONNECT);
    } else {
      connectMode.set(ConnectMode.NONE);
    }
  }
}
