<?xml version="1.0" encoding="UTF-8"?>

<?import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridWizardIndicator?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ListView?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<VBox stylesheets="@activate_view.css" xmlns="http://javafx.com/javafx/8"
  xmlns:fx="http://javafx.com/fxml/1"
  prefWidth="700" prefHeight="550" id="main-container">
  <children>
    <MatrixGridWizardIndicator currentIndex="5"/>
    <VBox id="sub-container">
      <Region minHeight="45"/>
      <Label text="%activate.info1" wrapText="true"/>
      <Label text="%activate.info2" wrapText="true"/>
      <Label text="%activate.info3" wrapText="true"/>
      <Region minHeight="10"/>
      <TableView minHeight="149" fx:id="tableView">
        <columns>
          <TableColumn fx:id="fileCol" text="%activate.table.file"/>
          <TableColumn fx:id="nameCol" text="%activate.table.name"/>
          <TableColumn fx:id="infoCol" text="%activate.table.info"/>
          <TableColumn fx:id="ipCol" text="%activate.table.ip"/>
          <TableColumn fx:id="versionCol" text="%activate.table.version"/>
        </columns>
      </TableView>
      <Region minHeight="5"/>
      <HBox alignment="CENTER" minHeight="36">
        <Label text="%activate.log"/>
        <Region HBox.hgrow="ALWAYS"/>
        <Button styleClass="common-button" text="%activate.activate" onAction="#onActivate"
          fx:id="activateBtn"/>
        <Region prefWidth="10"/>
        <Button styleClass="common-button" text="%activate.savelog" onAction="#onSaveLog"/>
      </HBox>
      <ListView fx:id="logList" VBox.vgrow="ALWAYS"
        style="-fx-border-width: 1px; -fx-border-color:#e6e6e6;"/>
    </VBox>

  </children>
</VBox>
