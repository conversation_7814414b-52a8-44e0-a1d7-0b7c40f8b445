.caesar-system-edit-page .tab-label {
  -fx-graphic: url("pages/systemedit/logo_normal.png");
}

.caesar-system-edit-page:selected .tab-label {
  -fx-graphic: url("pages/systemedit/logo_hover.png");
}

.caesar-system-edit-page:hover .tab-label {
  -fx-graphic: url("pages/systemedit/logo_hover.png");
}

.caesar-operation-page .tab-label {
  -fx-graphic: url("pages/operation/logo_normal.png");
}

.caesar-operation-page:selected .tab-label {
  -fx-graphic: url("pages/operation/logo_hover.png");
}

.caesar-operation-page:hover .tab-label {
  -fx-graphic: url("pages/operation/logo_hover.png");
}

.caesar-office-page .tab-label {
  -fx-graphic: url("pages/office/logo_normal.png");
}

.caesar-office-page:selected .tab-label {
  -fx-graphic: url("pages/office/logo_hover.png");
}

.caesar-office-page:hover .tab-label {
  -fx-graphic: url("pages/office/logo_hover.png");
}

.caesar-device-manage-page .tab-label {
  -fx-graphic: url("pages/devicemanagement/logo_normal.png");
}

.caesar-device-manage-page:selected .tab-label {
  -fx-graphic: url("pages/devicemanagement/logo_hover.png");
}

.caesar-device-manage-page:hover .tab-label {
  -fx-graphic: url("pages/devicemanagement/logo_hover.png");
}

.caesar-hostconfiguration-page .tab-label {
  -fx-graphic: url("pages/hostconfiguration/logo_normal.png");
}

.caesar-hostconfiguration-page:selected .tab-label {
  -fx-graphic: url("pages/hostconfiguration/logo_hover.png");
}

.caesar-hostconfiguration-page:hover .tab-label {
  -fx-graphic: url("pages/hostconfiguration/logo_hover.png");
}

.caesar-permissionconfiguration-page .tab-label {
  -fx-graphic: url("pages/permissionconfiguration/logo_normal.png");
}

.caesar-permissionconfiguration-page:selected .tab-label {
  -fx-graphic: url("pages/permissionconfiguration/logo_hover.png");
}

.caesar-permissionconfiguration-page:hover .tab-label {
  -fx-graphic: url("pages/permissionconfiguration/logo_hover.png");
}

.caesar-hotkeyconfiguration-page .tab-label {
  -fx-graphic: url("pages/hotkeyconfiguration/logo_normal.png");
}

.caesar-hotkeyconfiguration-page:selected .tab-label {
  -fx-graphic: url("pages/hotkeyconfiguration/logo_hover.png");
}

.caesar-hotkeyconfiguration-page:hover .tab-label {
  -fx-graphic: url("pages/hotkeyconfiguration/logo_hover.png");
}

.caesar-hostupdate-page .tab-label {
  -fx-graphic: url("pages/hostupdate/logo_normal.png");
}

.caesar-hostupdate-page:selected .tab-label {
  -fx-graphic: url("pages/hostupdate/logo_hover.png");
}

.caesar-hostupdate-page:hover .tab-label {
  -fx-graphic: url("pages/hostupdate/logo_hover.png");
}

.caesar-extenderupdate-page .tab-label {
  -fx-graphic: url("pages/extenderupdate/logo_normal.png");
}

.caesar-extenderupdate-page:selected .tab-label {
  -fx-graphic: url("pages/extenderupdate/logo_hover.png");
}

.caesar-extenderupdate-page:hover .tab-label {
  -fx-graphic: url("pages/extenderupdate/logo_hover.png");
}

.application-logo {
  -fx-background-image: url("caesar_logo.png");
  -fx-pref-height: 101;
  -fx-min-height: 101;
  -fx-background-position: center;
  -fx-background-repeat: no-repeat;
}