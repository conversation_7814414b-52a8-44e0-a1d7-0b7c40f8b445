<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.CheckBox?>
<?import javafx.scene.control.ChoiceBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Spinner?>
<?import javafx.scene.control.Tab?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TabPane?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.Cursor?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<fx:root type="javafx.scene.layout.VBox" xmlns="http://javafx.com/javafx/8"
  xmlns:fx="http://javafx.com/fxml/1">
  <children>
    <HBox prefHeight="500.0" prefWidth="1100.0" VBox.vgrow="ALWAYS">
      <children>
        <BorderPane>
          <center>
            <TableView fx:id="tableView" prefWidth="350.0" BorderPane.alignment="CENTER">
              <columns>
                <TableColumn fx:id="indexCol" prefWidth="30.0" resizable="false" text="C1"/>
                <TableColumn fx:id="nameCol" prefWidth="90.0" text="C2"/>
                <TableColumn fx:id="horCol" maxWidth="1.7976931348623157E308" text="C3"/>
                <TableColumn fx:id="verCol" maxWidth="1.7976931348623157E308" text="C3"/>
              </columns>
            </TableView>
          </center>
          <top>
            <HBox maxHeight="25.0" spacing="5.0" BorderPane.alignment="CENTER">
              <children>
                <Label prefHeight="25.0" prefWidth="80.0" text="Filter Table:"/>
                <TextField fx:id="filterField" maxHeight="25.0" HBox.hgrow="ALWAYS"/>
              </children>
            </HBox>
          </top>
        </BorderPane>
        <VBox HBox.hgrow="ALWAYS">
          <children>
            <GridPane fx:id="dataPane" VBox.vgrow="ALWAYS">
              <columnConstraints>
                <ColumnConstraints hgrow="ALWAYS" maxWidth="100.0" minWidth="100.0"
                  prefWidth="100.0"/>
                <ColumnConstraints hgrow="ALWAYS"/>
              </columnConstraints>
              <rowConstraints>
                <RowConstraints maxHeight="1.7976931348623157E308" minHeight="30.0"
                  prefHeight="40.0" vgrow="ALWAYS"/>
                <RowConstraints maxHeight="1.7976931348623157E308" minHeight="1.0" prefHeight="50.0"
                  vgrow="ALWAYS"/>
                <RowConstraints maxHeight="1.7976931348623157E308" minHeight="10.0"
                  prefHeight="50.0" vgrow="ALWAYS"/>
                <RowConstraints minHeight="10.0" prefHeight="55.0" vgrow="ALWAYS"/>
                <RowConstraints minHeight="10.0" prefHeight="55.0" vgrow="ALWAYS"/>
                <RowConstraints minHeight="10.0" prefHeight="50.0" vgrow="ALWAYS"/>
              </rowConstraints>
              <children>
                <Label fx:id="dataName" text="名称"/>
                <Label fx:id="dataMode" text="模式" GridPane.rowIndex="5"/>
                <Label fx:id="dataHorizontal" text="行" GridPane.rowIndex="1"/>
                <TextField fx:id="nameLabel" maxWidth="200.0" GridPane.columnIndex="1">
                  <opaqueInsets>
                    <Insets/>
                  </opaqueInsets>
                  <cursor>
                    <Cursor fx:constant="TEXT"/>
                  </cursor>
                </TextField>
                <TextField fx:id="horLabel" disable="true" editable="false" maxWidth="200.0"
                  GridPane.columnIndex="1" GridPane.rowIndex="1"/>
                <Label fx:id="dataVertical" text="列" GridPane.rowIndex="2"/>
                <Label fx:id="dataUsFrame" text="提示框时间" GridPane.rowIndex="4"/>
                <Label fx:id="dataCtrlConId" text="控制con" GridPane.rowIndex="3"/>
                <ChoiceBox fx:id="controlConChoiceBox" prefWidth="150.0" GridPane.columnIndex="1"
                  GridPane.rowIndex="3"/>
                <TextField fx:id="verLabel" disable="true" editable="false" maxWidth="200.0"
                  GridPane.columnIndex="1" GridPane.rowIndex="2"/>
                <HBox prefHeight="100.0" prefWidth="200.0" GridPane.columnIndex="1"
                  GridPane.rowIndex="5">
                  <children>
                    <CheckBox fx:id="modeCheckBox" mnemonicParsing="false" HBox.hgrow="ALWAYS"/>
                    <Label text="跨屏类型 0：鼠标跨屏 1：手动跨屏"/>
                  </children>
                </HBox>
                <Spinner fx:id="spinner" editable="true" GridPane.columnIndex="1"
                  GridPane.rowIndex="4"/>
              </children>
            </GridPane>
            <TabPane minHeight="300.0" tabClosingPolicy="UNAVAILABLE" VBox.vgrow="ALWAYS">
              <tabs>
                <Tab fx:id="tab" closable="false" text="tab"/>
              </tabs>
            </TabPane>
          </children>
        </VBox>
      </children>
    </HBox>
    <HBox>
      <children>
        <Region prefHeight="50.0" HBox.hgrow="ALWAYS"/>
        <Button fx:id="newButton" mnemonicParsing="false" prefWidth="70.0" text="New"/>
        <Button fx:id="deleteButton" mnemonicParsing="false" prefWidth="70.0" text="Delete">
          <HBox.margin>
            <Insets left="10.0" right="10.0"/>
          </HBox.margin>
        </Button>
        <Button fx:id="applyButton" mnemonicParsing="false" prefWidth="70.0" text="Apply"/>
        <Button fx:id="cancelButton" mnemonicParsing="false" prefWidth="70.0" text="Cancel">
          <HBox.margin>
            <Insets left="10.0" right="10.0"/>
          </HBox.margin>
        </Button>
      </children>
    </HBox>
  </children>
</fx:root>
