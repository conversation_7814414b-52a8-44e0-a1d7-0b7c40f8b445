MultiScreenControl.NewAtctionWindow.cancelNew=取消
MultiScreenControl.NewAtctionWindow.commitNew=提交
MultiScreenControl.NewAtctionWindow.commitNewError.text=总数不能大于16
MultiScreenControl.NewAtctionWindow.commitNewError.title=跨屏组创建不成功
MultiScreenControl.NewAtctionWindow.msg=行*列，总数不能大于16
MultiScreenControl.NewAtctionWindow.title=创建跨屏组
MultiScreenControl.applyButton.text=应用
MultiScreenControl.cancelButton.text=取消
MultiScreenControl.dataCtrlConId.text=控制Con
MultiScreenControl.dataHorizontal.text=行
MultiScreenControl.dataMode.text=模式
MultiScreenControl.dataUsFrame.text=提示框时间
MultiScreenControl.dataVertical.text=列
MultiScreenControl.deleteButton.text=删除
MultiScreenControl.nameLabel.text=名称
MultiScreenControl.newButton.text=新建
MultiScreenControl.tab.text=编辑
MultiScreenControl.tableView.horCol.text=行
MultiScreenControl.tableView.nameCol.text=名称
MultiScreenControl.tableView.verCol.text=列
