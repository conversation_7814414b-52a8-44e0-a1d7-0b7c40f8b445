<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.HBox?>
<fx:root stylesheets="@search_field.css" type="javafx.scene.layout.HBox"
  xmlns:fx="http://javafx.com/fxml/1" alignment="center" prefHeight="30" id="main-container">
  <children>
    <TextField HBox.Hgrow="ALWAYS" id="search-text" fx:id="searchText"/>
    <Label prefWidth="12" prefHeight="12" alignment="center" id="search-icon"/>
  </children>
</fx:root>
