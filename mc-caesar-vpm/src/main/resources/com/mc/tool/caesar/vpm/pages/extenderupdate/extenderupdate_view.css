@import "/com/mc/tool/framework/common.css";

#main-container {
  -fx-padding: 10px;
  -fx-spacing: 10px;
}

#tool-box {
  -fx-spacing: 10px;
}

#update-log-title {
  -fx-text-fill: #666666;
}

#update-log-title-box {
  -fx-padding: 10 0 0 0;
}

#loglist {
  -fx-border-size: 1;
  -fx-border-color: #e6e6e6;
}

.seperator {
  -fx-background-color: #cccccc;
}

#offline-btn {
  -fx-graphic: url("btn_normal.png");
}

#offline-btn:selected {
  -fx-text-fill: MC_COLOR;
  -fx-graphic: url("btn_hover.png");
}

#offline-btn:hover {
  -fx-text-fill: MC_COLOR;
  -fx-graphic: url("btn_hover.png");
}

#delTxgroup-btn {
  -fx-graphic: url("btn_normal.png");
}

#delTxgroup-btn:selected {
  -fx-text-fill: MC_COLOR;
  -fx-graphic: url("btn_hover.png");
}

#delTxgroup-btn:hover {
  -fx-text-fill: MC_COLOR;
  -fx-graphic: url("btn_hover.png");
}
