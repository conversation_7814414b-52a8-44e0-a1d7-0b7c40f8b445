osd:[t(200) l(200) w(80) h(400) c(a4ff) bc(80000000) a(63) id(0x0 0x1 0x2 0x3 0x0 0x0 0x0 0x0)]
input(iw ih ow oh):[0(1920 2160 960 1080) 1(1920 2160 960 1080) 2(1920 2160 960 1080) 3(1920 2160 960 1080) 4(1920 2160 960 1080) 5(1920 2160 960 1080) 6(1920 2160 960 1080) 7(1920 2160 960 1080) ]
vert_cut(start_line, video_src):[0(0 0) 1(0 4) 2(0 1) 3(0 5) 4(0 2) 5(0 6) 6(0 3) 7(0 7) 8(0 0) 9(0 0) 10(0 0) 11(0 0) 12(0 0) 13(0 0) 14(0 0) 15(0 0) 16(0 0) 17(0 0) 18(0 0) 19(0 0) ]
horz_cut(start_px, end_px, horz_cut_cnt, vert_cut_index):[0(0 959 0 0) 1(0 959 0 1) 2(0 959 0 2) 3(0 959 0 3) 4(0 959 0 4) 5(0 959 0 5) 6(0 959 0 6) 7(0 959 0 7) 8(0 0 0 20) 9(0 0 0 20) 10(0 0 0 20) 11(0 0 0 20) 12(0 0 0 20) 13(0 0 0 20) 14(0 0 0 20) 15(0 0 0 20) 16(0 0 0 20) 17(0 0 0 20) 18(0 0 0 20) 19(0 0 0 20) ]
output(oport olayer iw ih ow oh):[0(0 0 960 1080 960 1080) 1(0 1 960 1080 960 1080) 2(1 0 960 1080 960 1080) 3(1 1 960 1080 960 1080) 4(2 0 960 1080 960 1080) 5(2 1 960 1080 960 1080) 6(3 0 960 1080 960 1080) 7(3 1 960 1080 960 1080) 8(0 6 0 0 0 0) 9(0 6 0 0 0 0) 10(0 6 0 0 0 0) 11(0 6 0 0 0 0) 12(0 6 0 0 0 0) 13(0 6 0 0 0 0) 14(0 6 0 0 0 0) 15(0 6 0 0 0 0) 16(0 6 0 0 0 0) 17(0 6 0 0 0 0) 18(0 6 0 0 0 0) 19(0 6 0 0 0 0) ]
layer(start_line start_px w h a):8[0(0 0 960 1080 127) 1(0 960 960 1080 127) 6(0 0 960 1080 127) 7(0 960 960 1080 127) 12(0 0 960 1080 127) 13(0 960 960 1080 127) 18(0 0 960 1080 127) 19(0 960 960 1080 127) ]
