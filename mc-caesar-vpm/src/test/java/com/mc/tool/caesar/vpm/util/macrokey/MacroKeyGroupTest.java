package com.mc.tool.caesar.vpm.util.macrokey;

import com.mc.tool.caesar.api.CaesarConstants.FunctionKey.Cmd.Command;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData.MacroType;
import com.mc.tool.caesar.api.utils.SampleModelCreater.SampleModelInfo;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.util.CaesarSampleStatusCreater;
import com.mc.tool.caesar.vpm.util.macrokey.datamodel.Fkey;
import com.mc.tool.caesar.vpm.util.macrokey.datamodel.MacroDataObjectParam;
import com.mc.tool.caesar.vpm.util.macrokey.datamodel.MacroKeyGroup;
import com.mc.tool.caesar.vpm.util.macrokey.datamodel.MacroKeyItem;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.util.Pair;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * .
 */
public class MacroKeyGroupTest {

  @Before
  public void before() {
    System.setProperty("no-fx-mode", "true");
    System.setProperty("generator-mode", "true");
  }

  @SuppressWarnings("checkstyle:Indentation")
  @Test
  public void testConvert() {
    SampleModelInfo info = CaesarSampleStatusCreater.createSimpleSingleStatusInfo();
    info.setTxCount(16);
    info.setRxCount(16);
    CaesarDeviceController deviceController =
        CaesarSampleStatusCreater.createDeviceController(info);

    ObservableList<FunctionKeyData> keys = FXCollections.observableArrayList();
    List<ConsoleData> rxList =
        new ArrayList<>(deviceController.getDataModel().getConfigDataManager().getActiveConsoles());
    List<CpuData> txList =
        new ArrayList<>(deviceController.getDataModel().getConfigDataManager().getActiveCpus());
    for (int i = 0; i < 16; i++) {
      FunctionKeyData functionKeyData =
          deviceController.getDataModel().getConfigDataManager().getFreeFunctionKeyData();

      functionKeyData.setStatusActive(true);
      functionKeyData.setHostSubscript(rxList.get(0).getOid() + 1);
      functionKeyData.setHotkey(i);
      functionKeyData.setKeyline(i);
      functionKeyData.setMacroCmd(Command.Connect.getId());
      functionKeyData.setMacroType(MacroType.CON);
      functionKeyData.setIntParam(0, rxList.get(i).getOid() + 1);
      functionKeyData.setIntParam(1, txList.get(i).getOid() + 1);

      keys.add(functionKeyData);
    }

    MacroKeyGroup macroKeyGroup = new MacroKeyGroup(keys);
    macroKeyGroup.init(deviceController);
    macroKeyGroup.reload();
    Assert.assertFalse(macroKeyGroup.isChange());
    // 修改对象，但没有修改rx tx
    macroKeyGroup.getMacroKeys(Fkey.F1)[0].getParam1().set(new MacroDataObjectParam(rxList.get(0)));
    macroKeyGroup.getMacroKeys(Fkey.F1)[0].getParam2().set(new MacroDataObjectParam(txList.get(0)));
    Assert.assertFalse(macroKeyGroup.isChange());
    // 修改rx
    for (Fkey key : Fkey.values()) {
      macroKeyGroup.getMacroKeys(key)[key.ordinal()]
          .getParam1().set(new MacroDataObjectParam(rxList.get((key.ordinal() + 1) % 16)));
      Assert.assertTrue(macroKeyGroup.isChange());
      //
      Map<Pair<Integer, Integer>, MacroKeyItem> items =
          macroKeyGroup.getChangedItems(Lists.emptyList());
      Assert.assertEquals(1, items.size());
      Map.Entry<Pair<Integer, Integer>, MacroKeyItem> entry = items.entrySet().iterator().next();
      Assert.assertEquals(Integer.valueOf(key.ordinal()), entry.getKey().getKey());
      Assert.assertEquals(Integer.valueOf(key.ordinal()), entry.getKey().getValue());
      //
      macroKeyGroup.reload();
      Assert.assertEquals(
          new MacroDataObjectParam(rxList.get(key.ordinal())),
          macroKeyGroup.getMacroKeys(key)[key.ordinal()].getParam1().get());
    }
    // 修改tx
    for (Fkey key : Fkey.values()) {
      macroKeyGroup.getMacroKeys(key)[key.ordinal()]
          .getParam2().set(new MacroDataObjectParam(txList.get((key.ordinal() + 1) % 16)));
      Assert.assertTrue(macroKeyGroup.isChange());
      //
      Map<Pair<Integer, Integer>, MacroKeyItem> items =
          macroKeyGroup.getChangedItems(Lists.emptyList());
      Assert.assertEquals(1, items.size());
      Map.Entry<Pair<Integer, Integer>, MacroKeyItem> entry = items.entrySet().iterator().next();
      Assert.assertEquals(Integer.valueOf(key.ordinal()), entry.getKey().getKey());
      Assert.assertEquals(Integer.valueOf(key.ordinal()), entry.getKey().getValue());
      //
      macroKeyGroup.reload();
      Assert.assertEquals(
          new MacroDataObjectParam(txList.get(key.ordinal())),
          macroKeyGroup.getMacroKeys(key)[key.ordinal()].getParam2().get());
    }
    // 修改cmd
    for (Fkey key : Fkey.values()) {
      macroKeyGroup.getMacroKeys(key)[key.ordinal()].getCmd().set(Command.ConnectVideo);
      Assert.assertTrue(macroKeyGroup.isChange());
      //
      Map<Pair<Integer, Integer>, MacroKeyItem> items =
          macroKeyGroup.getChangedItems(Lists.emptyList());
      Assert.assertEquals(1, items.size());
      Map.Entry<Pair<Integer, Integer>, MacroKeyItem> entry = items.entrySet().iterator().next();
      Assert.assertEquals(Integer.valueOf(key.ordinal()), entry.getKey().getKey());
      Assert.assertEquals(Integer.valueOf(key.ordinal()), entry.getKey().getValue());
      //
      macroKeyGroup.reload();
      Assert.assertEquals(
          Command.Connect, macroKeyGroup.getMacroKeys(key)[key.ordinal()].getCmd().get());
    }
    // 内部添加key
    MacroKeyItem macroKeyItem = macroKeyGroup.getMacroKeys(Fkey.F1)[1];
    macroKeyItem.getCmd().set(Command.ConnectVideo);
    macroKeyItem.getParam1().set(new MacroDataObjectParam(rxList.get(0)));
    macroKeyItem.getParam2().set(new MacroDataObjectParam(txList.get(0)));
    Assert.assertTrue(macroKeyGroup.isChange());

    //
    Map<Pair<Integer, Integer>, MacroKeyItem> items =
        macroKeyGroup.getChangedItems(Lists.emptyList());
    Assert.assertEquals(1, items.size());
    Map.Entry<Pair<Integer, Integer>, MacroKeyItem> entry = items.entrySet().iterator().next();
    Assert.assertEquals(Integer.valueOf(0), entry.getKey().getKey());
    Assert.assertEquals(Integer.valueOf(1), entry.getKey().getValue());
    //
    macroKeyGroup.reload();
    Assert.assertEquals(Command.NoFunction, macroKeyItem.getCmd().get());
    // 外部添加key
    FunctionKeyData functionKeyData =
        deviceController.getDataModel().getConfigDataManager().getFreeFunctionKeyData();

    functionKeyData.setStatusActive(true);
    functionKeyData.setHostSubscript(rxList.get(0).getOid() + 1);
    functionKeyData.setHotkey(0);
    functionKeyData.setKeyline(1);
    functionKeyData.setMacroCmd(Command.Connect.getId());
    functionKeyData.setMacroType(MacroType.CON);
    functionKeyData.setIntParam(0, rxList.get(0).getOid() + 1);
    functionKeyData.setIntParam(1, txList.get(0).getOid() + 1);
    keys.add(functionKeyData);
    Assert.assertFalse(macroKeyGroup.isChange());
    Assert.assertEquals(Command.Connect, macroKeyGroup.getMacroKeys(Fkey.F1)[0].getCmd().get());
  }
}
