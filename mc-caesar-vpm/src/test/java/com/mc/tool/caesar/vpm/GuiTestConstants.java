package com.mc.tool.caesar.vpm;

/**
 * .
 */
public class GuiTestConstants {
  // main
  public static final String MENU_FILE = "#menuFile";
  public static final String MENU_EXPORT = "导出";
  public static final String MENU_EXPORT_GRAPH = "系统图";
  public static final String DOWNLOAD_CONFIG = "下载配置";
  public static final String UPLOAD_CONFIG = "上传配置";
  public static final String WIZARD_NEXT = "下一步";
  public static final String WIZARD_PREV = "上一步";
  public static final String WIZARD_FINISH = "完成";
  public static final String DIALOG_UPLOAD = "上传";
  public static final String TEXT_UPLOAD_IP = "#address";
  public static final String TEXT_UPLOAD_USER = "#user";
  public static final String TEXT_UPLOAD_PWD = "#password";
  public static final String CHECKBOX_UPLOAD_ACTIVATE = "#activateCkb";
  public static final String TABLE_UPLOAD_CONFIG_LIST = "#tableView";
  public static final String BUTTON_UPLOAD_BTN = "#btn";

  public static final String BUTTON_VALIDATE = "验证";

  public static final String BUTTON_CONNET_DEV = "连接设备";
  public static final String BUTTON_CONFIRM = "确定";
  public static final String BUTTON_APPLY = "应用";
  public static final String BUTTON_FINISH = "完成 ";
  public static final String BUTTON_CANCEL = "取消";
  public static final String BUTTON_YES = "是";
  public static final String BUTTON_NO = "否";
  public static final String BUTTON_CLOSE_ENTITY = "#close-btn";
  public static final String BUTTON_LIST_MENU = ".object-menu";
  public static final String MENU_CLOSE_ALL = "关闭全部";
  public static final String BUTTON_MOVE_TO_SOURCE = "#moveToSource";
  public static final String BUTTON_MOVE_TO_TARGET = "#moveToTarget";
  public static final String BUTTON_MOVE_TO_SOURCE_ALL = "#moveToSourceAll";
  public static final String BUTTON_MOVE_TO_TARGET_ALL = "#moveToTargetAll";

  public static final String SPINNER_BUTTON_INCREMENT = ".increment-arrow-button";
  public static final String SPINNER_BUTTON_DECREMENT = ".decrement-arrow-button";

  public static final String TEXT_IP_INPUT = "#ip-input";
  public static final String TEXT_USER_INPUT = "#user-input";
  public static final String TEXT_PWD_INPUT = "#pwd-input";
  public static final String INPUT_IP = "0.0.0.0";
  public static final String INPUT_USER = "admin";
  public static final String INPUT_PWD = "admin";

  public static final String TAB_PANE_PAGES = "#pages-tab";

  // system edit
  public static final String RESTORE_GRAPH = "#restore-btn";
  public static final String ZOOM_OUT_GRAPH = "#zoomout-btn";
  public static final String SWITCH_GRAPH = "#switch-btn";
  public static final String MATRIX_CELL = ".matrix";
  public static final String BUTTON_USB_BIND = "USB端口绑定";
  public static final String COMBO_USB_TYPE = "#type-form-editor";
  public static final String TEXT_PORT_INPUT = "#port-form-editor";
  public static final String COMBO_BINDING = "#binding-form-editor";
  public static final String MENU_DELETE = "删除";
  public static final String MENU_GROUP = "组合";
  public static final String MENU_CREATE_VIDEO_WALL = "创建视频墙";
  public static final String MENU_CREATE_CROSS_SCREEN = "创建跨屏组";
  public static final String MENU_CREATE_MULTI_SCREEN = "创建预案组";
  public static final String TEXT_TERMINAL_NAME = "#name-text";
  public static final String DIALOG_SET_NAME = "设置名称";
  public static final String DIALOG_SET_ID = "确认";
  public static final String SWITCH_LIST_GRAPH_BTN = "#list-graph-switch-btn";
  public static final String SWITCH_LIST_GRAPH_BTN_TEXT_LIST = "列表";
  public static final String SWITCH_LIST_GRAPH_BTN_TEXT_graph = "拓扑";
  public static final String TERMINAL_LIST_VIEW_CONTAINER = "#listView";
  public static final String TERMINAL_GRAPH_VIEW_CONTAINER = "#graphView";
  public static final String TERMINAL_LIST_VIEW = "#terminalListView";
  public static final String CON_00 = "CON_0";
  public static final String CON_01 = "CON_1";
  public static final String CON_100 = "CON_100";
  public static final String CPU_00 = "CPU_0";
  public static final String CPU_01 = "CPU_1";
  public static final String GRID_TOPOLOGICAL_GRAPH = "级联拓扑图";
  public static final String RX_PROP_NAME = "#prop-name";
  public static final String EDID_1_PROP = "#edid-1-property";
  public static final String RXNUM_PROP = "#rxnum-property";
  public static final String TXNUM_PROP = "#txnum-property";
  public static final String ACTION_MACRO = "宏控制";
  public static final String ACTION_CPU_RIGHTS = "权限管理";
  public static final String ACTION_FAVOURITES = "轮询顺序配置";
  public static final String TERMINAL_LIST_VIEW_ID_COL = "id-col";
  public static final String TERMINAL_LIST_VIEW_NAME_COL = "name-col";
  public static final String IMPORT_EXTENDER_INFO_BTN = "#import-extender-info-btn";
  public static final String EXPORT_EXTENDER_INFO_BTN = "#export-extender-info-btn";

  public static final String VP6_DEMO_NAME = "VP6_268369928";
  public static final String VP6_DEMO_SUB_NAME = "VP6_268369936";
  public static final String VP6_DEMO_SUB_NAME2 = "VP6_268369960";

  // operation
  public static final String PUBLISH_MODE = "#publish-mode";
  public static final String PUBLISH_MODE_AUTO = "自动发布";
  public static final String PUBLISH_MODE_MANUAL = "手动发布";
  public static final String VIDEO_SOURCE_TREE = "#sourceTree";
  public static final String MULTI_SCREEN_ROW = "#multiscreenRow";
  public static final String FUNC_COMBOBOX = "#function-combobox";
  public static final String VIDEO_WALL_PREWINDOW_BTN = "#pre-window";
  public static final String VIDEO_WALL_SAVE_BTN = "#save-btn";
  public static final String VIDEO_WALL_SAVE_AS_BTN = "#saveas-btn";
  public static final String VIDEO_WALL_SCREEN_BTN = "#screen-btn";
  public static final String VIDEO_WALL_AUTO_ARRANGE_BTN = "#autoArrangeBtn";
  public static final String VIDEO_WALL_VIDEO_LAYOUT_PROPERTY = "#videoLayoutPropertySheet";
  public static final String VIDEO_WALL_CONFIG_PROPERTY = "#configPropertySheet";
  public static final String VIDEO_WALL_LAYOUT_PROPERTY = "#layoutPropertySheet";
  public static final String VIDEO_WALL_OUTPUT_PROPERTY = "#outputPropertySheet";
  public static final String VIDEO_WALL_VIDEO_CELL = ".video-cell";
  // videowall
  public static final String TOOLBAR_VIDEOWALL_SHOW_MODE = "大屏显示模式";
  public static final String OSD_ALPHA_PROPERTY = "#osd-alpha-property";
  public static final String BGIMG_UPLOAD_PROPERTY = "#bgimg-upload-property";
  public static final String BGIMG_WIDTH_PROPERTY = "#bgimg-width-property";
  public static final String BGIMG_HEIGHT_PROPERTY = "#bgimg-height-property";
  public static final String WINDOW_BGIMG_UPLOAD = "上传背景图";
  public static final String BGIMG_UPLOAD_SELECT_BTN = "选择";
  public static final String BGIMG_UPLOAD_UPLOAD_BTN = "上传";

  // host config
  public static final String GENERAL_FORM_HOST_NAME = "#hostName-form-editor";
  public static final String GENERAL_FORM_DEVICE_NAME = "#configurationName-form-editor";
  public static final String GENERAL_FORM_INFO = "#notes-form-editor";
  public static final String GENERAL_FORM_FORCE_USER_LOGIN_OSD = "#isLogin-form-editor";
  public static final String GENERAL_FORM_ALLOW_USER_OSD = "#isUserLock-form-editor";
  public static final String GENERAL_FORM_ALLOW_CON_OSD = "#isConLock-form-editor";
  public static final String GENERAL_FORM_NEW_USER_FOR_CPU = "#isUserAccess-form-editor";
  public static final String GENERAL_FORM_NEW_CON_FOR_CPU = "#isConAccess-form-editor";
  public static final String GENERAL_FROM_AUTO_DISCONNECT_WHEN_OSD = "#isCpuDisconnect-form-editor";
  public static final String GENERAL_FORM_ALLOW_VIDEO_SHARE = "#isCpuWatch-form-editor";
  public static final String GENERAL_FORM_FULL_WITH_VIDEO_SHARE = "#isCpuConnect-form-editor";
  public static final String GENERAL_FORM_FULL_WITH_VIDEO_DISCONNECT =
      "#isConDisconnect-form-editor";
  public static final String GENERAL_FORM_FORCE_PUSH_GET = "#isForcePushGet-form-editor";
  public static final String GENERAL_FORM_ALLOW_UART = "#isUart-form-editor";
  public static final String GENERAL_FORM_ALLOW_COM_ECHO = "#isComEcho-form-editor";
  public static final String GENERAL_FORM_ALLOW_LAN_ECHO = "#isLanEcho-form-editor";
  public static final String GENERAL_FORM_DEBUG_LEVEL = "#isDebug-form-editor";
  public static final String GENERAL_FORM_INFO_LEVEL = "#isInfo-form-editor";
  public static final String GENERAL_FORM_NOTICE_LEVEL = "#isNotice-form-editor";
  public static final String GENERAL_FORM_WARNING_LEVEL = "#isWarning-form-editor";
  public static final String GENERAL_FORM_ERROR_LEVEL = "#isError-form-editor";
  public static final String GENERAL_FORM_TIMEOUT_DISPLAY = "#timeoutDisplay-form-editor";
  public static final String GENERAL_FORM_TIMEOUT_LOGOUT = "#timeoutLogout-form-editor";
  public static final String GENERAL_FORM_TIMEOUT_DISCONNECT = "#timeoutDisconnect-form-editor";
  public static final String GENERAL_FORM_TIMEOUT_SHARE_ENABLE =
      "#isKeyboardMouseConnect-form-editor";
  public static final String GENERAL_FORM_TIMEOUT_SHARE = "#timeoutShare-form-editor";
  public static final String GENERAL_FORM_AUTO_REJECT_TIME = "#autoRejectTime-form-editor";

  public static final String GENERAL_FORM_COMMIT_BTN = "#generalCommitButton";
  public static final String GENERAL_FORM_CANCEL_BTN = "#generalReloadButton";

  public static final String NETWORK_FORM_TAB = "网络配置";
  public static final String NETWORK_FORM_IP = "#ipAddress-form-editor";
  public static final String NETWORK_FORM_MASK = "#netMask-form-editor";
  public static final String NETWORK_FORM_GATEWAY = "#gateway-form-editor";
  public static final String NETWORK_FORM_DHCP = "#isDhcp-form-editor";
  public static final String NETWORK_FORM_MAC = "#macAddress-form-editor";
  public static final String NETWORK_FORM_COMMIT_BTN = "#networkCommitButton";
  public static final String NETWORK_FORM_CANCEL_BTN = "#networkReloadButton";

  // permission
  public static final String PERMISSION_RIGHTS_SOURCE_LIST = "#sourceList";
  public static final String PERMISSION_RIGHTS_SEARCH_TEXT = "#search-text";

  // hotkey
  public static final String HOTKEY_FAVOURITE_SOURCE_LIST = "#sourceList";
  public static final String HOTKEY_FAVOURITE_SEARCH_TEXT = "#search-text";
  public static final String HOTKEY_MACRO_USER_TAB = "用户宏配置";
  public static final String HOTKEY_MACRO_RX_TAB = "管控端宏配置";
  public static final String HOTKEY_MACRO_SOURCE_LIST = "#sourceList";
  public static final String HOTKEY_MACRO_KEY_LIST = "#keyListView";
  public static final String HOTKEY_MACRO_TABLE = "#macroTable";
  public static final String HOTKEY_MACRO_TABLE_CMD_COLUMN = "cmdColumn";
  public static final String HOTKEY_MACRO_TABLE_PARAM1_COLUMN = "param1Column";
  public static final String HOTKEY_MACRO_TABLE_PARAM2_COLUMN = "param2Column";
  public static final String HOTKEY_MACRO_SEARCH_TEXT = "#search-text";
  public static final String BUTTON_COPY_MACRO_TO_RX = "复制宏配置至其他管控端";
  public static final String BUTTON_COPY_MACRO_LIST = "复制宏列表";
  public static final String BUTTON_PASTE_MACRO_LIST = "粘贴宏列表";
  public static final String WINDOW_COPY_MACRO = "复制宏配置";
  public static final String HOTKEY_MARCO_CMD_FULL = "完全访问连接";
  public static final String HOTKEY_MACRO_PARAM_CURR_RX = "当前管控端";

  // crossscreen
  public static final String CROSS_SCREEN_NAME_INPUT = "#cross-screen-name";
  public static final String CROSS_SCREEN_ROW_INPUT = "#cross-screen-row";
  public static final String CROSS_SCREEN_COLUMN_INPUT = "#cross-screen-column";
  public static final String DIALOG_SWITCH = "切换";
  public static final String CROSS_SCREEN_SCREEN_LIST = "#screen-list";
  public static final String CROSS_SCREEN_SCREEN_TEXT = "#screen-text";

  // user
  public static final String TAB_USER = "用户";
  public static final String USER_USER_NAME_FIELD = "#usernameField";
  public static final String USER_PWD_FIELD = "#passwordField";
  public static final String USER_RE_PWD_FIELD = "#rePasswordField";
  public static final String USER_RIGHTS_FIELD = "#rightsField";
  public static final String USER_MOUSE_SPEED = "#mouseSpeedSpinner";
  public static final String USER_NEW_BTN = "#newAction";
  public static final String USER_DELETE_BTN = "#deleteAction";
  public static final String USER_EDIT_COL = "editCol";
  public static final String WINDOW_NEW_USER = "新建用户";
  public static final String WINDOW_DELETE_USER = "删除用户";
  public static final String WINDOW_EDIT_USER = "编辑用户";
  public static final String USER_TABLE_VIEW = "#tableView";

  // matrixgrid
  public static final String TAB_MATRIX_GRID = "级联配置";

  public static final String BUTTON_DELETE = "删除";
  public static final String MATRIX_GRID_GRID_NAME = "#gridName";
  public static final String MATRIX_GRID_IP_COL = "ipCol";
  public static final String MATRIX_GRID_USER_COL = "userCol";
  public static final String MATRIX_GRID_PWD_COL = "pwdCol";
  public static final String MATRIX_GRID_VALIDATE_COL = "validateCol";
  public static final String MATRIX_GRID_DELETE_COL = "deleteCol";
  public static final String MATRIX_GRID_NAME_COL = "nameCol";
  public static final String MATRIX_GRID_AUTOID_COL = "autoId";

  // HOST UPDATE
  public static final String HOST_LOAD_BUTTON = "#loadButton";
  public static final String HOST_SELECT_ALL_BUTTON = "#selectAllButton";
  public static final String HOST_TABLE_VIEW = "#tableView";

  // EXTENDER UPDATE
  public static final String EXTENDER_LOAD_BUTTON = "#loadButton";
  public static final String EXTENDER_SELECT_ALL_BUTTON = "#selectAllButton";
  public static final String EXTENDER_TABLE_VIEW = "#tableView";
}
