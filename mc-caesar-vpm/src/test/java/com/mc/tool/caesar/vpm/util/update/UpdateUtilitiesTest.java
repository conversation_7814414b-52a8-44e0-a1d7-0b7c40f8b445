package com.mc.tool.caesar.vpm.util.update;

import com.mc.tool.caesar.api.CaesarConstants.Module.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.xml.bind.DatatypeConverter;
import org.junit.Assert;
import org.junit.Test;

/**
 * .
 */
public class UpdateUtilitiesTest {

  @Test
  public void test_36_single_update_one() {
    List<Integer> ioIndexes = Arrays.asList(0);
    byte[] bytes = UpdateUtilities.calculateUpdateParams(0, Type.SINGLE_CPU.getValue(), ioIndexes);
    Assert.assertEquals("0100000000000000", DatatypeConverter.printHexBinary(bytes));

    ioIndexes = Arrays.asList(2);
    bytes = UpdateUtilities.calculateUpdateParams(0, Type.SINGLE_CPU.getValue(), ioIndexes);
    Assert.assertEquals("0400000000000000", DatatypeConverter.printHexBinary(bytes));
  }

  @Test
  public void test_36_single_update_all() {
    List<Integer> ioIndexes = Arrays.asList(0, 1, 2);
    byte[] bytes = UpdateUtilities.calculateUpdateParams(0, Type.SINGLE_CPU.getValue(), ioIndexes);
    Assert.assertEquals("0700000000000000", DatatypeConverter.printHexBinary(bytes));
  }

  @Test
  public void test_96_single_update_one() {
    List<Integer> ioIndexes = Arrays.asList(0);
    byte[] bytes = UpdateUtilities.calculateUpdateParams(0, Type.SINGLE_CPU.getValue(), ioIndexes);
    Assert.assertEquals("0100000000000000", DatatypeConverter.printHexBinary(bytes));

    ioIndexes = Arrays.asList(5);
    bytes = UpdateUtilities.calculateUpdateParams(0, Type.SINGLE_CPU.getValue(), ioIndexes);
    Assert.assertEquals("2000000000000000", DatatypeConverter.printHexBinary(bytes));
  }

  @Test
  public void test_96_single_update_all() {
    List<Integer> ioIndexes = Arrays.asList(0, 1, 2, 3, 4, 5);
    byte[] bytes = UpdateUtilities.calculateUpdateParams(0, Type.SINGLE_CPU.getValue(), ioIndexes);
    Assert.assertEquals("3F00000000000000", DatatypeConverter.printHexBinary(bytes));
  }

  @Test
  public void test_384_single_update_one() {
    List<Integer> ioIndexes = Arrays.asList(0);
    byte[] bytes = UpdateUtilities.calculateUpdateParams(0, Type.PLUGIN_384.getValue(), ioIndexes);
    Assert.assertEquals("0100000000000000", DatatypeConverter.printHexBinary(bytes));

    ioIndexes = Arrays.asList(15);
    bytes = UpdateUtilities.calculateUpdateParams(0, Type.PLUGIN_384.getValue(), ioIndexes);
    Assert.assertEquals("0080000000000000", DatatypeConverter.printHexBinary(bytes));
  }

  @Test
  public void test_384_single_update_all() {
    List<Integer> ioIndexes = new ArrayList<>();
    for (int i = 0; i < 16; i++) {
      ioIndexes.add(i);
    }
    byte[] bytes = UpdateUtilities.calculateUpdateParams(0, Type.PLUGIN_816.getValue(), ioIndexes);
    Assert.assertEquals("FFFF000000000000", DatatypeConverter.printHexBinary(bytes));
  }

  @Test
  public void test_816_single_update_one() {
    List<Integer> ioIndexes = Arrays.asList(0);
    byte[] bytes = UpdateUtilities.calculateUpdateParams(0, Type.PLUGIN_816.getValue(), ioIndexes);
    Assert.assertEquals("0100000000000000", DatatypeConverter.printHexBinary(bytes));

    ioIndexes = Arrays.asList(33);
    bytes = UpdateUtilities.calculateUpdateParams(0, Type.PLUGIN_816.getValue(), ioIndexes);
    Assert.assertEquals("0000000000000100", DatatypeConverter.printHexBinary(bytes));
  }

  @Test
  public void test_816_single_update_all() {
    List<Integer> ioIndexes = new ArrayList<>();
    for (int i = 0; i < 34; i++) {
      ioIndexes.add(i);
    }
    byte[] bytes = UpdateUtilities.calculateUpdateParams(0, Type.PLUGIN_816.getValue(), ioIndexes);
    Assert.assertEquals("FFFF0100FFFF0100", DatatypeConverter.printHexBinary(bytes));
  }

  @Test
  public void test_36_grid_update_all() {
    String all = "0700000000000000";
    String prefix = "";
    for (int i = 1; i < 16; i++) {
      List<Integer> ioIndexes = Arrays.asList(0, 1, 2);
      byte[] bytes =
          UpdateUtilities.calculateUpdateParams(i, Type.SINGLE_CPU.getValue(), ioIndexes);
      prefix += "0000000000000000";
      Assert.assertEquals(prefix + all, DatatypeConverter.printHexBinary(bytes));
    }
  }

  @Test
  public void test_96_grid_update_all() {
    String all = "3F00000000000000";
    String prefix = "";
    for (int i = 1; i < 16; i++) {
      List<Integer> ioIndexes = Arrays.asList(0, 1, 2, 3, 4, 5);
      byte[] bytes =
          UpdateUtilities.calculateUpdateParams(i, Type.SINGLE_CPU.getValue(), ioIndexes);
      prefix += "0000000000000000";
      Assert.assertEquals(prefix + all, DatatypeConverter.printHexBinary(bytes));
    }
  }

  @Test
  public void test_384_grid_update_all() {
    String all = "FFFF000000000000";
    String prefix = "";
    for (int i = 1; i < 16; i++) {
      List<Integer> ioIndexes = new ArrayList<>();
      for (int j = 0; j < 16; j++) {
        ioIndexes.add(j);
      }
      byte[] bytes =
          UpdateUtilities.calculateUpdateParams(i, Type.PLUGIN_384.getValue(), ioIndexes);
      prefix += "0000000000000000";
      Assert.assertEquals(prefix + all, DatatypeConverter.printHexBinary(bytes));
    }
  }

  @Test
  public void test_816_grid_update_all() {
    String all = "FFFF0100FFFF0100";
    String prefix = "";
    for (int i = 1; i < 16; i++) {
      List<Integer> ioIndexes = new ArrayList<>();
      for (int j = 0; j < 34; j++) {
        ioIndexes.add(j);
      }
      byte[] bytes =
          UpdateUtilities.calculateUpdateParams(i, Type.PLUGIN_816.getValue(), ioIndexes);
      prefix += "0000000000000000";
      Assert.assertEquals(prefix + all, DatatypeConverter.printHexBinary(bytes));
    }
  }
}
