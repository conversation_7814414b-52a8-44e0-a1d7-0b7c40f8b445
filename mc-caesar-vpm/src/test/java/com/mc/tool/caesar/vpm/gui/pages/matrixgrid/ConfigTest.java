package com.mc.tool.caesar.vpm.gui.pages.matrixgrid;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.caesar.vpm.pages.hostconfiguration.CaesarHostConfigurationPage;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.WizardAutoId;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.WizardGridSystem;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.WizardHostConfig;
import javafx.scene.Node;
import javafx.scene.control.Button;
import javafx.scene.input.KeyCode;
import org.junit.Assert;
import org.junit.Test;

/**
 * .
 */
public class ConfigTest extends AppGuiTestBase {

  @Override
  public void beforeAll() {
    login();
  }

  @Test
  public void testNormlConfig() {
    clickOn("#" + CaesarHostConfigurationPage.NAME);
    clickOn(GuiTestConstants.TAB_MATRIX_GRID);
    clickOn("#configBtn");
    // 前期准备
    Button nextButton = lookup(GuiTestConstants.WIZARD_NEXT).queryButton();
    Assert.assertTrue(nextButton.isDisable());
    clickOn("#check1");
    Assert.assertTrue(nextButton.isDisable());
    clickOn("#check2");
    Assert.assertTrue(nextButton.isDisable());
    clickOn("#check3");
    Assert.assertTrue(!nextButton.isDisable());
    clickOn(nextButton);
    // 添加主机
    nextButton = lookup(GuiTestConstants.WIZARD_NEXT).queryButton();
    // 检查最多主机数
    Button addMatrixBtn = lookup("#addMatrixBtn").queryButton();
    for (int i = 1; i < CaesarConstants.MATRIX_GRID_COUNT; i++) {
      Assert.assertTrue(!addMatrixBtn.isDisable());
      clickOn(addMatrixBtn);
    }
    Assert.assertTrue(addMatrixBtn.isDisable());
    // 删除主机，剩下一个
    for (int i = 1; i < CaesarConstants.MATRIX_GRID_COUNT - 1; i++) {
      clickOn(lookupTableCell(1, GuiTestConstants.MATRIX_GRID_DELETE_COL, WizardHostConfig.class));
    }

    Assert.assertTrue(nextButton.isDisable());
    textInTableCell(
        lookupTableCell(1, GuiTestConstants.MATRIX_GRID_IP_COL, WizardHostConfig.class),
        "************");
    textInTableCell(
        lookupTableCell(1, GuiTestConstants.MATRIX_GRID_USER_COL, WizardHostConfig.class), "admin");
    textInTableCell(
        lookupTableCell(1, GuiTestConstants.MATRIX_GRID_PWD_COL, WizardHostConfig.class), "admin");
    clickOn(lookupTableCell(1, GuiTestConstants.MATRIX_GRID_VALIDATE_COL, WizardHostConfig.class));
    Assert.assertFalse(nextButton.isDisable());
    clickOn(nextButton);

    // 级联名称
    nextButton = lookup(GuiTestConstants.WIZARD_NEXT).queryButton();
    clickOn(GuiTestConstants.MATRIX_GRID_GRID_NAME);
    eraseText(50);
    Assert.assertTrue(nextButton.isDisable());
    StringBuilder builder = new StringBuilder();
    for (int i = 0; i < CaesarConstants.NAME_LEN + 1; i++) {
      builder.append('a');
    }
    write(builder.toString());
    Assert.assertTrue(nextButton.isDisable());
    type(KeyCode.BACK_SPACE);
    Assert.assertFalse(nextButton.isDisable());
    clickOn(nextButton);

    // 级联系统
    nextButton = lookup(GuiTestConstants.WIZARD_NEXT).queryButton();
    Assert.assertTrue(nextButton.isDisable());
    textInTableCell(
        lookupTableCell(0, GuiTestConstants.MATRIX_GRID_NAME_COL, WizardGridSystem.class),
        "matrix1");
    clickOn(lookupTableCell(0, GuiTestConstants.MATRIX_GRID_VALIDATE_COL, WizardGridSystem.class));
    textInTableCell(
        lookupTableCell(1, GuiTestConstants.MATRIX_GRID_NAME_COL, WizardGridSystem.class),
        "matrix2");
    clickOn(lookupTableCell(1, GuiTestConstants.MATRIX_GRID_VALIDATE_COL, WizardGridSystem.class));
    Assert.assertFalse(nextButton.isDisable());
    // 同名
    textInTableCell(
        lookupTableCell(1, GuiTestConstants.MATRIX_GRID_NAME_COL, WizardGridSystem.class),
        "matrix1");
    clickOn(lookupTableCell(1, GuiTestConstants.MATRIX_GRID_VALIDATE_COL, WizardGridSystem.class));
    Assert.assertTrue(nextButton.isDisable());
    // 名字过长
    textInTableCell(
        lookupTableCell(1, GuiTestConstants.MATRIX_GRID_NAME_COL, WizardGridSystem.class),
        builder.toString());
    clickOn(lookupTableCell(1, GuiTestConstants.MATRIX_GRID_VALIDATE_COL, WizardGridSystem.class));
    Assert.assertTrue(nextButton.isDisable());

    textInTableCell(
        lookupTableCell(1, GuiTestConstants.MATRIX_GRID_NAME_COL, WizardGridSystem.class),
        "matrix2");
    clickOn(lookupTableCell(1, GuiTestConstants.MATRIX_GRID_VALIDATE_COL, WizardGridSystem.class));
    clickOn(nextButton);

    // 自动id
    Node autoIdBox =
        lookupTableCell(0, GuiTestConstants.MATRIX_GRID_AUTOID_COL, WizardAutoId.class);
    clickOn(autoIdBox.lookup(".check-box"));
    autoIdBox = lookupTableCell(1, GuiTestConstants.MATRIX_GRID_AUTOID_COL, WizardAutoId.class);
    clickOn(autoIdBox.lookup(".check-box"));
    nextButton = lookup(GuiTestConstants.WIZARD_NEXT).queryButton();
    clickOn(nextButton);

    // 激活

    clickOn(GuiTestConstants.BUTTON_FINISH);
  }

  @Test
  public void testDeleteConfig() {
    clickOn("#" + CaesarHostConfigurationPage.NAME);
    clickOn(GuiTestConstants.TAB_MATRIX_GRID);
    clickOn("#configBtn");
    // 前期准备
    clickOn("#check1");
    clickOn("#check2");
    clickOn("#check3");
    Button nextButton = lookup(GuiTestConstants.WIZARD_NEXT).queryButton();
    clickOn(nextButton);
    // 删除所有主机
    clickOn(lookupTableCell(0, GuiTestConstants.MATRIX_GRID_DELETE_COL, WizardHostConfig.class));
    nextButton = lookup(GuiTestConstants.WIZARD_NEXT).queryButton();
    clickOn(nextButton);
    // 级联名称
    Node node = lookup(GuiTestConstants.MATRIX_GRID_GRID_NAME).query();
    Assert.assertTrue(node.isDisable());
    nextButton = lookup(GuiTestConstants.WIZARD_NEXT).queryButton();
    clickOn(nextButton);
    nextButton = lookup(GuiTestConstants.WIZARD_NEXT).queryButton();
    clickOn(nextButton);
    // id
    nextButton = lookup(GuiTestConstants.WIZARD_NEXT).queryButton();
    clickOn(nextButton);
    // 激活
    clickOn(GuiTestConstants.BUTTON_FINISH);
  }
}
