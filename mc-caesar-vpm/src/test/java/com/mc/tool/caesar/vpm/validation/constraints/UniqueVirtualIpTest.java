package com.mc.tool.caesar.vpm.validation.constraints;

import com.mc.tool.caesar.api.datamodel.NetworkData;
import com.mc.tool.caesar.api.datamodel.SystemConfigData;
import com.mc.tool.caesar.api.utils.IpUtil;
import org.junit.Before;
import org.junit.Test;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Set;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试UniqueVirtualIp验证注解.
 */
public class UniqueVirtualIpTest {

  private Validator validator;

  @Before
  public void setUp() {
    ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
    validator = factory.getValidator();
  }

  @Test
  public void testValidVirtualIp() {
    // 创建测试Bean
    TestBean bean = new TestBean();
    bean.systemConfigData = createMockSystemConfigData(
        "***********", // network1 IP
        "***********"  // network3 IP
    );
    bean.virtualIp = "***********"; // 不同的虚拟IP

    // 验证
    Set<ConstraintViolation<TestBean>> violations = validator.validate(bean);
    assertTrue("虚拟IP与网络配置IP不同时应该验证通过", violations.isEmpty());
  }

  @Test
  public void testInvalidVirtualIpSameAsNetwork1() {
    // 创建测试Bean
    TestBean bean = new TestBean();
    bean.systemConfigData = createMockSystemConfigData(
        "***********", // network1 IP
        "***********"  // network3 IP
    );
    bean.virtualIp = "***********"; // 与network1相同的虚拟IP

    // 验证
    Set<ConstraintViolation<TestBean>> violations = validator.validate(bean);
    assertFalse("虚拟IP与网络配置1 IP相同时应该验证失败", violations.isEmpty());
    assertEquals(1, violations.size());
  }

  @Test
  public void testInvalidVirtualIpSameAsNetwork3() {
    // 创建测试Bean
    TestBean bean = new TestBean();
    bean.systemConfigData = createMockSystemConfigData(
        "***********", // network1 IP
        "***********"  // network3 IP
    );
    bean.virtualIp = "***********"; // 与network3相同的虚拟IP

    // 验证
    Set<ConstraintViolation<TestBean>> violations = validator.validate(bean);
    assertFalse("虚拟IP与网络配置3 IP相同时应该验证失败", violations.isEmpty());
    assertEquals(1, violations.size());
  }

  @Test
  public void testNullVirtualIp() {
    // 创建测试Bean
    TestBean bean = new TestBean();
    bean.systemConfigData = createMockSystemConfigData(
        "***********", // network1 IP
        "***********"  // network3 IP
    );
    bean.virtualIp = null; // null虚拟IP

    // 验证
    Set<ConstraintViolation<TestBean>> violations = validator.validate(bean);
    assertTrue("null虚拟IP应该验证通过（由其他验证器处理）", violations.isEmpty());
  }

  @Test
  public void testEmptyVirtualIp() {
    // 创建测试Bean
    TestBean bean = new TestBean();
    bean.systemConfigData = createMockSystemConfigData(
        "***********", // network1 IP
        "***********"  // network3 IP
    );
    bean.virtualIp = ""; // 空虚拟IP

    // 验证
    Set<ConstraintViolation<TestBean>> violations = validator.validate(bean);
    assertTrue("空虚拟IP应该验证通过（由其他验证器处理）", violations.isEmpty());
  }

  /**
   * 创建模拟的SystemConfigData对象.
   */
  private SystemConfigData createMockSystemConfigData(String network1Ip, String network3Ip) {
    SystemConfigData systemConfigData = mock(SystemConfigData.class);
    
    // 模拟网络配置1
    NetworkData networkData1 = mock(NetworkData.class);
    when(networkData1.getAddress()).thenReturn(IpUtil.getAddressByte(network1Ip));
    when(systemConfigData.getNetworkDataCurrent1()).thenReturn(networkData1);
    
    // 模拟网络配置3
    NetworkData networkData3 = mock(NetworkData.class);
    when(networkData3.getAddress()).thenReturn(IpUtil.getAddressByte(network3Ip));
    when(systemConfigData.getNetworkDataCurrent3()).thenReturn(networkData3);
    
    return systemConfigData;
  }

  /**
   * 测试用的Bean类.
   */
  @UniqueVirtualIp
  public static class TestBean {
    public SystemConfigData systemConfigData;
    public String virtualIp;

    public String getVirtualIp() {
      return virtualIp;
    }

    public void setVirtualIp(String virtualIp) {
      this.virtualIp = virtualIp;
    }
  }
}
