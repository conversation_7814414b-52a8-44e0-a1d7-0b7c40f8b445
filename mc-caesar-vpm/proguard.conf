
-dontoptimize
-dontusemixedcaseclassnames

# -libraryjars "<java.home>/lib/rt.jar"
# -libraryjars "<java.home>/lib/javaws.jar"
# -libraryjars "<java.home>/lib/ext/jfxrt.jar"

#common
-keepnames class com.mc.common.control.gridview.GridViewEx{}
-keep class com.mc.common.validation.constraints.** { *;}

#graph
-keepnames class com.mc.graph.interfaces.ConnectorIdentifier{*;}
-keepnames class com.mc.graph.connector.IntegerConnectorIdentifier{*;}
-keepnames class com.mc.graph.connector.StringConnectorIdentifier{*;}
-keepnames class com.mc.graph.connector.AnomymousConnectorObject{*;}

#framework
-keep class com.mc.tool.framework.controller.ObjectListController { *;}
-keep class com.mc.tool.framework.view.ObjectListView { *;}
-keep class com.mc.tool.framework.controller.MainMasterDetailPane{ *;}
-keep class com.mc.tool.framework.DefaultLog4jConfigurationFactory{ *;}
-keep class com.mc.tool.framework.VpmConfig{ *;}
-keep class com.mc.tool.framework.SyslogConfig{ *;}
-keep class com.mc.tool.framework.view.config.SyslogBean{ *;}
-keep class com.mc.tool.framework.controller.SafeComboBoxSkin{ *;}

-keepnames class com.mc.tool.framework.systemedit.datamodel.VisualEditNode{ *;}
-keepnames class com.mc.tool.framework.systemedit.datamodel.VisualEditGroup{ *;}
-keepnames class com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal{ *;}
-keepnames class com.mc.tool.framework.systemedit.datamodel.VisualEditMatrix{ *;}

-keepnames class com.mc.tool.framework.systemedit.datamodel.VisualEditModel{ *;}
-keepnames class com.mc.tool.framework.systemedit.datamodel.InnerVisualEditGroup{ *;}
-keepnames class com.mc.tool.framework.systemedit.datamodel.DefaultVideoWallFunc{ *;}
-keepnames class com.mc.tool.framework.systemedit.datamodel.DefaultSeatFunc{ *;}
-keepnames class com.mc.tool.framework.systemedit.datamodel.DefaultCrossScreenFunc{ *;}
-keepnames class com.mc.tool.framework.systemedit.datamodel.preview.VideoPreviewFunc{ *;}
-keepnames class com.mc.tool.framework.systemedit.datamodel.preview.VideoThumbnailFunc{ *;}
-keepnames class com.mc.tool.framework.OemInfo{ *;}

-keep class com.mc.tool.framework.operation.view.VideoSourceTree{ *;}
-keep class com.mc.tool.framework.beans.ConfigBean{ *;}
-keep class com.mc.tool.framework.beans.ConfigBean$**{ *;}

#caesar
-keep class com.mc.tool.caesar.vpm.CaesarApp { *; }

-keepnames class com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal{ *;}
-keepnames class com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal{ *;}
-keepnames class com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbTxTerminal{ *;}
-keepnames class com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbRxTerminal{ *;}
-keepnames class com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarGridLineTerminal{ *;}
-keepnames class com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix{ *;}
-keepnames class com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarVideoWallFunc{ *;}
-keepnames class com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup{ *;}
-keepnames class com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.CpuGroup{ *;}
-keepnames class com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.AudioGroup{ *;}
-keepnames class com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCrossScreenFunc{ *;}
-keepnames class com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMultiScreenFunc{ *;}

-keep class com.mc.tool.caesar.vpm.util.control.SearchField{ *;}
-keep class com.mc.tool.caesar.vpm.util.searchdevices.GridInfoData{ *;}
-keep class com.mc.tool.caesar.vpm.pages.update.UpdateData{ *;}
-keep class com.mc.tool.caesar.api.datamodel.vp.VpConsoleData{ *;}
-keep class com.mc.tool.caesar.vpm.pages.operation.view.CaesarOperationFuncPane { *;}
-keep class com.sun.javafx.scene.control.skin.ColorPaletteEx{ *;}

-keep class com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridWizardIndicator{ *;}

-keep class com.mc.tool.caesar.vpm.pages.systemedit.view.TerminalListView{ *;}
-keep class com.mc.tool.caesar.vpm.pages.systemedit.view.RemoteSwitchingView{ *;}
-keep class com.mc.tool.caesar.vpm.pages.systemedit.view.BranchSettingView{ *;}
-keep class com.mc.tool.caesar.vpm.pages.systemedit.view.OfflineManagerView { *; }
-keep class com.mc.tool.caesar.vpm.pages.operation.videowall.view.CaeasrVideoWallVideoSourceTree { *; }

-keep class com.mc.tool.caesar.api.datamodel.VersionSet {*;}
-keep class com.mc.tool.caesar.api.datamodel.VersionDef {*;}

-keep class com.mc.tool.caesar.api.datamodel.OpticalModuleInfo{ *;}

-keep class com.mc.tool.caesar.vpm.pages.extenderupdate.txgroup.InvalidTxRxGroupsManagerView{ *;}

-keep class com.mc.tool.caesar.vpm.pages.splicingscreen.**{*;}
-keep class com.mc.tool.caesar.vpm.kaito.**{*;}
-keep class com.mc.tool.caesar.vpm.view.IdCell{*;}

#all
-keepclassmembers enum * { *; }
-keepclassmembers class **Bean{*;}
-keep class **FieldFactory{*;}

# Ignoring all of the external "org" libraries 
# (for example org.apache & org.jackson)
-keep class afu.** {*;}
-keep class cl.** {*;}
-keep class Class50.** {*;}
-keep class com.alibaba.** {*;}
-keep class com.dooapp.** {*;}
-keep class com.fasterxml.** {*;}
-keep class com.google.** {*;}
-keep class com.graphbuilder.** {*;}
-keep class com.itextpdf.** {*;}
-keep class com.microsoft.** {*;}
-keep class com.sun.** {*;}
-keep class com.zwitserloot.** {*;}
-keep class edu.** {*;}
-keep class fonts.** {*;}
-keep class impl.** {*;}
-keep class javafx.** {*;}
-keep class javax.** {*;}
-keep class lombok.** {*;}
-keep class net.** {*;}
-keep class org.** {*;}
-keep class repackage.** {*;}
-keep class schemaorg_apache_xmlbeans.** {*;}
-keep class tornadofx.** {*;}
-keep class com.blade.** {*;}
-keep class com.hellokaton.** {*;}
-keep class io.netty.** {*;}
-keep class cn.hutool.** {*;}
-keep class com.jfoenix.** {*;}
-keep class com.zaxxer.** {*;}
-keep class feign.** {*;}

-dontwarn org.**
-dontwarn com.google.**
-dontwarn lombok.**
-dontwarn javax.**
-dontwarn afu.**
-dontwarn com.sun.javafx.scene.control.skin.**
-dontwarn com.microsoft.**
-dontwarn net.sf.**
-dontwarn com.alibaba.**
-dontwarn schemaorg_apache_xmlbeans.**
-dontwarn com.blade.**
-dontwarn com.hellokaton.**
-dontwarn io.netty.**
-dontwarn cn.hutool.**
-dontwarn com.jfoenix.**
-dontwarn com.zaxxer.**

# Save meta-data for stack traces
-renamesourcefileattribute SourceFile
-keepattributes SourceFile,LineNumberTable

# Keep all annotations and meta-data
-keepattributes *Annotation*,Signature,EnclosingMethod



# Keep names of fields marked with @FXML attribute
-keepclassmembers class * {
    @javafx.fxml.FXML *;
}

# Keep names fo fields needed to be serialized/deserialized
-keepclassmembers class * {
	@com.google.gson.annotations.Expose *;
}

# Keep names to use eventbus
-keepclassmembers class * {
	@com.google.common.eventbus.Subscribe *;
}

-printmapping obfuscated-map.out

##---------------Begin: proguard configuration for Gson  ----------
# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature

# For using GSON @Expose annotation
-keepattributes *Annotation*

# Gson specific classes
-keep class sun.misc.Unsafe { *; }
#-keep class com.google.gson.stream.** { *; }

# Application classes that will be serialized/deserialized over Gson
#-keep class com.google.gson.examples.android.model.** { *; }

# Prevent proguard from stripping interface information from TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

##---------------End: proguard configuration for Gson  ----------

# JavaCV
-keep @org.bytedeco.javacpp.annotation interface * {
    *;
}

-keep @org.bytedeco.javacpp.annotation.Platform public class *

-keepclasseswithmembernames class * {
    @org.bytedeco.* <fields>;
}

-keepclasseswithmembernames class * {
    @org.bytedeco.* <methods>;
}

-keepattributes EnclosingMethod
-keep @interface org.bytedeco.javacpp.annotation.*,javax.inject.*

-keepattributes *Annotation*, Exceptions, Signature, Deprecated, SourceFile, SourceDir, LineNumberTable, LocalVariableTable, LocalVariableTypeTable, Synthetic, EnclosingMethod, RuntimeVisibleAnnotations, RuntimeInvisibleAnnotations, RuntimeVisibleParameterAnnotations, RuntimeInvisibleParameterAnnotations, AnnotationDefault, InnerClasses
-keep class org.bytedeco.javacpp.** {*;}
-dontwarn java.awt.**
-dontwarn org.bytedeco.javacv.**
-dontwarn org.bytedeco.javacpp.**

# end javacv