<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
	<head>
		<title>Launch4j - Cross-platform Java executable wrapper</title>
		<meta name="description" content="Cross-platform Java executable wrapper for creating lightweight Windows native EXEs. Provides advanced JRE search, application startup configuration and better user experience.">
		<meta name="keywords" content="java executable wrapper, java application wrapper, exe wrapper, jar wrapper, wrap, wraps, wrapping, free software, launch, launcher, linux, mac, windows, open source, ant, native splash screen, deploy, build tool">
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<meta name="author" content="Grzegorz Kowal" >
		<link rel="stylesheet" type="text/css" href="style.css">
	</head>
	<body>
		<div id="container">
			<div id="top">
				<img style="width: 249px; height: 58px;" src="launch4j.gif" alt="launch4j"> <span class="version">3.12</span>
			</div>
			<div id="leftnav">
				<ul>
					<li><a href="index.html">Home</a></li>
					<li><a href="docs.html">Docs</a></li>
					<li><a href="changelog.html">Changelog</a></li>
					<li><a href="http://sourceforge.net/project/screenshots.php?group_id=95944">Screenshots</a></li>
					<li><a href="http://sourceforge.net/projects/launch4j/files/launch4j-3/3.12">Download</a></li>
					<li><a href="http://sourceforge.net/projects/launch4j/support">Support</a></li>
					<li><a href="http://sourceforge.net/projects/launch4j">Project summary</a></li>
					<li><a href="http://sourceforge.net/tracker/?atid=613100&amp;group_id=95944">Bug tracker</a></li>
					<li><a href="links.html">Links</a></li>
				</ul>
				<br />
				<a class="button" href="https://sourceforge.net/projects/launch4j/files/launch4j-3/3.12" rel="nofollow"><img alt="Downloads" src="https://img.shields.io/sourceforge/dm/launch4j.svg"></a>
				<a class="button" href="https://sourceforge.net/p/launch4j/" rel="nofollow"><img alt="Download Launch4j" src="https://sourceforge.net/sflogo.php?type=10&group_id=95944"></a>
			</div>
			<div id="content">
<h2>Links</h2>
<p>
	<a href="https://github.com/lukaszlenart/launch4j-maven-plugin">Maven plugin by Lukasz Lenart<a><br>
	<a href="http://search.maven.org/#search%7Cgav%7C1%7Cg%3A%22net.sf.launch4j%22%20AND%20a%3A%22launch4j%22">Maven Central Repository</a><br>
    <a href="http://prdownloads.sourceforge.net/dev-cpp/devcpp-4.9.9.2_setup.exe">Bloodshed Dev-C++</a><br>
	<a href="http://www.mingw.org/">MinGW</a><br>
	<a href="http://jakarta.apache.org/commons/beanutils/">Commons BeanUtils</a><br>
	<a href="http://jakarta.apache.org/commons/logging/">Commons Logging</a><br>
	<a href="http://xstream.codehaus.org/">XStream</a><br>
	<a href="http://www.jgoodies.com/freeware/libraries/">JGoodies</a><br>
	<a href="http://www.eclipse.org/">Eclipse</a><br>
</p>
<h3>Windows native installers</h3>
<p>
	<a href="http://nsis.sourceforge.net/">NSIS</a> (cross-platform, targets Windows)<br>
	<a href="http://www.jrsoftware.org/isinfo.php">Inno Setup</a><br>
</p>
			</div>
			<div class="footer">
			    All trademarks mentioned are properties of their respective owners.<br />
            	Copyright &copy; 2005-2017 Grzegorz Kowal
            	<p style="margin-top: 0.5em">
            	    <a href="https://sourceforge.net/p/launch4j/" rel="nofollow"><img alt="Download Launch4j Executable Wrapper" src="https://sourceforge.net/sflogo.php?type=16&group_id=95944"></a>
            	</p>
			</div>
		</div>
	</body>
</html>
