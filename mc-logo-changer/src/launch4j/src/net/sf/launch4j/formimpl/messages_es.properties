#
#	Launch4j (http://launch4j.sourceforge.net/)
#	Cross-platform Java application wrapper for creating Windows native executables.
#
#	Copyright (c) 2004, 2007 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
#	All rights reserved.
#
#	Redistribution and use in source and binary forms, with or without modification,
#	are permitted provided that the following conditions are met:
#	
#	1. Redistributions of source code must retain the above copyright notice,
#	   this list of conditions and the following disclaimer.
#	
#	2. Redistributions in binary form must reproduce the above copyright notice,
#	   this list of conditions and the following disclaimer in the documentation
#	   and/or other materials provided with the distribution.
#	
#	3. Neither the name of the copyright holder nor the names of its contributors
#	   may be used to endorse or promote products derived from this software without
#	   specific prior written permission.
#	
#	THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#	AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
#	THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#	ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
#	FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
#	(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
#	LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
#	AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
#	OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
#	OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#

tab.basic = Básico
tab.header = Cabecera
tab.jre = JRE
tab.splash = Pantalla de bienvenida
tab.version = Información de la versión

jar = Jar
jarPath = Ruta del jar
jarTip = Jar de la aplicación.
jarPathTip = Ruta del jar relativa al ejecutable. Por ejemplo, si el lanzador ejecutable y el jar de la aplicación, llamados calc.exe y calc.jar respectivamente, están en el mismo directorio, sería\: calc.jar.

MainFrame.config.files = Ficheros de configuración de launch4j (.xml, .cfg)
MainFrame.new.config = Nueva configuración
MainFrame.open.config = Abrir configuración o importar 1.x
MainFrame.save.config = Guardar configuración
MainFrame.build.wrapper = Construir el empaquetador
MainFrame.test.wrapper = Comprobar el empaquetador
MainFrame.about.launch4j = Acerca de launch4j
MainFrame.discard.changes = ¿Descartar cambios?
MainFrame.confirm = Confirmar
MainFrame.untitled = Sin nombre
MainFrame.executing = Ejecutando\: 
MainFrame.jar.integrity.test = Prueba de integridad jar, ejecutando\: 
