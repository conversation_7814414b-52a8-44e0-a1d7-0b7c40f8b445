#
#	Launch4j (http://launch4j.sourceforge.net/)
#	Cross-platform Java application wrapper for creating Windows native executables.
#
#	Copyright (c) 2004, 2015 <PERSON><PERSON><PERSON><PERSON>
#	All rights reserved.
#
#	Redistribution and use in source and binary forms, with or without modification,
#	are permitted provided that the following conditions are met:
#	
#	1. Redistributions of source code must retain the above copyright notice,
#	   this list of conditions and the following disclaimer.
#	
#	2. Redistributions in binary form must reproduce the above copyright notice,
#	   this list of conditions and the following disclaimer in the documentation
#	   and/or other materials provided with the distribution.
#	
#	3. Neither the name of the copyright holder nor the names of its contributors
#	   may be used to endorse or promote products derived from this software without
#	   specific prior written permission.
#	
#	THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#	AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
#	THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#	ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
#	FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
#	(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
#	LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
#	AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
#	OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
#	OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#

log=Log

outfile=Output file:
outfileTip=Output executable file.
stayAlive=Stay alive after launching a GUI application
restartOnCrash=Restart the application after a crash
restartOnCrashToolTip=When the application exits, any exit code other than 0 is considered a crash and the application will be started again
manifest=Wrapper manifest:
manifestTip=Wrapper manifest for User Account Control.
icon=Icon:
iconTip=Application icon.
jar=Jar:
jarTip=Application jar.
dontWrapJar=Dont't wrap the jar, launch only
cmdLine=Command line args:
cmdLineTip=Constant command line arguments passed to the application.
options=Options:
chdir=Change dir:
chdirTip=Change current directory to a location relative to the executable. Empty field has no effect, . - changes directory to the exe location.
priority=Process priority:
normalPriority=Normal
idlePriority=Idle
highPriority=High
downloadAndSupport=Java download and support
errorTitle=Error title:
errorTitleTip=Launch4j signals errors using a message box, you can set it's title to the application's name.
downloadUrl=Java download URL:
supportUrl=Support URL:

new=New
accept=Accept
remove=Remove
customClassPath=Custom classpath
classPath=Classpath:
mainClass=Main class:
editClassPath=Edit item:
importClassPath=Import attributes from a jar's manifest.

headerType=Header type:
gui=GUI
guiTooltip=Stable version of the GUI header which uses the javaw.exe launcher.
console=Console
consoleTooltip=Stable version of the console header which uses the java.exe launcher.
jniGui=JNI GUI (beta)
jniGuiTooltip=BETA version of the GUI header which launches the Java VM through JNI.
jniConsole=JNI Console (beta)
jniConsoleTooltip=BETA version of the console header which launches the Java VM through JNI.
objectFiles=Object files:
libs=w32api:
linkerOptions=Custom header - linker options

enableSingleInstance=Allow only a single instance of the application
mutexName=Mutex name
mutexNameTip=Mutex name that will uniquely identify your application.
windowTitle=Window title
windowTitleTip=Title of the GUI application window to bring up on attempt to start a next instance.

jrePath=Bundled JRE path:
jrePathTip=Bundled JRE path relative to the executable or absolute.
bundledJre64Bit=64-bit
bundledJre64BitTip=The bundled JRE is 64-bit, heap size will not be limited to the 32-bit maximum.
bundledJreAsFallback=Fallback option
bundledJreAsFallbackTip=When the fallback option is checked, the bundled JRE will be used only if the min/max search fails. By default the bundled JRE, if specified, is used as first.
searchOptions=Search options
options=Options
jreMin=Min JRE version:
jreMax=Max JRE version:
dontUsePrivateJres=Don't use private JREs
jvmOptions=JVM options:
jvmOptionsTip=Accepts everything you would normally pass to java/javaw launcher: assertion options, system properties and X options.
initialHeapSize=Initial heap size:
maxHeapSize=Max heap size:
availableMemory=% of available memory
addVariables=Add variables:
addVariablesTip=Add special variable or map environment variables to system properties.
exeDirVarTip=Executable's runtime directory path.
exeFileVarTip=Executable's runtime file path (directory and filename).
varsAndRegistry=Variables / registry:
envVar=Environment var:
property=Property
propertyTip=Map a variable to a system property.
option=Option
optionTip=Pass a JVM option using a variable.

setVariables=Set variables:

enableSplash=Enable splash screen
splashFile=Splash file:
splashFileTip=Splash screen file in BMP format.
waitForWindow=Wait for window
waitForWindowText=Close splash screen when an application window appears
timeout=Timeout [s]:
timeoutTip=Number of seconds after which the splash screen must close. Splash timeout may cause an error depending on splashTimeoutErr property.
timeoutErr=Signal error on timeout
timeoutErrTip=True signals an error on splash timeout, false closes the splash screen quietly.

version=Version
additionalInfo=Additional information
addVersionInfo=Add version information
fileVersion=File version:
fileVersionTip=Version number 'x.x.x.x'
productVersion=Product version:
productVersionTip=Version number 'x.x.x.x'
fileDescription=File description:
fileDescriptionTip=File description presented to the user.
copyright=Copyright:
txtFileVersion=Free form:
txtFileVersionTip=Free form file version, for example '1.20.RC1'.
txtProductVersion=Free form:
txtProductVersionTip=Free form product version, for example '1.20.RC1'.
productName=Product name:
originalFilename=Original filename:
originalFilenameTip=Original name of the file without the path. Allows to determine whether a file has been renamed by a user.
internalName=Internal name:
internalNameTip=Internal name without extension, original filename or module name for example.
companyName=Company name:
trademarks=Trademarks:
language=Language:
charset=Charset:

addMessages=Add custom messages
startupErr=Startup error:
bundledJreErr=Bundled JRE error:
jreVersionErr=JRE version error:
jreVersionErrTip=Launch4j will append the required version number at the end of this message.
launcherErr=Launcher error:
instanceAlreadyExistsMsg=Inst. already exists:
instanceAlreadyExistsMsgTip=Message displayed by single instance console applications if an instance already exists.
