package com.mc.tool.caesar.api.datamodel.vp;

/**
 * .
 */
public class Vp7SubBuilder implements VpBuilder {
  @Override
  public VpConConfigData createConfigData() {
    return null;
  }

  @Override
  public int getVpconId(int extId, int conId) {
    return conId;
  }

  @Override
  public int getVpconInputIndex(int extId) {
    return 0;
  }

  @Override
  public String formatName(int id) {
    return String.format(Vp7Builder.VP7_NAME_FORMAT_STRING + "[%d]",
        id & 0xffff, id >> 16);
  }
}
