package com.mc.tool.caesar.api.utils;

import java.beans.PropertyChangeListener;
import java.beans.PropertyChangeSupport;
import java.util.Collection;

/**
 * .
 */
public final class CustomPropertyChangeSupport extends PropertyChangeSupport {

  private static final long serialVersionUID = 98456149362314L;
  private final Object sourceBean;

  public CustomPropertyChangeSupport(Object sourceBean) {
    super(sourceBean);
    this.sourceBean = sourceBean;
  }

  /**
   * .
   */
  public synchronized void addPropertyChangeListener(String[] propertyNames,
      PropertyChangeListener listener) {
    if (null != propertyNames) {
      for (String propertyName : propertyNames) {
        addPropertyChangeListener(propertyName, listener);
      }
    }
  }

  /**
   * .
   */
  public synchronized void addPropertyChangeListener(Collection<String> propertyNames,
      PropertyChangeListener listener) {
    if (null != propertyNames) {
      for (String propertyName : propertyNames) {
        addPropertyChangeListener(propertyName, listener);
      }
    }
  }

  /**
   * .
   */
  public void fireMultiIndexedPropertyChange(String propertyName, int[] indexes, Object oldValue,
      Object newValue) {
    firePropertyChange(new MultiIndexedPropertyChangeEvent(this.sourceBean, propertyName, oldValue,
        newValue, indexes));
  }

  /**
   * .
   */
  public void fireMultiIndexedPropertyChange(String propertyName, int[] indexes, int oldValue,
      int newValue) {
    if (newValue != oldValue) {
      fireMultiIndexedPropertyChange(propertyName, indexes, Integer.valueOf(newValue),
          Integer.valueOf(oldValue));
    }
  }

  /**
   * .
   */
  public void fireMultiIndexedPropertyChange(String propertyName, int[] indexes, boolean oldValue,
      boolean newValue) {
    if (newValue != oldValue) {
      fireMultiIndexedPropertyChange(propertyName, indexes, Boolean.valueOf(newValue),
          Boolean.valueOf(oldValue));
    }
  }
}

