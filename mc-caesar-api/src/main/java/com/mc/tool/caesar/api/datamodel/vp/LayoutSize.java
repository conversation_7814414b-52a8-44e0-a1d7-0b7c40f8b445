package com.mc.tool.caesar.api.datamodel.vp;

/**
 * .
 */
public class LayoutSize {

  private int horzLength = 0;
  private int vertLength = 0;

  public LayoutSize(int horz, int vert) {
    this.horzLength = horz;
    this.vertLength = vert;
  }

  public LayoutSize() {

  }

  public int getHorzLength() {
    return horzLength;
  }

  public void setHorzLength(int horzLength) {
    this.horzLength = horzLength;
  }

  public int getVertLength() {
    return vertLength;
  }

  public void setVertLength(int vertLength) {
    this.vertLength = vertLength;
  }
}
