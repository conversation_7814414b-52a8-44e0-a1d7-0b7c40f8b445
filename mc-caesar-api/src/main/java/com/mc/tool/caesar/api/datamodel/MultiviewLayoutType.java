package com.mc.tool.caesar.api.datamodel;

import com.mc.tool.caesar.api.Bundle;
import com.mc.tool.caesar.api.interfaces.Nameable;
import lombok.Getter;

/**
 * MultiviewLayoutType.
 */
@Getter
public enum MultiviewLayoutType implements Nameable {
  FULL_SCREEN(0, 1, Bundle.multiviewLayout_full_screen()), // 单窗口全屏
  TWO_GRID(1, 2, Bundle.multiviewLayout_two_grid()),    // 一行两列
  FOUR_GRID(2, 4, Bundle.multiviewLayout_four_grid()),   // 四宫格
  SIDE_BAR(3, 4, Bundle.multiviewLayout_side_bar());     // 一大三小
  private final int index;
  private final int value;
  private final String name;

  MultiviewLayoutType(int index, int value, String name) {
    this.index = index;
    this.value = value;
    this.name = name;
  }

  /**
   * 根据索引获取枚举.
   */
  public static MultiviewLayoutType getByIndex(int index) {
    for (MultiviewLayoutType type : MultiviewLayoutType.values()) {
      if (type.index == index) {
        return type;
      }
    }
    return null;
  }

}
