package com.mc.tool.caesar.api.utils;

import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import java.util.Collection;
import lombok.Getter;

/**
 * .
 */
public class ExtenderUpdateInfo {

  @Getter
  private CaesarSwitchDataModel model;
  private byte[] updateParams;
  @Getter
  private Collection<ExtenderData> extenders;
  @Getter
  private final String address;

  /**
   * .
   *
   * @param model         model
   * @param extenderDatas extenderDatas
   * @param updateParams  updateParams
   */
  public ExtenderUpdateInfo(CaesarSwitchDataModel model, String address,
      Collection<ExtenderData> extenderDatas, byte[] updateParams) {
    this.model = model;
    this.address = address;
    this.extenders = extenderDatas;
    this.updateParams = updateParams.clone();
  }

  /**
   * .
   */
  public byte[] getUpdateParams() {
    return this.updateParams.clone();
  }

  /**
   * .
   */
  public void setUpdateParams(byte[] updateparams) {
    this.updateParams = updateparams.clone();
  }

}
