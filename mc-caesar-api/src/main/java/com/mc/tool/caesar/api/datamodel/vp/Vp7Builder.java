package com.mc.tool.caesar.api.datamodel.vp;

/**
 *  .
 */
public class Vp7Builder implements VpBuilder {

  public static final String VP7_NAME_FORMAT_STRING = "VP7_%05d";

  @Override
  public VpConConfigData createConfigData() {
    return new Vp7ConfigData();
  }

  @Override
  public int getVpconId(int extId, int conId) {
    // 一个VP7只有一个输入端口，不用处理ID
    return conId;
  }

  @Override
  public int getVpconInputIndex(int extId) {
    return 0;
  }

  @Override
  public String formatName(int id) {
    return String.format(VP7_NAME_FORMAT_STRING, id);
  }
}
