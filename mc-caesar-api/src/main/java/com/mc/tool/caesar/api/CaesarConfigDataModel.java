package com.mc.tool.caesar.api;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.datamodel.AbstractData;
import com.mc.tool.caesar.api.datamodel.ConfigData;
import com.mc.tool.caesar.api.datamodel.ConfigMetaData;
import com.mc.tool.caesar.api.datamodel.SwitchMacroData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.interfaces.CommitRollback;
import com.mc.tool.caesar.api.interfaces.ConfigDataModel;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.CfgError;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.DefaultCommitRollback;
import com.mc.tool.caesar.api.utils.FileTransfer;
import com.mc.tool.caesar.api.utils.FtpSupport;
import com.mc.tool.caesar.api.utils.UrlValidator;
import java.beans.PropertyChangeListener;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLDecoder;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * .
 *
 * @brief 用于加载与写入configdata
 */
public class CaesarConfigDataModel extends DefaultCommitRollback implements ConfigDataModel {

  private static final Logger LOG = Logger.getLogger(CaesarConfigDataModel.class.toString());
  public static final String PROPERTY_CONFIGDATA_LOADED = "ConfigDataModel.configdata.loading";
  public static final String PROPERTY_CONFIGDATA_LOADING = "ConfigDataModel.configdata.loaded";
  public static final String PROPERTY_CONFIGDATA_FAILED = "ConfigDataModel.configdata.failed";
  private final CustomPropertyChangeSupport pcs = new CustomPropertyChangeSupport(this);
  private final ConfigDataManager dataManager = new ConfigDataManager();
  @Expose
  private final ConfigData configData = new ConfigData(this.pcs, this.dataManager);

  /**
   * .
   */
  public CaesarConfigDataModel() {
    this.dataManager.setConfigData(this.configData);

    initCommitRollback();
  }

  public final CustomPropertyChangeSupport getChangeSupport() {
    return this.pcs;
  }

  public final ConfigDataManager getConfigDataManager() {
    return this.dataManager;
  }

  public final ConfigMetaData getConfigMetaData() {
    return this.configData.getConfigMetaData();
  }

  @Override
  public final synchronized void addPropertyChangeListener(PropertyChangeListener listener) {
    this.pcs.addPropertyChangeListener(listener);
  }

  @Override
  public final synchronized void addPropertyChangeListener(String[] propertyNames,
      PropertyChangeListener listener) {
    if (null != propertyNames) {
      for (String propertyName : propertyNames) {
        addPropertyChangeListener(propertyName, listener);
      }
    }
  }

  @Override
  public final synchronized void addPropertyChangeListener(Collection<String> propertyNames,
      PropertyChangeListener listener) {
    if (null != propertyNames) {
      for (String propertyName : propertyNames) {
        addPropertyChangeListener(propertyName, listener);
      }
    }
  }

  @Override
  public final synchronized void addPropertyChangeListener(String propertyName,
      PropertyChangeListener listener) {
    this.pcs.addPropertyChangeListener(propertyName, listener);
  }

  @Override
  public final synchronized void removePropertyChangeListener(PropertyChangeListener listener) {
    this.pcs.removePropertyChangeListener(listener);
  }

  @Override
  public final synchronized void removePropertyChangeListener(String[] propertyNames,
      PropertyChangeListener listener) {
    if (null != propertyNames) {
      for (String propertyName : propertyNames) {
        removePropertyChangeListener(propertyName, listener);
      }
    }
  }

  @Override
  public final synchronized void removePropertyChangeListener(Collection<String> propertyNames,
      PropertyChangeListener listener) {
    if (null != propertyNames) {
      for (String propertyName : propertyNames) {
        removePropertyChangeListener(propertyName, listener);
      }
    }
  }

  @Override
  public final synchronized void removePropertyChangeListener(String propertyName,
      PropertyChangeListener listener) {
    this.pcs.removePropertyChangeListener(propertyName, listener);
  }

  public final ConfigData getConfigData() {
    return this.configData;
  }

  public void initDefaults() {
    this.configData.initDefaults();
  }

  @Override
  public final void readFile(String fileName) throws ConfigException {
    if (null == fileName || fileName.isEmpty()) {
      throw new ConfigException(CfgError.NOT_FOUND);
    }
    InputStream is = null;
    try {
      is = new FileInputStream(fileName);
      readData(is, ConfigData.IoMode.Available);
      return;
    } catch (IOException ioe) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, ioe);
    } finally {
      if (null != is) {
        try {
          is.close();
        } catch (IOException ioe) {
          LOG.log(Level.SEVERE, null, ioe);
        }
      }
    }
  }

  private void readFtp(String urlPath, ConfigData.IoMode readMode) throws ConfigException {
    InputStream is = null;

    UrlValidator urlValidator = new UrlValidator(urlPath, UrlValidator.Protocol.FTP);
    if (urlValidator.isValid()) {
      try {
        byte[] bytes = FileTransfer.read(urlPath);
        is = new ByteArrayInputStream(bytes);
        readData(is, readMode);
      } catch (BusyException ex) {
        LOG.log(Level.SEVERE, null, ex);
        throw new ConfigException(CfgError.BUSY);
      } catch (IOException ex) {
        LOG.log(Level.SEVERE, null, ex);
        throw new ConfigException(CfgError.IO);
      } finally {
        try {
          if (is != null) {
            is.close();
          }
        } catch (IOException ex) {
          LOG.log(Level.WARNING, null, ex);
        }
      }
    }
  }

  @Override
  public void readFtp(String urlPath) throws ConfigException {
    readFtp(urlPath, ConfigData.IoMode.Available);
  }

  /**
   * .
   */
  public void readData(InputStream is, ConfigData.IoMode readMode) throws ConfigException {
    boolean success = false;
    try {
      this.pcs.firePropertyChange(CaesarConfigDataModel.PROPERTY_CONFIGDATA_LOADING, null,
          Boolean.TRUE);
      this.configData.readData(new CfgReader(is, true), readMode);
      success = true;
    } catch (Exception ex) {
      LOG.log(Level.WARNING, "Fail to read data!", ex);
    } finally {
      if (success) {

        this.pcs.firePropertyChange(CaesarConfigDataModel.PROPERTY_CONFIGDATA_LOADED, null,
            Boolean.TRUE);
      } else {
        if (this.configData.getDataParts().isEmpty()
            || !this.configData.getDataParts().contains(ConfigData.DataPart.SYSTEM)) {
          rollback(Threshold.ALL);
        } else {
          this.configData.getCrWrapperConsole().rollback(Threshold.ALL);
          this.configData.getCrWrapperCpu().rollback(Threshold.ALL);
          this.configData.getCrWrapperExtender().rollback(Threshold.ALL);
          this.configData.getCrWrapperFunctionKey().rollback(Threshold.ALL);
          this.configData.getCrWrapperMatrix().rollback(Threshold.ALL);
          this.configData.getCrWrapperPort().rollback(Threshold.ALL);
          this.configData.getCrWrapperUser().rollback(Threshold.ALL);
          commit(Threshold.ALL);
        }
        this.pcs.firePropertyChange(CaesarConfigDataModel.PROPERTY_CONFIGDATA_FAILED, null,
            Boolean.TRUE);
      }
    }
  }

  /**
   * .
   */
  public void readSwitchMacro(SwitchMacroData switchMacroData) throws ConfigException {
    if (null == switchMacroData || switchMacroData.getFileName() == null
        || switchMacroData.getFileName().isEmpty()) {
      throw new ConfigException(CfgError.NOT_FOUND);
    }
    InputStream ios = null;
    try {
      ios = new FileInputStream(switchMacroData.getFileName());
      CfgReader reader = new CfgReader(ios);
      if (reader.available() > 0) {
        switchMacroData.setDeviceName(reader.readString(16));
        switchMacroData.setConfigName(reader.readString(16));
      }
      while (reader.available() > 0) {
        int type = reader.readInteger();
        if (type == CaesarControllerConstants.SwitchRequest.SET_CPUCON_CONNECTION_BLOCK
            .getByteValue()) {
          int size = reader.readInteger();
          Map<Integer, Integer> fullAccess = new HashMap<>();
          for (int i = 0; i < size; i++) {
            int conId = reader.readInteger();
            int cpuId = reader.readInteger();
            fullAccess.put(conId, cpuId);
          }
          switchMacroData.setFullAccess(fullAccess);
        } else if (type == CaesarControllerConstants.SwitchRequest.SET_CPU_CONNECT_TO_CON
            .getByteValue()) {
          int size = reader.readInteger();
          Map<Integer, Integer> videoAccess = new HashMap<>();
          for (int i = 0; i < size; i++) {
            int conId = reader.readInteger();
            int cpuId = reader.readInteger();
            videoAccess.put(conId, cpuId);
          }
          switchMacroData.setVideoAccess(videoAccess);
        } else if (type == CaesarControllerConstants.SwitchRequest.SET_CPUCON_CONNECTION_BYMODE
            .getByteValue()) {
          int size = reader.readInteger();
          Map<Integer, Integer> privateMode = new HashMap<>();
          for (int i = 0; i < size; i++) {
            int conId = reader.readInteger();
            int cpuId = reader.readInteger();
            privateMode.put(conId, cpuId);
          }
          switchMacroData.setPrivateMode(privateMode);
        }
      }
      return;
    } catch (IOException ioe) {
      throw new ConfigException(CfgError.IO, ioe);
    } finally {
      if (null != ios) {
        try {
          ios.close();
        } catch (IOException ioe) {
          LOG.log(Level.SEVERE, ioe.getMessage());
        }
      }
    }
  }

  public void writeData(OutputStream os, ConfigData.IoMode readMode) throws ConfigException {
    this.configData.writeData(new CfgWriter(os), readMode);
    this.configData.commit(Threshold.ALL);
  }

  @Override
  public final void writeFile(String fileName) throws ConfigException {
    if (null == fileName || fileName.isEmpty()) {
      throw new ConfigException(CfgError.NOT_FOUND);
    }
    ByteArrayOutputStream os = null;
    OutputStream fos = null;
    try {
      fos = new FileOutputStream(fileName);
      os = new ByteArrayOutputStream(10240);
      writeData(os, ConfigData.IoMode.Available);

      String property = System.getProperty("ConfigFilesUnencrypted");
      if (Boolean.parseBoolean(property)) {
        fos.write(os.toByteArray());
      } else {
        fos.write(CfgWriter.encrypt(os));
      }
      return;
    } catch (IOException ioe) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, ioe);
    } finally {
      if (null != fos) {
        try {
          fos.close();
        } catch (IOException ioe) {
          LOG.log(Level.SEVERE, null, ioe);
        }
      }
      if (null != os) {
        try {
          os.close();
        } catch (IOException ioe) {
          LOG.log(Level.SEVERE, null, ioe);
        }
      }
    }
  }

  @Override
  public void writeFtp(String urlPath) throws ConfigException {
    ByteArrayOutputStream os = new ByteArrayOutputStream();
    writeData(os, ConfigData.IoMode.Available);
    try {
      FileTransfer.write(os.toByteArray(), urlPath);
    } catch (DeviceConnectionException | BusyException ex) {
      LOG.log(Level.SEVERE, null, ex);
      throw new ConfigException(CfgError.BUSY);
    }
  }

  /**
   * .
   */
  public void writeSwitchMacro(SwitchMacroData switchMacroData) throws ConfigException {
    if (null == switchMacroData || switchMacroData.getFileName() == null
        || switchMacroData.getFileName().isEmpty()) {
      throw new ConfigException(CfgError.NOT_FOUND);
    }
    OutputStream fos = null;
    try {
      fos = new FileOutputStream(switchMacroData.getFileName());

      CfgWriter writer = new CfgWriter(fos);

      writer.writeString(switchMacroData.getDeviceName(), 16);
      writer.writeString(switchMacroData.getConfigName(), 16);
      if (switchMacroData.getFullAccess() != null
          && switchMacroData.getFullAccess().size() > 0) {
        writer.writeInteger(
            CaesarControllerConstants.SwitchRequest.SET_CPUCON_CONNECTION_BLOCK.getByteValue());
        writer.writeInteger(switchMacroData.getFullAccess().size());
        for (Map.Entry<Integer, Integer> entry : switchMacroData.getFullAccess().entrySet()) {
          writer.writeInteger(entry.getKey());
          writer.writeInteger(entry.getValue());
        }
      }
      if (switchMacroData.getVideoAccess() != null
          && switchMacroData.getVideoAccess().size() > 0) {
        writer.writeInteger(
            CaesarControllerConstants.SwitchRequest.SET_CPU_CONNECT_TO_CON.getByteValue());
        writer.writeInteger(switchMacroData.getVideoAccess().size());
        for (Map.Entry<Integer, Integer> entry : switchMacroData.getVideoAccess().entrySet()) {
          writer.writeInteger(entry.getKey());

          writer.writeInteger(entry.getValue());
        }
      }
      if (switchMacroData.getPrivateMode() != null
          && switchMacroData.getPrivateMode().size() > 0) {
        writer.writeInteger(
            CaesarControllerConstants.SwitchRequest.SET_CPUCON_CONNECTION_BYMODE.getByteValue());
        writer.writeInteger(switchMacroData.getPrivateMode().size());
        for (Map.Entry<Integer, Integer> entry : switchMacroData.getPrivateMode().entrySet()) {
          writer.writeInteger(entry.getKey());

          writer.writeInteger(entry.getValue());
        }
      }
      return;
    } catch (IOException ioe) {
      throw new ConfigException(CfgError.IO, ioe);
    } finally {
      if (null != fos) {
        try {
          fos.close();
        } catch (IOException ioe) {
          LOG.log(Level.SEVERE, ioe.getMessage());
        }
      }
    }
  }

  @Override
  protected Collection<? extends CommitRollback> getDependentCommitRollbacks() {
    return Collections.singletonList(this.dataManager);
  }

  @Override
  public boolean checkUserRights(String urlName) throws ConfigException {
    int index = urlName.lastIndexOf('/');
    if (-1 != index) {
      try {
        return FtpSupport.checkUserRights(urlName, System.getProperty("default.charset"));
      } catch (IOException ex) {
        throw new ConfigException(CfgError.USER_ERROR, ex);
      }
    }
    throw new ConfigException(CfgError.CONNECTION_ERROR);
  }

  @Override
  public Threshold getUiThreshold() {
    return AbstractData.THRESHOLD_UI_LOCAL_CHANGES;
  }

  protected String getUser(URL url) throws ConfigException {
    String[] userInfo = url.getUserInfo().split(":");
    if (userInfo.length == 2) {
      try {
        return URLDecoder.decode(userInfo[0], System.getProperty("default.charset"));
      } catch (UnsupportedEncodingException ex) {
        throw new ConfigException(CfgError.USER_ERROR);
      }
    }
    throw new ConfigException(CfgError.USER_ERROR);
  }

  protected String getPassword(URL url) throws ConfigException {
    String[] userInfo = url.getUserInfo().split(":");
    if (userInfo.length == 2) {
      try {
        return URLDecoder.decode(userInfo[1], System.getProperty("default.charset"));
      } catch (UnsupportedEncodingException ex) {
        throw new ConfigException(CfgError.USER_ERROR);
      }
    }
    throw new ConfigException(CfgError.USER_ERROR);
  }

  @Override
  public void clear() {
    for (PropertyChangeListener listener : this.pcs.getPropertyChangeListeners()) {
      this.pcs.removePropertyChangeListener(listener);
    }
  }
}

