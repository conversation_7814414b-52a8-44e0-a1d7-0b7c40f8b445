package com.mc.tool.caesar.api.interfaces;

import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;

/**
 * .
 */
public interface ConnectionModel {

  void setConnection(String paramString) throws ConfigException, BusyException;

  void closeExternalConnection();

  void closeConnection();

  /**
   * .
   *
   * @brief 激活设备里的配置，如default.dtc等
   * @note 会重启设备与其级联的设备
   */
  //configNr 配置的序号，从0开始
  void activateConfig(int paramInt) throws ConfigException, BusyException;

  void reloadConfigData() throws ConfigException, BusyException;

  boolean isConnected();

  boolean isAdminUser(String paramString) throws ConfigException, BusyException;

  boolean userExists(String paramString) throws ConfigException, BusyException;

  /**
   * .
   *
   * @return 如果有效，返回true，否则抛出ConfigException
   * @brief 检查用户名是否有效 //urlName 带用户与密码的url 如********************************/，最后必须带斜杠
   */
  boolean checkUserRights(String paramString) throws ConfigException, BusyException;

  boolean checkConfigVersion(String paramString,
      ConfigDataModel paramConfigDataModel) throws ConfigException, BusyException;

  int getConfigVersion() throws ConfigException, BusyException;
}

