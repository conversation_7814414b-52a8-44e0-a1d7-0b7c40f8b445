package com.mc.tool.caesar.api.utils;

import com.mc.tool.caesar.api.exception.InvalidIpException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.StringTokenizer;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * .
 */
public final class IpUtil {

  static final Logger LOG = Logger.getLogger(IpUtil.class.getName());
  public static final int INVALID_IP = 0;
  public static final int IPv4 = 4;
  public static final int IPv6 = 6;

  /**
   * 删除ip地址字符串中多余的字符.
   *
   * @param ipAddress ip地址
   * @return trim后的字符串
   * @throws Exception 如果ip地址不符合规范，抛出异常
   */
  public static String trimIpAddress(String ipAddress) throws Exception {
    short[] ip = checkIpAddress(ipAddress);
    return ip[0] + "." + ip[1] + "." + ip[2] + "." + ip[3];
  }

  /**
   * 检查ip地址字符串是否符合ip规范，而且第一个数字不能为0.
   *
   * @param octets ip字符串
   * @return ip数据
   * @throws Exception 如果不符合，抛出异常
   */
  public static short[] checkIpAddress(String octets) throws Exception {
    return checkIpAddress(octets, false);
  }

  /**
   * 检查ip地址字符串是否符合ip规范，而且第一个数字不能为0.
   *
   * @param octets ip字符串
   * @param allow0 第一个数字是否允许为0
   * @return ip数据
   * @throws Exception 如果不符合，抛出异常
   */
  public static short[] checkIpAddress(String octets, boolean allow0) throws Exception {
    int ioctetCount = 0;

    short[] atemp = new short[4];

    String tmpOctets = octets + ".";
    char[] achars = tmpOctets.toCharArray();

    StringBuilder sbTemp = new StringBuilder();
    for (char achar : achars) {
      switch (achar) {
        case '.':
          int ioctet;
          try {
            ioctet = Integer.parseInt(sbTemp.toString());
          } catch (NumberFormatException nfe) {
            throw new Exception();
          }
          if (ioctet < 0 || ioctet > 255) {
            throw new Exception();
          }
          atemp[ioctetCount] = (short) ioctet;
          sbTemp.setLength(0);
          ioctetCount++;
          break;
        default:
          sbTemp.append(achar);
      }
    }
    if (ioctetCount == 0 || ioctetCount != 4) {
      throw new Exception();
    }
    short[] aresults = new short[ioctetCount];
    System.arraycopy(atemp, 0, aresults, 0, ioctetCount);
    int min = allow0 ? 0 : 1;
    if (aresults[0] < min || aresults[0] > 255) {
      throw new Exception();
    }
    if (aresults[1] < 0 || aresults[1] > 255) {
      throw new Exception();
    }
    if (aresults[2] < 0 || aresults[2] > 255) {
      throw new Exception();
    }
    if (aresults[3] < 0 || aresults[3] > 255) {
      throw new Exception();
    }
    return aresults;
  }

  private static boolean singleByteCheck(byte addr) {
    byte[] allowed = {(byte) 255, (byte) 254, (byte) 252, (byte) 248, (byte) 240, (byte) 224,
        (byte) 192, (byte) 128, 0};
    for (byte b : allowed) {
      if (addr == b) {
        return true;
      }
    }
    return false;
  }

  /**
   * 检查子网掩码的数据是否合规范.
   *
   * @param mask mask
   */
  public static boolean checkSubnetMask(byte[] mask) {
    if (mask == null || mask.length != 4) {
      return false;
    }
    if ((mask[0] & 0xff) == 255) {
      if ((mask[1] & 0xff) == 255) {
        if ((mask[2] & 0xff) == 255) {
          return singleByteCheck(mask[3]);
        }
        if (singleByteCheck(mask[2])) {
          return mask[3] == 0;
        }
        return false;
      }
      if (singleByteCheck(mask[1])) {
        return mask[2] == 0 && mask[3] == 0;
      }
      return false;
    }
    if (singleByteCheck(mask[0])) {
      return mask[1] == 0 && mask[2] == 0 && mask[3] == 0;
    }
    return false;
  }

  /**
   * 检查子网掩码字符串是否符合规范.
   *
   * @param octets octets
   * @return 子网掩码数据
   */
  public static boolean checkSubnetMask(String octets) {
    byte[] ipb;
    try {
      StringTokenizer st = new StringTokenizer(octets, ".");

      ipb = new byte[]{(byte) (Integer.parseInt(st.nextToken()) & 0xFF),
          (byte) (Integer.parseInt(st.nextToken()) & 0xFF),
          (byte) (Integer.parseInt(st.nextToken()) & 0xFF),
          (byte) (Integer.parseInt(st.nextToken()) & 0xFF)};
    } catch (RuntimeException ex) {
      return false;
    } catch (Exception ex) {
      return false;
    }
    return checkSubnetMask(ipb);
  }

  /**
   * 检查是否是有效的主机名.
   */
  public static boolean isValidHostname(String adr) {
    if (adr == null || adr.trim().length() == 0 || adr.startsWith("-")
        || adr.contains(" ")) {
      return false;
    }
    Pattern pt = Pattern.compile("[a-zA-Z0-9-_.]+");
    Matcher mat = pt.matcher(adr);
    return mat.matches();
  }

  public static boolean isDeactivated(String adr) {
    return "s0.0.0.0".substring(1).equals(adr);
  }

  /**
   * 是否为有效的IP地址.
   */
  public static boolean isValidIp(String adr) {
    if (adr == null) {
      return false;
    }
    @SuppressWarnings("unused")
    String byteExp = "(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])";

    String exp2 =
        "(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\."
            + "(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])"
            + "\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])"
            + "\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])";

    return Pattern.matches(exp2, adr);
  }

  /**
   * 是否为有效的子网掩码.
   */
  public static boolean isValidIpSubnet(String adr) {
    String tmpAdr = adr;
    if (tmpAdr == null) {
      return false;
    }
    String[] splittedSubnet = tmpAdr.split("/");
    if (splittedSubnet.length < 2) {
      return false;
    }
    String subnetMask = splittedSubnet[1];
    if (subnetMask.contains(".")) {
      if (isValidIp(subnetMask)) {
        tmpAdr = splittedSubnet[0] + "/" + getSubnetAddress(subnetMask);
      } else {
        return false;
      }
    }
    @SuppressWarnings("unused")
    String byteExp = "(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])";

    String exp3 =
        "(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])(\\.(25[0-5]|2"
            + "[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])){0,3}\\/(3[0-1]|[1-2][0-9]|[0-9])";

    String exp4 = "(\\*\\.){3}\\*";

    return Pattern.matches(exp3, tmpAdr) || Pattern.matches(exp4, tmpAdr);
  }

  /**
   * getSubnetAddress.
   */
  public static String getSubnetAddress(String subnet) {
    long address = intAddress(subnet);
    for (int i = 31; i >= 0; i--) {
      long pow = (long) (Math.pow(2.0D, i));
      if ((address & pow) != pow) {
        return String.valueOf(31 - i);
      }
    }
    return "0";
  }

  /**
   * getSubnetMask.
   */
  public static String getSubnetMask(String subnet) {
    long subnetLong = 0L;
    int subnetInt = Integer.parseInt(subnet);
    for (long i = 0L; i < subnetInt; i += 1L) {
      subnetLong += 1L << (int) (31L - i);
    }
    return inetAddress(subnetLong);
  }

  /**
   * matchIpSubnet.
   */
  public static int matchIpSubnet(String adr, String subnet) {
    String tmpSubnet = subnet;
    if (adr == null || tmpSubnet == null) {
      return 0;
    }
    if (!isValidIp(adr) || !isValidIpSubnet(tmpSubnet)) {
      return 0;
    }
    String[] splittedSubnet = tmpSubnet.split("/");
    String subnetMask = splittedSubnet[1];
    if (subnetMask.contains(".")) {
      if (isValidIp(subnetMask)) {
        tmpSubnet = splittedSubnet[0] + "/" + getSubnetAddress(subnetMask);
      } else {
        return 0;
      }
    }
    HostAndSubnetPair adrHs = parseHostOrNetString(adr);

    HostAndSubnetPair subnetHs = parseHostOrNetString(tmpSubnet);

    int smask = subnetHs.getSubnet();
    if (smask == 32) {
      return smask;
    }
    int adrMasked = mask((int) adrHs.getHost(), smask);
    int subnetMasked = mask((int) subnetHs.getHost(), smask);
    if (adrMasked == subnetMasked) {
      return smask;
    }
    return 0;
  }

  /**
   * mask.
   */
  private static int mask(int addr, int maskbits) {
    if (maskbits == 0) {
      return 0;
    }
    return addr & -1 << 32 - maskbits;
  }

  /**
   * parseHostOrNetString.
   */
  private static HostAndSubnetPair parseHostOrNetString(String hostOrNetString) {
    int subnet = 32;
    int div = hostOrNetString.indexOf('/');
    long host;
    if (div == -1) {
      host = intAddress(hostOrNetString);
    } else {
      subnet = Integer.parseInt(hostOrNetString.substring(div + 1));
      host = intAddress(hostOrNetString.substring(0, div));
    }
    return new HostAndSubnetPair(host, subnet);
  }

  /**
   * intAddress.
   */
  public static long intAddress(String addr) {
    long xx;
    try {
      byte[] bb = InetAddress.getByName(addr).getAddress();

      xx = ((long) (bb[0] & 0xFF) << 24) + ((bb[1] & 0xFF) << 16) + ((bb[2] & 0xFF) << 8) + (bb[3]
          & 0xFF);
    } catch (UnknownHostException ue) {
      return 0L;
    } catch (SecurityException se) {
      return 0L;
    }
    return xx;
  }

  /**
   * inetAddress.
   */
  public static String inetAddress(long addr) {
    long b1 = addr & 0xFF;
    long b2 = (addr & 0xFF00) >> 8;
    long b3 = (addr & 0xFF0000) >> 16;
    long b4 = (addr & 0xFFFFFFFFFF000000L) >> 24;
    return b4 + "." + b3 + "." + b2 + "."
        + b1;
  }

  /**
   * .
   */
  public interface Ipv6Compattble {

  }

  /**
   * .
   */
  public interface Ipv4Compattble {

  }

  private static class HostAndSubnetPair {

    private long host;
    private int subnet;

    public HostAndSubnetPair(long host, int subnet) {
      this.host = host;
      this.subnet = subnet;
    }

    public long getHost() {
      return this.host;
    }

    public int getSubnet() {
      return this.subnet;
    }
  }

  /**
   * isSameNetwork.
   */
  public static synchronized boolean isSameNetwork(byte[] netw, byte[] iaddr, byte[] subnetaddr) {
    boolean same = false;
    byte[] locnet = netw;
    byte[] network = getNetworkAddress(iaddr, subnetaddr);
    int ii;
    for (ii = 0; ii < 4; ii++) {
      byte ll = locnet[ii];
      byte nn = network[ii];
      if (ll != nn) {
        break;
      }
    }
    if (ii == 4) {
      same = true;
    }
    return same;
  }

  /**
   * isOnSameNetwork.
   */
  public static synchronized boolean isOnSameNetwork(byte[] netw, byte[] broad, byte[] iaddr,
      byte[] subnetaddr) {
    boolean onSame = true;
    byte[] locnet = netw;
    int ii = 0;
    int il = -1;

    byte[] tmpIaddr = iaddr;
    if (subnetaddr != null) {
      tmpIaddr = getNetworkAddress(tmpIaddr, subnetaddr);
    }
    while (ii < 4) {
      int in = tmpIaddr[ii] & 0xFF;
      if (il == -1) {
        int ib = broad[ii] & 0xFF;
        if (ib < in) {
          onSame = false;
          break;
        }
        if (ib > in) {
          il = locnet[ii] & 0xFF;
          if (il < in) {
            break;
          }
          if (il > in) {
            onSame = false;
            break;
          }
        }
      } else {
        il = locnet[ii] & 0xFF;
        if (il < in) {
          break;
        }
        if (il > in) {
          onSame = false;
          break;
        }
      }
      ii++;
    }
    return onSame;
  }

  /**
   * getHostCount.
   */
  public static synchronized int getHostCount(byte[] ip, byte[] subnet) {
    byte[] subnetaddr = subnet;
    int ii = 3;
    int merk = 0;
    int hosts = 0;
    double res2 = Math.log(2.0D);
    while (ii >= 0) {
      int rest = 255 - subnetaddr[ii] & 0xFF;
      if (rest < 255) {
        double rr = rest + 1.0D;
        double res1 = Math.log(rr);
        merk += (int) (res1 / res2);
        hosts = Double.valueOf(Math.pow(2.0D, merk)).intValue();
        break;
      }
      merk += 8;
      ii--;
    }
    return hosts - 2;
  }

  /**
   * getBroadcastAddress.
   */
  public static synchronized byte[] getBroadcastAddress(byte[] ip, byte[] subnet) {

    int slash = 0;
    for (int i = 0; i < subnet.length; i++) {
      int jj = subnet[i] & 0xFF;
      if (jj < 255) {
        slash = (int) (Math.log(256 - jj) / Math.log(2.0D));
        switch (i) {
          case 0:
            slash += 24;
            break;
          case 1:
            slash += 16;
            break;
          case 2:
            slash += 8;
            break;
          default:
            break;
        }
      }
    }
    int addr1 = ip[0] & 0xFF;
    String saddr1 = Integer.toBinaryString(addr1);
    int length = saddr1.length();
    int ii;
    if (saddr1.length() < 8) {
      for (ii = 0; ii < 8 - length; ii++) {
        saddr1 = String.format("0%s", saddr1);
      }
    }
    int addr2 = ip[1] & 0xFF;
    String saddr2 = Integer.toBinaryString(addr2);
    length = saddr2.length();
    if (length < 8) {
      for (ii = 0; ii < 8 - length; ii++) {
        saddr2 = String.format("0%s", saddr2);
      }
    }
    int addr3 = ip[2] & 0xFF;
    String saddr3 = Integer.toBinaryString(addr3);
    length = saddr3.length();
    if (length < 8) {
      for (ii = 0; ii < 8 - length; ii++) {
        saddr3 = String.format("0%s", saddr3);
      }
    }
    int addr4 = ip[3] & 0xFF;
    String saddr4 = Integer.toBinaryString(addr4);
    length = saddr4.length();
    if (length < 8) {
      for (ii = 0; ii < 8 - length; ii++) {
        saddr4 = String.format("0%s", saddr4);
      }
    }
    String saddr = saddr1 + saddr2 + saddr3 + saddr4;
    saddr = saddr.substring(0, saddr.length() - slash);
    StringBuilder buf = new StringBuilder();
    for (ii = 0; ii < slash; ii++) {
      buf.append("1");
    }
    saddr += buf.toString();
    byte[] broad = new byte[4];
    broad[0] = (byte) Integer.parseInt(saddr.substring(0, 8), 2);
    saddr = saddr.substring(8);
    broad[1] = (byte) Integer.parseInt(saddr.substring(0, 8), 2);
    saddr = saddr.substring(8);
    broad[2] = (byte) Integer.parseInt(saddr.substring(0, 8), 2);
    saddr = saddr.substring(8);
    broad[3] = (byte) Integer.parseInt(saddr.substring(0, 8), 2);
    return broad;
  }

  /**
   * 把ip值转换为字符串.
   *
   * @param addr addr
   * @return ip字符串
   */
  public static synchronized String getAddressString(byte[] addr) {
    try {
      return InetAddress.getByAddress(addr).getHostAddress();
    } catch (Exception ex) {
      LOG.log(Level.WARNING, "Fail to get address string!", ex);
    }
    return null;
  }

  /**
   * getAddressByte.
   */
  public static synchronized byte[] getAddressByte(String addr) {
    byte[] bb = new byte[4];
    StringTokenizer st = new StringTokenizer(addr, ".");
    int ii = 0;
    while (st.hasMoreTokens()) {
      String tok = st.nextToken();
      bb[ii] = Integer.valueOf(tok).byteValue();
      ii++;
    }
    if (ii != 4) {
      bb = null;
    }
    return bb;
  }

  /**
   * getNetworkAddress.
   */
  public static synchronized byte[] getNetworkAddress(byte[] ip, byte[] subnet) {
    byte[] network = new byte[4];
    for (int i = 0; i < 4; i++) {
      byte ia = ip[i];
      byte ss = subnet[i];
      byte nn = (byte) (ia & ss);
      network[i] = nn;
    }
    return network;
  }

  /**
   * getIpAddressType.
   */
  public static int getIpAddressType(String ip) {
    if (isValidIp(ip)) {
      return 4;
    }
    if (isValidIpSec(ip)) {
      return 6;
    }
    return 0;
  }

  /**
   * isValidIpSec.
   */
  public static boolean isValidIpSec(String ip) {
    if (ip == null) {
      return false;
    }
    @SuppressWarnings("unused")
    String blockExp = "([0123456789abcdefABCDEF]{1,4})";
    String[] block = ip.split(":");
    int ipl = ip.length();
    if (ipl >= 6 && ipl <= 39 && !ip.contains("::")) {
      for (String bs : block) {
        if (!Pattern.matches("([0123456789abcdefABCDEF]{1,4})", bs)) {
          return false;
        }
      }
      return true;
    }
    if (ipl >= 6 && ipl < 39) {
      if (!ip.contains("::") || ip.indexOf("::") != ip.lastIndexOf("::")) {
        return false;
      }
      for (String bs : block) {
        if (bs.length() != 0
            && !Pattern.matches("([0123456789abcdefABCDEF]{1,4})", bs)) {
          return false;
        }
      }
      return true;
    }
    return false;
  }

  /**
   * isIpAloopbackAddress.
   */
  public static boolean isIpAloopbackAddress(String ip) throws InvalidIpException {
    try {
      switch (getIpAddressType(ip)) {
        case 4:
        case 6:
          return InetAddress.getByName(ip).isLoopbackAddress();
        default:
          break;
      }
    } catch (UnknownHostException ex) {
      LOG.log(Level.WARNING, null, ex);
    }
    throw new InvalidIpException();
  }

  /**
   * validateIp.
   */
  public static boolean validateIp(String ip) {
    return isValidIp(ip) || isValidIpSec(ip);
  }

  /**
   * compareIps.
   */
  public static boolean compareIps(String ip1, String ip2) throws InvalidIpException {
    if (ip1 == null || ip2 == null) {
      throw new NullPointerException();
    }
    int type1 = getIpAddressType(ip1);
    int type2 = getIpAddressType(ip2);
    if (type1 == 0 || type2 == 0) {
      throw new InvalidIpException();
    }
    try {
      InetAddress inetaddress1 = InetAddress.getByName(ip1);
      InetAddress inetaddress2 = InetAddress.getByName(ip2);
      return inetaddress1.equals(inetaddress2);
    } catch (UnknownHostException ex) {
      throw new InvalidIpException(ex);
    }
  }

  /**
   * isIpInSubnet.
   */
  public static boolean isIpInSubnet(String ip, String subnetIp, String subnetMask) {
    if (ip == null || subnetIp == null || subnetMask == null) {
      throw new NullPointerException();
    }
    int type1 = getIpAddressType(ip);
    int type2 = getIpAddressType(subnetIp);
    int type3 = getIpAddressType(subnetMask);
    if (type1 == 0 || type2 == 0 || type3 == 0) {
      return false;
    }
    if (type1 != type2 || type2 != type3) {
      return false;
    }
    try {
      InetAddress iip = InetAddress.getByName(ip);
      InetAddress isubnetIp = InetAddress.getByName(subnetIp);
      InetAddress isubnetMask = InetAddress.getByName(subnetMask);

      byte[] bip = iip.getAddress();
      byte[] bmask = isubnetMask.getAddress();
      byte[] bsubnet = isubnetIp.getAddress();
      int len = bip.length;
      if (len != bmask.length || len != bsubnet.length) {
        return false;
      }
      byte[] b1 = new byte[len];
      byte[] b2 = new byte[len];
      for (int i = 0; i < len; i++) {
        b1[i] = (byte) (bip[i] & bmask[i]);
        b2[i] = (byte) (bsubnet[i] & bmask[i]);
      }
      return Arrays.equals(b1, b2);
    } catch (UnknownHostException ex) {
      LOG.log(Level.INFO, null, ex);
    }
    return false;
  }

  /**
   * isIpInSubnet.
   */
  public static boolean isIpInSubnet(String ip, String subnet) throws InvalidIpException {
    if (ip == null || subnet == null) {
      throw new NullPointerException();
    }
    return isIpInSubnet(ip, getSubnetIp(subnet), getFullSubnetMask(subnet));
  }

  /**
   * createSingelSubentString.
   */
  public static String createSingelSubentString(String subnetIp, String subnetMask)
      throws InvalidIpException {
    if (subnetIp == null || subnetMask == null) {
      throw new NullPointerException();
    }
    int type1 = getIpAddressType(subnetIp);
    int type2 = getIpAddressType(subnetMask);
    if (type1 != type2 || type1 == 0 || type2 == 0) {
      throw new InvalidIpException();
    }
    return subnetIp + "/" + String.valueOf(getSubnetBitNumber(subnetMask));
  }

  /**
   * getSubnetBitNumber.
   */
  public static int getSubnetBitNumber(String subnetMask) throws InvalidIpException {
    if (!validateIp(subnetMask)) {
      throw new InvalidIpException();
    }
    try {
      InetAddress mask = InetAddress.getByName(subnetMask);
      byte[] bmask = mask.getAddress();

      int number = 0;
      int ii = 0;
      @SuppressWarnings("unused")
      byte nullByte = 0;
      @SuppressWarnings("unused")
      byte fullByte = -1;
      @SuppressWarnings("unused")
      byte ebyte = Byte.MIN_VALUE;
      @SuppressWarnings("unused")
      byte zbyte = -64;
      @SuppressWarnings("unused")
      byte dbyte = -32;
      @SuppressWarnings("unused")
      byte vbyte = -16;
      @SuppressWarnings("unused")
      byte fbyte = -8;
      @SuppressWarnings("unused")
      byte sebyte = -4;
      @SuppressWarnings("unused")
      byte sibyte = -2;
      for (ii = 0; ii < bmask.length && -1 == bmask[ii]; ii++) {
      }
      number = ii * 8;
      if (ii < bmask.length && bmask[ii] != 0) {
        if (Byte.MIN_VALUE == bmask[ii]) {
          number++;
        } else if (-64 == bmask[ii]) {
          number += 2;
        } else if (-32 == bmask[ii]) {
          number += 3;
        } else if (-16 == bmask[ii]) {
          number += 4;
        } else if (-8 == bmask[ii]) {
          number += 5;
        } else if (-4 == bmask[ii]) {
          number += 6;
        } else if (-2 == bmask[ii]) {
          number += 7;
        }
      }
      return number;
    } catch (UnknownHostException ex) {
      LOG.log(Level.INFO, null, ex);

      throw new InvalidIpException();
    }
  }

  /**
   * getFullSubnetMask.
   */
  public static String getFullSubnetMask(String subnetString) throws InvalidIpException {
    if (subnetString == null) {
      throw new NullPointerException();
    }
    if (!vaildateSubnet(subnetString)) {
      throw new InvalidIpException();
    }
    String[] result = subnetString.split("/");
    int type1 = getIpAddressType(result[0]);
    int type2 = getIpAddressType(result[1]);
    if (type1 == 0) {
      throw new InvalidIpException();
    }
    if (type1 == type2) {
      return result[1];
    }
    try {
      InetAddress iip = InetAddress.getByName(result[0]);
      int subnet = Integer.parseInt(result[1]);

      byte[] raw = iip.getAddress();

      Arrays.fill(raw, Byte.decode("0x00"));

      int ii;
      for (ii = 0; subnet >= 8 + 8 * ii; ii++) {
        raw[ii] = -1;
      }
      int rest = subnet - 8 * ii;
      if (rest > 0) {
        byte toAdd;
        switch (rest) {
          case 1:
            toAdd = Byte.MIN_VALUE;
            break;
          case 2:
            toAdd = -64;
            break;
          case 3:
            toAdd = -32;
            break;
          case 4:
            toAdd = -16;
            break;
          case 5:
            toAdd = -8;
            break;
          case 6:
            toAdd = -4;
            break;
          case 7:
            toAdd = -2;
            break;
          default:
            throw new InvalidIpException();
        }
        raw[ii] = toAdd;
      }
      InetAddress mask = InetAddress.getByAddress(raw);
      return mask.getHostAddress();
    } catch (UnknownHostException ex) {
      throw new InvalidIpException();
    }
  }

  /**
   * getSubnetIp.
   */
  public static String getSubnetIp(String subnet) throws InvalidIpException {
    if (subnet == null) {
      throw new NullPointerException();
    }
    if (!subnet.contains("/")) {
      throw new InvalidIpException();
    }
    String ip = subnet.split("/")[0];
    if (!validateIp(ip)) {
      throw new InvalidIpException();
    }
    try {
      String fullSubnetMask = getFullSubnetMask(subnet);
      InetAddress isub = InetAddress.getByName(fullSubnetMask);
      InetAddress iip = InetAddress.getByName(ip);

      byte[] bip = iip.getAddress();
      byte[] bsub = isub.getAddress();
      if (bip.length != bsub.length) {
        throw new InvalidIpException();
      }
      int len = bip.length;
      byte[] newIp = new byte[len];
      for (int i = 0; i < len; i++) {
        newIp[i] = (byte) (bip[i] & bsub[i]);
      }
      return InetAddress.getByAddress(newIp).getHostAddress();
    } catch (UnknownHostException ex) {
      throw new InvalidIpException(ex);
    }
  }

  /**
   * compareSubnets.
   */
  public static boolean compareSubnets(String subnet1, String subnet2) throws InvalidIpException {
    boolean result = isIpInSubnet(getSubnetIp(subnet1), subnet2);
    if (!result) {
      return false;
    }
    int i1 = getSubnetBitNumber(getFullSubnetMask(subnet1));
    int i2 = getSubnetBitNumber(getFullSubnetMask(subnet2));
    result = i1 == i2;
    return result;
  }

  /**
   * vaildateSubnet.
   */
  public static boolean vaildateSubnet(String subnet) {
    if (subnet == null) {
      throw new NullPointerException();
    }
    String[] block = subnet.split("/");
    if (block.length != 2) {
      return false;
    }
    int type = getIpAddressType(block[0]);
    if (block[1].matches("[0-9]{1,3}")) {
      int subnetn = Integer.parseInt(block[1]);
      if (type == 4) {
        return subnetn > 0 && subnetn <= 32;
      }
      if (type == 6) {
        return subnetn >= 1 && subnetn <= 128;
      }
      return false;
    }
    int masktype = getIpAddressType(block[1]);
    if (masktype != type || masktype == 0) {
      return false;
    }
    byte[] possiblebytes = {0, Byte.MIN_VALUE, -64, -32, -16, -8, -4, -2, -1};
    try {
      byte[] subnetaddr = InetAddress.getByName(block[1]).getAddress();
      for (byte sa : subnetaddr) {
        boolean result = false;
        for (int u = 0; u < 9; u++) {
          if (sa == possiblebytes[u]) {
            result = true;
          }
        }
        if (!result) {
          return false;
        }
      }
      return true;
    } catch (UnknownHostException ex) {
      LOG.log(Level.INFO, null, ex);
    }
    return false;
  }

  /**
   * getClassMask.
   */
  public static String getClassMask(IpAddress ipAddress) throws InvalidIpException {
    if (ipAddress == null) {
      throw new InvalidIpException();
    }
    String ipAddrString = getAddressString(ipAddress.getData());

    return getClassMask(ipAddrString);
  }

  /**
   * .
   */
  public static String getClassMask(String ipAddress) throws InvalidIpException {
    if (!isValidIp(ipAddress)) {
      throw new InvalidIpException();
    }
    byte[] addrBytes = getAddressByte(ipAddress);
    if (addrBytes == null || addrBytes.length != 4) {
      throw new InvalidIpException();
    }
    if ((addrBytes[0] & 0x80) == 0) {
      return "s255.0.0.0".substring(1);
    }
    if ((addrBytes[0] & 0x80) != 0 && (addrBytes[0] & 0x40) == 0) {
      return "s255.255.0.0".substring(1);
    }
    if ((addrBytes[0] & 0x80) != 0 && (addrBytes[0] & 0x40) != 0
        && (addrBytes[0] & 0x20) == 0) {
      return "s255.255.255.0".substring(1);
    }
    return "s255.255.255.0".substring(1);
  }

  public static long getNumberOfHosts(String ip, String sub) {
    return getHostCount(getAddressByte(ip), getAddressByte(sub));
  }
}

