package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;
import java.util.Arrays;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * .
 */
public class SyslogData extends AbstractData {

  private static final Logger LOG = Logger.getLogger(SyslogData.class.getName());
  public static final String PROPERTY_BASE = "SystemConfigData.SyslogData.";
  public static final String FIELD_STATUS = "Status";
  public static final String FIELD_ADDRESS = "Address";
  // Syslog下的 系统日志服务器 ip的填写
  public static final String PROPERTY_ADDRESS = "SystemConfigData.SyslogData.Address";
  public static final String FIELD_PORT = "Port";
  // Syslog下的端口号 默认为514
  public static final String PROPERTY_PORT = "SystemConfigData.SyslogData.Port";
  public static final String FIELD_FACILITY = "Facility";
  public static final String PROPERTY_FACILITY = "SystemConfigData.SyslogData.Facility";
  public static final String FIELD_SYSLEVEL = "SysLevel";
  public static final String PROPERTY_SYSLEVEL = "SystemConfigData.SyslogData.SysLevel";
  public static final String FIELD_SYSLEVEL_DEBUG = "SysLevel.Debug";
  public static final String PROPERTY_SYSLEVEL_DEBUG = "SystemConfigData.SyslogData.SysLevel.Debug";
  public static final String FIELD_SYSLEVEL_INFO = "SysLevel.Info";
  // System有导航下的 System Data 的窗口标题 System - System Data 下的General 的Info
  public static final String PROPERTY_SYSLEVEL_INFO = "SystemConfigData.SyslogData.SysLevel.Info";
  public static final String FIELD_SYSLEVEL_NOTICE = "SysLevel.Notice";
  public static final String PROPERTY_SYSLEVEL_NOTICE =
      "SystemConfigData.SyslogData.SysLevel.Notice";
  public static final String FIELD_SYSLEVEL_WARNING = "SysLevel.Warning";
  public static final String PROPERTY_SYSLEVEL_WARNING =
      "SystemConfigData.SyslogData.SysLevel.Warning";
  public static final String FIELD_SYSLEVEL_ERROR = "SysLevel.Error";
  public static final String PROPERTY_SYSLEVEL_ERROR = "SystemConfigData.SyslogData.SysLevel.Error";
  // Syslog的是否选中 启用日志
  public static final String PROPERTY_SYSLOG_ENABLED = "SystemConfigData.SyslogData.Syslog.Enabled";
  public static final String PROPERTY_LOCALLOG_ENABLED =
      "SystemConfigData.SyslogData.LocalLog.Enabled";

  public static final int STATUS_ENABLE = 0x40;
  public static final int STATUS_DISABLE_LOCAL = 0x80;

  public static final int SYSLEVEL_DEBUG = 0x01;
  public static final int SYSLEVEL_INFO = 0x02;
  public static final int SYSLEVEL_NOTICE = 0x04;
  public static final int SYSLEVEL_WARNING = 0x08;
  public static final int SYSLEVEL_ERROR = 0x10;
  @Expose
  private int status; // 启用与否
  @Expose
  private byte[] address = new byte[4]; // server address
  @Expose
  private int port; // server port
  @Expose
  private int sysLevel; //

  /**
   * .
   */
  public SyslogData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager, int oid,
      String fqn) {
    super(pcs, configDataManager, oid, fqn);

    initCommitRollback();
  }

  @Override
  public void initDefaults() {
    setStatus(0);
    setSysLevel(this.sysLevel);

    this.port = CaesarConstants.SYSLOG_PORT;
  }

  public byte[] getAddress() {
    return Arrays.copyOf(this.address, this.address.length);
  }

  /**
   * .
   */
  public void setAddress(byte[] address) {
    byte[] oldValue = this.address;
    this.address = Arrays.copyOf(address, address.length);
    firePropertyChange(SyslogData.PROPERTY_ADDRESS, oldValue, this.address, new int[0]);
  }

  public int getPort() {
    return this.port;
  }

  /**
   * .
   */
  public void setPort(int port) {
    int oldValue = this.port;
    this.port = port;
    firePropertyChange(SyslogData.PROPERTY_PORT, Integer.valueOf(oldValue),
        Integer.valueOf(this.port), new int[0]);
  }


  public int getStatus() {
    return this.status;
  }

  public void setStatus(int status) {
    this.status = status;
  }

  /**
   * .
   */
  public void setEnabled(boolean enabled) {
    boolean oldValue = isEnabled();
    setStatus(Utilities.setBits(this.status, enabled, new int[]{STATUS_ENABLE}));
    firePropertyChange(SyslogData.PROPERTY_SYSLOG_ENABLED, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  /**
   * 设置启用本地日志.
   */
  public void setEnableLocalLog(boolean enabled) {
    boolean oldValue = isEnableLocalLog();
    setStatus(Utilities.setBits(this.status, !enabled, new int[]{STATUS_DISABLE_LOCAL}));
    firePropertyChange(SyslogData.PROPERTY_LOCALLOG_ENABLED, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  public boolean isEnabled() {
    return Utilities.areBitsSet(this.status, STATUS_ENABLE);
  }

  public boolean isEnableLocalLog() {
    return !Utilities.areBitsSet(this.status, STATUS_DISABLE_LOCAL);
  }

  public int getSysLevel() {
    return this.sysLevel;
  }

  /**
   * .
   */
  public void setSysLevel(int sysLevel) {
    int oldValue = this.sysLevel;
    this.sysLevel = sysLevel;
    firePropertyChange(SyslogData.PROPERTY_SYSLEVEL, Integer.valueOf(oldValue),
        Integer.valueOf(this.sysLevel), new int[0]);
  }

  public boolean isSysLevelDebug() {
    return Utilities.areBitsSet(this.sysLevel, SYSLEVEL_DEBUG);
  }

  /**
   * .
   */
  public void setSysLevelDebug(boolean enabled) {
    boolean oldValue = isSysLevelDebug();
    setSysLevel(Utilities.setBits(this.sysLevel, enabled, new int[]{SYSLEVEL_DEBUG}));
    firePropertyChange(SyslogData.PROPERTY_SYSLEVEL_DEBUG, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  public boolean isSysLevelInfo() {
    return Utilities.areBitsSet(this.sysLevel, SYSLEVEL_INFO);
  }

  /**
   * .
   */
  public void setSysLevelInfo(boolean enabled) {
    boolean oldValue = isSysLevelInfo();
    setSysLevel(Utilities.setBits(this.sysLevel, enabled, new int[]{SYSLEVEL_INFO}));
    firePropertyChange(SyslogData.PROPERTY_SYSLEVEL_INFO, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  public boolean isSysLevelNotice() {
    return Utilities.areBitsSet(this.sysLevel, SYSLEVEL_NOTICE);
  }

  /**
   * .
   */
  public void setSysLevelNotice(boolean enabled) {
    boolean oldValue = isSysLevelNotice();
    setSysLevel(Utilities.setBits(this.sysLevel, enabled, new int[]{SYSLEVEL_NOTICE}));
    firePropertyChange(SyslogData.PROPERTY_SYSLEVEL_NOTICE, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  public boolean isSysLevelWarning() {
    return Utilities.areBitsSet(this.sysLevel, SYSLEVEL_WARNING);
  }

  /**
   * .
   */
  public void setSysLevelWarning(boolean enabled) {
    boolean oldValue = isSysLevelWarning();
    setSysLevel(Utilities.setBits(this.sysLevel, enabled, new int[]{SYSLEVEL_WARNING}));
    firePropertyChange(SyslogData.PROPERTY_SYSLEVEL_WARNING, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  public boolean isSysLevelError() {
    return Utilities.areBitsSet(this.sysLevel, SYSLEVEL_ERROR);
  }

  /**
   * .
   */
  public void setSysLevelError(boolean enabled) {
    boolean oldValue = isSysLevelError();
    setSysLevel(Utilities.setBits(this.sysLevel, enabled, new int[]{SYSLEVEL_ERROR}));
    firePropertyChange(SyslogData.PROPERTY_SYSLEVEL_ERROR, Boolean.valueOf(oldValue),
        Boolean.valueOf(enabled), new int[0]);
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (SyslogData.PROPERTY_SYSLOG_ENABLED.equals(propertyName)) {
      setEnabled(Boolean.class.cast(value).booleanValue());
    } else if (SyslogData.PROPERTY_LOCALLOG_ENABLED.equals(propertyName)) {
      setEnableLocalLog(Boolean.class.cast(value).booleanValue());
    } else if (SyslogData.PROPERTY_ADDRESS.equals(propertyName)) {
      setAddress(byte[].class.cast(value));
    } else if (SyslogData.PROPERTY_PORT.equals(propertyName)) {
      setPort(Integer.class.cast(value).intValue());
    } else if (SyslogData.PROPERTY_SYSLEVEL_DEBUG.equals(propertyName)) {
      setSysLevelDebug(Boolean.class.cast(value).booleanValue());
    } else if (SyslogData.PROPERTY_SYSLEVEL_INFO.equals(propertyName)) {
      setSysLevelInfo(Boolean.class.cast(value).booleanValue());
    } else if (SyslogData.PROPERTY_SYSLEVEL_NOTICE.equals(propertyName)) {
      setSysLevelNotice(Boolean.class.cast(value).booleanValue());
    } else if (SyslogData.PROPERTY_SYSLEVEL_WARNING.equals(propertyName)) {
      setSysLevelWarning(Boolean.class.cast(value).booleanValue());
    } else if (SyslogData.PROPERTY_SYSLEVEL_ERROR.equals(propertyName)) {
      setSysLevelError(Boolean.class.cast(value).booleanValue());
    } else if (SyslogData.PROPERTY_SYSLEVEL.equals(propertyName)) {
      setSysLevel(Integer.class.cast(value).intValue());
    }

  }

  /**
   * .
   */
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    Level logLevel = Level.FINER;
    boolean isLoggable = LOG.isLoggable(logLevel);
    if (!getConfigDataManager().getConfigMetaData().getUtilVersion().isComplicatedSyslogData()) {
      if (isLoggable) {
        LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), SyslogData.PROPERTY_SYSLEVEL});
      }
      cfgWriter.writeInteger(this.sysLevel);
    }
    if (isLoggable) {
      LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), "Status"});
    }
    cfgWriter.writeInteger(getStatus());
    for (int idx = this.address.length; idx > 0; idx--) {
      if (isLoggable) {
        LOG.log(logLevel, "writing {0}.{1}[{2}]",
            new Object[]{getFqn(), "Address", Integer.valueOf(idx - 1)});
      }
      cfgWriter.write4Byte(this.address[idx - 1]);
    }
    if (getConfigDataManager().getConfigMetaData().getUtilVersion().isComplicatedSyslogData()) {
      if (isLoggable) {
        LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), SyslogData.PROPERTY_PORT});
      }
      cfgWriter.writeInteger(this.port);
      if (isLoggable) {
        LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), SyslogData.PROPERTY_SYSLEVEL});
      }
      cfgWriter.writeInteger(this.sysLevel);
    }
  }

  /**
   * .
   */
  public void readData(CfgReader cfgReader) throws ConfigException {
    Level logLevel = Level.FINER;
    boolean isLoggable = LOG.isLoggable(logLevel);
    if (!getConfigDataManager().getConfigMetaData().getUtilVersion().isComplicatedSyslogData()) {
      if (isLoggable) {
        LOG.log(logLevel, "reading {0}.{1}[{2}]",
            new Object[]{getFqn(), SyslogData.PROPERTY_SYSLEVEL});
      }
      int sysLevel = cfgReader.readInteger();
      if (!isPropertyChangedByUi(SyslogData.PROPERTY_SYSLEVEL)) {
        setSysLevel(sysLevel);
      }
    }
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), "Status"});
    }
    int status = cfgReader.readInteger();
    setStatus(status);

    byte[] newAddress = new byte[this.address.length];
    for (int idx = newAddress.length; idx > 0; idx--) {
      if (isLoggable) {
        LOG.log(logLevel, "reading {0}.{1}[{2}]",
            new Object[]{getFqn(), "Address", Integer.valueOf(idx - 1)});
      }
      newAddress[idx - 1] = cfgReader.read4ByteValue();
    }
    if (!isPropertyChangedByUi(SyslogData.PROPERTY_ADDRESS)) {
      setAddress(newAddress);
    }
    if (getConfigDataManager().getConfigMetaData().getUtilVersion().isComplicatedSyslogData()) {
      if (isLoggable) {
        LOG.log(logLevel, "reading {0}.{1}[{2}]",
            new Object[]{getFqn(), SyslogData.PROPERTY_PORT});
      }
      int port = cfgReader.readInteger();
      if (!isPropertyChangedByUi(SyslogData.PROPERTY_PORT)) {
        setPort(port);
      }
      if (isLoggable) {
        LOG.log(logLevel, "reading {0}.{1}[{2}]",
            new Object[]{getFqn(), SyslogData.PROPERTY_FACILITY});
      }
      if (isLoggable) {
        LOG.log(logLevel, "reading {0}.{1}[{2}]",
            new Object[]{getFqn(), SyslogData.PROPERTY_SYSLEVEL});
      }
      int sysLevel = cfgReader.readInteger();
      if (!isPropertyChangedByUi(SyslogData.PROPERTY_SYSLEVEL)) {
        setSysLevel(sysLevel);
      }
    }
  }
}

