package com.mc.tool.caesar.api.datamodel.extargs;

import com.mc.tool.caesar.api.Bundle;
import lombok.Getter;

/**
 * DP模式.
 */
@Getter
public enum ExtDpMode implements ExtArgObject {
  SINGLE_DP_MODE(0, Bundle.getMessage("ext.dp.mode.single")),
  DOUBLE_DP_MODE(1, Bundle.getMessage("ext.dp.mode.double"));
  private final int value;
  private final String name;

  ExtDpMode(int value, String name) {
    this.value = value;
    this.name = name;
  }

  @Override
  public byte[] toBytes() {
    return new byte[] {(byte) getValue()};
  }

  @Override
  public String toString() {
    return name;
  }
}
