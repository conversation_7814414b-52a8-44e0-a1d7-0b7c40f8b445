package com.mc.tool.caesar.api.version.v2x;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.Version2x;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.version.ApiDataConverter;

/**
 * .
 */
public class UserDataConverter2x implements ApiDataConverter<UserData> {

  private final int dataSize;

  public UserDataConverter2x(int dataSize) {
    this.dataSize = dataSize;
  }

  @Override
  public void readData(UserData data, CfgReader cfgReader) throws ConfigException {
    final int startAvailable = cfgReader.available();
    String name = cfgReader.readString(CaesarConstants.NAME_LEN);
    if (!data.isPropertyChangedByUi(UserData.PROPERTY_NAME)) {
      data.setName(name);
    }
    String pwd = cfgReader.readString(16);
    if (!data.isPropertyChangedByUi(UserData.PROPERTY_PASSWORD)) {
      data.setPassword(pwd);
    }
    int status = cfgReader.readInteger();
    if (!data.isPropertyChangedByUi(UserData.PROPERTY_STATUS)) {
      data.setStatus(status);
    }
    int rights = cfgReader.read2ByteValue();
    if (!data.isPropertyChangedByUi(UserData.PROPERTY_RIGHTS)) {
      data.setRights(rights);
    }

    int controlBits = cfgReader.read2ByteValue();
    data.setControlBits(controlBits);

    int conIndex = cfgReader.read2ByteValue();
    if (!data.isPropertyChangedByUi(UserData.PROPERTY_CON_INDEX)) {
      data.setConIndex(conIndex);
    }

    data.setInitMode(true);
    data.getVideoAccessBitSet().clear();
    data.getNoAccessBitSet().clear();
    data.getUserGroupBitSet().clear();

    short[] lckControl = data.createArrayShort(Version2x.MAX_CPU_NUMBER / 16);
    for (int idx = 0; idx < lckControl.length; idx++) {
      lckControl[idx] = cfgReader.readShort();
    }
    data.getVideoAccessBitSet().setFromShorts(lckControl);

    short[] lckVideo = data.createArrayShort(Version2x.MAX_CPU_NUMBER / 16);
    for (int idx = 0; idx < lckVideo.length; idx++) {
      lckVideo[idx] = cfgReader.readShort();
    }
    if (!data.isPropertyChangedByUi(UserData.PROPERTY_NO_ACCESS)) {
      data.getNoAccessBitSet().setFromShorts(lckVideo);
    }

    data.setInitMode(false);
    for (int idx = 0; idx < data.getFavoriteArray().length; idx++) {
      int favorite = cfgReader.readInteger();
      if (!data.isPropertyChangedByUi("FavoriteObject.Favorite")) {
        data.setFavorite(idx, favorite);
      }
    }

    byte[] userGroup = new byte[UserData.USER_GROUP_BIT_COUNT / 8];
    for (int idx = 0; idx < userGroup.length; idx++) {
      userGroup[idx] = (byte) cfgReader.readByteValue();
    }
    if (!data.isPropertyChangedByUi(UserData.PROPERTY_USER_GROUP)) {
      data.getUserGroupBitSet().setFromBytes(userGroup);
    }

    data.setMouseSpeed(cfgReader.readByteValue());
    data.setVideoWallRights(cfgReader.read2ByteValue());

    int endAvailable = cfgReader.available();
    int readedSize = startAvailable - endAvailable;
    int reservedSize = dataSize - readedSize;
    cfgReader.readByteArray(reservedSize);
  }

  @Override
  public void writeData(UserData data, CfgWriter cfgWriter) throws ConfigException {
    final long startSize = cfgWriter.getSize();
    cfgWriter.writeString(data.getName(), CaesarConstants.NAME_LEN);
    cfgWriter.writeString(data.getPassword(), 16);
    cfgWriter.writeInteger(data.getStatus());
    cfgWriter.write2ByteSmallEndian(data.getRights());
    cfgWriter.write2ByteSmallEndian(data.getControlBits());
    cfgWriter.write2ByteSmallEndian(data.getConIndex());

    short[] lckControl = data.getVideoAccessBitSet().getAsShortArray(Version2x.MAX_CPU_NUMBER / 16);
    for (short i : lckControl) {
      cfgWriter.writeShort(i);
    }
    short[] lckVideo = data.getNoAccessBitSet().getAsShortArray(Version2x.MAX_CPU_NUMBER / 16);
    for (short i : lckVideo) {
      cfgWriter.writeShort(i);
    }

    for (int idx = 0; idx < data.getFavoriteArray().length; idx++) {
      cfgWriter.writeInteger(data.getFavorite(idx));
    }

    cfgWriter.writeByteArray(
        data.getUserGroupBitSet().getAsByteArray(UserData.USER_GROUP_BIT_COUNT / 8));

    cfgWriter.writeByte((byte) data.getMouseSpeed());
    cfgWriter.write2ByteSmallEndian(data.getVideoWallRights());

    long endSize = cfgWriter.getSize();
    int writedSize = (int) (endSize - startSize);
    int reservedSize = dataSize - writedSize;
    cfgWriter.writeByteArray(new byte[reservedSize]);
  }
}
