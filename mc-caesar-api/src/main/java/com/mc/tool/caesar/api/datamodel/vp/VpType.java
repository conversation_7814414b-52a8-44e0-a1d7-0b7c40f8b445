package com.mc.tool.caesar.api.datamodel.vp;

import lombok.Getter;

/**
 * .
 */
@Getter
public enum VpType {
  VP6(8, 8, 16, Vp6Builder.class),
  VP6_SUB(6, 1, 6, Vp6SubBuilder.class),
  VP7(1, 4, 4, Vp7Builder.class),
  VP7_SUB(1, 1, 4, Vp7SubBuilder.class);

  private final int inPortCount;
  private final int outPortCount;
  private final int windowCount;
  private VpBuilder builder;

  VpType(int inPortCount, int outPortCount, int windowCount,
      Class<? extends VpBuilder> clazz) {
    this.inPortCount = inPortCount;
    this.outPortCount = outPortCount;
    this.windowCount = windowCount;
    try {
      builder = clazz.newInstance();
    } catch (InstantiationException | IllegalAccessException ex) {
      builder = null;
    }
  }

  public VpConConfigData createConfigData() {
    return builder.createConfigData();
  }

}
