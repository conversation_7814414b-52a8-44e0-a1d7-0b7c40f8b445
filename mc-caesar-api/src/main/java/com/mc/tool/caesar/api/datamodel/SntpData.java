package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * SntpData.
 *
 * @brief 时间配置.
 */
public class SntpData extends AbstractData {

  private static final Logger LOG = Logger.getLogger(SntpData.class.getName());
  public static final String PROPERTY_BASE = "SystemConfigData.SntpData.";
  public static final String FIELD_STATUS = "Status";
  public static final String PROPERTY_STATUS = "SystemConfigData.SntpData.Status";
  public static final String FIELD_TIMEZONE = "TimeZone";
  public static final String PROPERTY_TIMEZONE = "SystemConfigData.SntpData.TimeZone";
  public static final String FIELD_ADDRESS = "Address";
  // Date and Time下的 SNTP Server
  public static final String PROPERTY_ADDRESS = "SystemConfigData.SntpData.Address";
  public static final String FIELD_PORT = "Port";
  public static final String PROPERTY_PORT = "SystemConfigData.SntpData.Port";
  public static final String FIELD_REALTIMECLOCK = "RealTimeClock";
  // Date and Time下的Date And Time
  public static final String PROPERTY_REALTIMECLOCK = "SystemConfigData.SntpData.RealTimeClock";
  @Expose
  private byte[] address = new byte[4]; // sntp服务器地址
  @Expose
  private int port; // sntp服务器端口
  @Expose
  private int status = 0; //
  @Expose
  private CaesarConstants.TimeZone timezone = CaesarConstants.TimeZone.UTC_0; // 时区
  @Expose
  private LocalDateTime realTimeClock; // 设备的当前时间

  /**
   * .
   */
  public SntpData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager,
      String fqn) {
    super(pcs, configDataManager, -1, fqn);

    initCommitRollback();
  }

  @Override
  public void initDefaults() {
    setStatus(0);
    setTimeZone(CaesarConstants.TimeZone.UTC_0);
  }

  public byte[] getAddress() {
    return Arrays.copyOf(this.address, this.address.length);
  }

  /**
   * SNTP Service的ip地址.
   */
  public void setAddress(byte[] address) {
    byte[] oldValue = this.address;
    this.address = Arrays.copyOf(address, address.length);
    firePropertyChange(SntpData.PROPERTY_ADDRESS, oldValue, this.address, new int[0]);
  }

  public int getPort() {
    return this.port;
  }

  /**
   * .
   */
  public void setPort(int port) {
    int oldValue = this.port;
    this.port = port;
    firePropertyChange(SntpData.PROPERTY_PORT, Integer.valueOf(oldValue),
        Integer.valueOf(this.port), new int[0]);
  }

  public int getStatus() {
    return this.status;
  }

  /**
   * .
   */
  public void setStatus(int status) {
    int oldValue = this.status;
    this.status = status;
    firePropertyChange(SntpData.PROPERTY_STATUS, Integer.valueOf(oldValue),
        Integer.valueOf(this.status), new int[0]);
  }

  public CaesarConstants.TimeZone getTimeZone() {
    return this.timezone;
  }

  /**
   * .
   */
  public void setTimeZone(CaesarConstants.TimeZone timezone) {
    CaesarConstants.TimeZone oldValue = this.timezone;
    this.timezone = timezone;
    firePropertyChange(SntpData.PROPERTY_TIMEZONE, oldValue, this.timezone, new int[0]);
  }

  public LocalDateTime getRealTimeClock() {
    return this.realTimeClock;
  }

  /**
   * .
   */
  public void setRealTimeClock(LocalDateTime realTimeClock) {
    LocalDateTime oldValue = this.realTimeClock;
    this.realTimeClock = realTimeClock;
    firePropertyChange(SntpData.PROPERTY_REALTIMECLOCK, oldValue, this.realTimeClock, new int[0]);
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (SntpData.PROPERTY_ADDRESS.equals(propertyName)) {
      setAddress(byte[].class.cast(value));
    } else if (SntpData.PROPERTY_PORT.equals(propertyName)) {
      setPort(Integer.class.cast(value).intValue());
    } else if (SntpData.PROPERTY_TIMEZONE.equals(propertyName)) {
      setTimeZone(CaesarConstants.TimeZone.valueOf(value.toString()));
    } else if (SntpData.PROPERTY_STATUS.equals(propertyName)) {
      setStatus(Integer.class.cast(value).intValue());
    } else if (SntpData.PROPERTY_REALTIMECLOCK.equals(propertyName)) {
      setRealTimeClock(LocalDateTime.class.cast(value));
    }
  }

  /**
   * .
   */
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    Level logLevel = Level.FINER;
    boolean isLoggable = LOG.isLoggable(logLevel);
    if (isLoggable) {
      LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), "Status"});
    }
    cfgWriter.writeInteger(getStatus());
    for (int idx = this.address.length; idx > 0; idx--) {
      if (isLoggable) {
        LOG.log(logLevel, "writing {0}.{1}[{2}]",
            new Object[]{getFqn(), "Address", Integer.valueOf(idx - 1)});
      }
      cfgWriter.write4Byte(this.address[idx - 1]);
    }
    if (getConfigDataManager().getConfigMetaData().getUtilVersion().hasSntpPortOption()) {
      if (isLoggable) {
        LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), SntpData.PROPERTY_PORT});
      }
      cfgWriter.writeInteger(this.port);
    }
    if (isLoggable) {
      LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), "TimeZone"});
    }
    cfgWriter.writeInteger(getTimeZone().getId());
  }

  /**
   * .
   */
  public void readData(CfgReader cfgReader) throws ConfigException {
    Level logLevel = Level.FINER;
    boolean isLoggable = LOG.isLoggable(logLevel);
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), "Status"});
    }
    int status = cfgReader.readInteger();
    if (!isPropertyChangedByUi(SntpData.PROPERTY_STATUS)) {
      setStatus(status);
    }
    byte[] newAddress = new byte[this.address.length];
    for (int idx = newAddress.length; idx > 0; idx--) {
      if (isLoggable) {
        LOG.log(logLevel, "reading {0}.{1}[{2}]",
            new Object[]{getFqn(), "Address", Integer.valueOf(idx - 1)});
      }
      newAddress[idx - 1] = cfgReader.read4ByteValue();
    }
    if (!isPropertyChangedByUi(SntpData.PROPERTY_ADDRESS)) {
      setAddress(newAddress);
    }
    if (getConfigDataManager().getConfigMetaData().getUtilVersion().hasSntpPortOption()) {
      if (isLoggable) {
        LOG.log(logLevel, "reading {0}.{1}[{2}]", new Object[]{getFqn(), SntpData.PROPERTY_PORT});
      }
      int port = cfgReader.readInteger();
      if (!isPropertyChangedByUi(SntpData.PROPERTY_PORT)) {
        setPort(port);
      }
    }
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), "TimeZone"});
    }
    int timezone = cfgReader.readInteger();
    if (!isPropertyChangedByUi(SntpData.PROPERTY_TIMEZONE)) {
      setTimeZone(CaesarConstants.TimeZone.valueOf(timezone));
    }
  }
}

