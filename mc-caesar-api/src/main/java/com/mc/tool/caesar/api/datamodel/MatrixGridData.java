package com.mc.tool.caesar.api.datamodel;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;

/**
 * .
 */
public class MatrixGridData extends AbstractData {

  public static final String PROPERTY_BASE = "SystemConfigData.MatrixGridData.";
  public static final String PROPERTY_FORCE_BITS_GRID =
      "SystemConfigData.MatrixGridData.ForceBits.Grid";
  private SystemConfigData systemConfigData;

  /**
   * .
   */
  public MatrixGridData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager,
      String fqn, SystemConfigData systemConfigData) {
    super(pcs, configDataManager, -1, fqn);
    this.systemConfigData = systemConfigData;
    initCommitRollback();
  }

  @Override
  public void initDefaults() {
    setMatrixGridEnabled(false);
  }

  public boolean isMatrixGridEnabled() {
    return Utilities.areBitsSet(this.systemConfigData.getForceBits(),
        CaesarConstants.System.Forcebits.GRID);
  }

  /**
   * .
   */
  public void setMatrixGridEnabled(boolean enabled) {
    Threshold oldThreshold = this.systemConfigData.getThreshold();
    this.systemConfigData.setThreshold(getThreshold());
    this.systemConfigData.setForceBits(Utilities.setBits(this.systemConfigData.getForceBits(),
        enabled, new int[]{CaesarConstants.System.Forcebits.GRID}));
    this.systemConfigData.setThreshold(oldThreshold);
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (MatrixGridData.PROPERTY_FORCE_BITS_GRID.equals(propertyName)) {
      setMatrixGridEnabled(Boolean.class.cast(value).booleanValue());
    }
  }
}

