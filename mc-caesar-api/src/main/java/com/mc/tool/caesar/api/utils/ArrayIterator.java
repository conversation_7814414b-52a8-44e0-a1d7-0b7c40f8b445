package com.mc.tool.caesar.api.utils;

import java.util.Iterator;
import java.util.NoSuchElementException;

/**
 * 数组迭代器.
 */
public class ArrayIterator<T> implements Iterator<T> {

  private final T[] array;
  private int index = 0;

  public ArrayIterator(T... array) {
    this.array = array;
  }

  @Override
  public boolean hasNext() {
    return this.index < this.array.length;
  }

  @Override
  public T next() {
    if (this.index >= this.array.length) {
      throw new NoSuchElementException("No more indizes available");
    }
    return this.array[this.index++];
  }

  @Override
  public void remove() {
    throw new UnsupportedOperationException("Not supported.");
  }
}

