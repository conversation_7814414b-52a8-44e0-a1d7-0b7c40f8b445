package com.mc.tool.caesar.api.version.v2x;

import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData.MacroType;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.version.ApiDataConverter;

/**
 * .
 */
public class FunctionKeyDataConverter2x implements ApiDataConverter<FunctionKeyData> {

  private static final int MACRO_TYPE_CON = 0x08;
  private static final int MACRO_TYPE_USER = 0x04;
  private final int dataSize;

  public FunctionKeyDataConverter2x(int dataSize) {
    this.dataSize = dataSize;
  }

  @Override
  public void readData(FunctionKeyData data, CfgReader cfgReader) throws ConfigException {
    final int startAvailable = cfgReader.available();
    int status = cfgReader.readByteValue();
    data.setStatus(status);

    int hostSubscript = cfgReader.read2ByteValue();
    data.setHostSubscript(hostSubscript);

    int lineHotkey = cfgReader.read2ByteValue();
    data.setHotkey(lineHotkey & 0xfff);
    data.setKeyline(lineHotkey >> 12);

    int handleMode = cfgReader.readByteValue();
    data.setMacroCmd(handleMode & 0x0f);
    int macroType = handleMode >> 4;
    if (macroType == MACRO_TYPE_CON) {
      data.setMacroType(MacroType.CON);
    } else if (macroType == MACRO_TYPE_USER) {
      data.setMacroType(MacroType.USER);
    } else {
      data.setMacroType(MacroType.ERROR);
    }

    // 2个int参数
    data.setIntParam(1, cfgReader.read2ByteValue());
    data.setIntParam(0, cfgReader.read2ByteValue());

    int endAvailable = cfgReader.available();
    int readedSize = startAvailable - endAvailable;
    int reservedSize = dataSize - readedSize;
    cfgReader.readByteArray(reservedSize);
  }

  @Override
  public void writeData(FunctionKeyData data, CfgWriter cfgWriter) throws ConfigException {
    final long startSize = cfgWriter.getSize();
    cfgWriter.writeByte((byte) data.getStatus());
    cfgWriter.write2ByteSmallEndian(data.getHostSubscript());

    int lineHotkey = data.getKeyline() << 12;
    lineHotkey += data.getHotkey() & 0xfff;
    cfgWriter.write2ByteSmallEndian(lineHotkey);

    int handleMode = 0;
    switch (data.getMacroType()) {
      case CON:
        handleMode = MACRO_TYPE_CON;
        break;
      case USER:
        handleMode = MACRO_TYPE_USER;
        break;
      default:
        break;
    }
    handleMode <<= 4;
    handleMode += data.getMacroCmd() & 0xf;
    cfgWriter.writeByte((byte) handleMode);

    cfgWriter.write2ByteSmallEndian(data.getIntParam(1));
    cfgWriter.write2ByteSmallEndian(data.getIntParam(0));

    long endSize = cfgWriter.getSize();
    cfgWriter.writeByteArray(new byte[(int) (dataSize - (endSize - startSize))]);
  }
}
