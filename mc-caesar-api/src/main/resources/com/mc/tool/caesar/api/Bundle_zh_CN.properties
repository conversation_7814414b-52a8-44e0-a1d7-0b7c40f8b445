Arrangement.M1x4=1 x 4
Arrangement.M2x2=2 x 2
FunctionKey.command.connect=完全访问连接 (X=管控端, Y=接入端)
FunctionKey.command.connectvideo=视频连接 (X=管控端, Y=接入端)
FunctionKey.command.disconnect=断开连接 (X=管控端)
FunctionKey.command.get=抓取(完全访问) (X=管控端)
FunctionKey.command.getvideo=抓取视频 (X=管控端)
FunctionKey.command.login=用户登录 (X=管控端, Y=用户)
FunctionKey.command.private=私有连接 (X=管控端, Y=接入端)
FunctionKey.command.push=推送(完全访问) (X=管控端)
FunctionKey.command.pushvideo=推送(视频) (X=管控端)
#FunctionKey.command.private=Connect Private (P1=CON, P2=
#FunctionKey.command.push=Push (P1=
#FunctionKey.command.pushvideo=Push Video (P1=
Keyboard.DE_CH_150G=German (CH, 150G)
Keyboard.DE_DE_129=German (DE, 129)
Keyboard.EN_GB_166=English (GB, 166)
Keyboard.EN_GB_168=English (GB, 168)
Keyboard.EN_US_103P=English (US, 103P)
Keyboard.FR_BE_120=French (BE, 120)
Keyboard.FR_CH_150F=French (CH, 150F)
Keyboard.FR_FR_189=French (FR, 189)
Keyboard.RU_1251=Russian (RU, 1251)
MultiviewLayout.fullScreen=单窗口
MultiviewLayout.twoGrid=双窗口
MultiviewLayout.fourGrid=四窗口
MultiviewLayout.sideBar=一大三小
OsdPosition.bottom.left=左下方
OsdPosition.bottom.right=右下方
OsdPosition.centered=中间
OsdPosition.custom=自定义
OsdPosition.top.left=左上方
OsdPosition.top.right=右上方
Owner.Screen1=屏幕1
Owner.Screen2=屏幕2
Owner.Screen3=屏幕3
Owner.Screen4=屏幕4
Owner.Shared=共享
SuspendPosition.topcenter=上中间
SuspendPosition.topleft=左上角
SuspendPosition.topright=右上角
TeraConnectionModel.file.conflict.cpu.message=您尝试一个主机配置上传到SNMP板卡，这是不允许的
TeraConnectionModel.file.conflict.snmp.message=您尝试一个SNMP配置上传到主机，这是不允许的
TeraConnectionModel.file.conflict.title=文件版本冲突
TeraVersion.MATX008C=MATX008C
TeraVersion.MATX016=MATX016
TeraVersion.MATX048=MATX048
TeraVersion.MATX048C=MATX048C
TeraVersion.MATX080=MATX080
TeraVersion.MATX080C=MATX080C
TeraVersion.MATX160=MATX160
TeraVersion.MATX21R=MATX21R
TeraVersion.MATX288=MATX288
TeraVersion.MATX576M=MATX576M
TeraVersion.MATX576S=MATX576S
TeraVersion.MATX6BP=MATX6BP
TeraVersion.TERA048=TERA048
TeraVersion.TERA080=TERA080
TeraVersion.TERA160=TERA160
TeraVersion.TERA288=TERA288
TeraVersion.TERASW=TERASW
TimeZone.UTC_0=(GMT) Coordinated Universal Time, Casablanca, Dublin, Lisbon, London
TimeZone.UTC_MINUS_1=(GMT -01:00) Azores, Cape Verde Is.
TimeZone.UTC_MINUS_10=(GMT -10:00) Hawaii
TimeZone.UTC_MINUS_11=(GMT -11:00) Coordinated Universal Time-11
TimeZone.UTC_MINUS_12=(GMT -12:00) International Date Line West
TimeZone.UTC_MINUS_2=(GMT -02:00) Coordinated Universal Time-02
TimeZone.UTC_MINUS_3=(GMT -03:00) Brasilia, Buenos Aires, Cayenne, Greenland
TimeZone.UTC_MINUS_4=(GMT -04:00) Atlantic Time (Canada)
TimeZone.UTC_MINUS_5=(GMT -05:00) Eastern Time (US & Canada)
TimeZone.UTC_MINUS_6=(GMT -06:00) Central Time (US & Canada)
TimeZone.UTC_MINUS_7=(GMT -07:00) Mountain Time (US & Canada)
TimeZone.UTC_MINUS_8=(GMT -08:00) Pacific Time (US & Canada)
TimeZone.UTC_MINUS_9=(GMT -09:00) Alaska
TimeZone.UTC_PLUS_1=(GMT +01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna
TimeZone.UTC_PLUS_10=(GMT +10:00) Brisbane, Canberra, Guam, Melbourne, Sydney, Vladivostok
TimeZone.UTC_PLUS_11=(GMT +11:00) Solomon Is., New Caledonia
TimeZone.UTC_PLUS_12=(GMT +12:00) Auckland, Fiji, Marshall Is. 12, Wellington
TimeZone.UTC_PLUS_2=(GMT +02:00) Athens, Bucharest, Cairo, Helsinki, Istanbul, Jerusalem
TimeZone.UTC_PLUS_3=(GMT +03:00) Baghdad, Kuwait, Moscow, St. Petersburg, Volgograd
TimeZone.UTC_PLUS_4=(GMT +04:00) Abu Dhabi, Baku, Muscat
TimeZone.UTC_PLUS_5=(GMT +05:00) Islamabad, Karachi
TimeZone.UTC_PLUS_6=(GMT +06:00) Astana, Dhaka, Novosibirsk
TimeZone.UTC_PLUS_7=(GMT +07:00) Bangkok, Hanoi, Jakarta
TimeZone.UTC_PLUS_8=(GMT +08:00) Beijing, Hong Kong, Irkutsk, Kuala Lumpur, Perth, Singapore
TimeZone.UTC_PLUS_9=(GMT +09:00) Osaka, Sapporo, Seoul, Tokyo
VideoMode.1024x768=1024 x 768
VideoMode.1280x1024=1280 x 1024
VideoMode.1280x800=1280 x 800
VideoMode.1600x1200=1600 x 1200
VideoMode.1680x1050=1680 x 1050
VideoMode.1920x1080=1920 x 1080
VideoMode.1920x1200=1920 x 1200
VideoMode.800x600=800 x 600
VideoMode.var=可改变的
FunctionKey.command.activeVideoWallScenario=切换预案(X=预案)
FunctionKey.command.activeVideoWallScenarioWindow=切换预案窗口(X=预案窗口，Y=接入端)
FunctionKey.command.switchMultiViewLayout=切换多画面窗口布局(X=布局)
FunctionKey.command.switchMultiViewSignal=切换多画面通道信号(X=通道，Y=接入端)
multiview.channel=通道
FunctionKey.command.switchMultiViewPush=四画面推送(X=管控端)
FunctionKey.command.switchMultiViewGet=四画面抓取(X=管控端)
multiview.output_mode=接口模式
ext.dp.mode.single=单DP模式
ext.dp.mode.double=双DP模式
