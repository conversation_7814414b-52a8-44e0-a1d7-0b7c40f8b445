package com.mc.tool.caesar.api.datamodel;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import com.mc.tool.caesar.api.CaesarConfigDataModel;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.Version;
import com.mc.tool.caesar.api.Version2x;
import com.mc.tool.caesar.api.Version3x;
import com.mc.tool.caesar.api.Version4x;
import com.mc.tool.caesar.api.datamodel.ConfigData.IoMode;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.interfaces.ChangedEvent;
import com.mc.tool.caesar.api.interfaces.ChangedListener;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

/**
 * AbstractDataTest.
 */
public class AbstractDataTest {

  /**
   * TestData.
   */
  public class TestData extends AbstractData {

    private int id = 0;
    private int[] datas = new int[10];
    private String name;
    public static final String PROPERTY_ID = "TestData.ID";
    public static final String PROPERTY_NAME = "TestData.NAME";
    public static final String PROPERTY_DATAS = "TestData.Datas";

    public TestData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager, int oid,
        String fqn) {
      super(pcs, configDataManager, oid, fqn);
    }

    int getId() {
      return id;
    }

    void setId(int inId) {
      int oldVal = id;
      id = inId;
      firePropertyChange(PROPERTY_ID, oldVal, inId);
    }

    String getName() {
      return name;
    }

    void setName(String inName) {
      String oldVal = name;
      name = inName;
      firePropertyChange(PROPERTY_NAME, oldVal, name);
    }

    int[] getDatas() {
      return datas;
    }

    void setData(int idx, int data) {
      if (idx >= 10 || idx < 0) {
        return;
      }
      int oldVal = datas[idx];
      datas[idx] = data;
      firePropertyChange(PROPERTY_DATAS, oldVal, data, idx);
    }

    void setData(int[] idx, int[] data) {
      if (idx.length != data.length) {
        return;
      }
      int len = idx.length;
      int[] oldVal = new int[len];
      for (int i = 0; i < len; i++) {
        oldVal[i] = datas[idx[i]];
        datas[idx[i]] = data[i];

      }
      firePropertyChange(PROPERTY_DATAS, oldVal, data, idx);
    }

    @Override
    protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
      if (propertyName.equals(PROPERTY_ID)) {
        setId((Integer) value);
      } else if (propertyName.equals(PROPERTY_NAME)) {
        setName((String) value);
      } else if (propertyName.equals(PROPERTY_DATAS)) {
        int[] vals = (int[]) value;
        if (vals.length != indizes.length) {
          return;
        }
        int len = vals.length;
        for (int i = 0; i < len; i++) {
          datas[indizes[i]] = vals[i];
        }
      }
    }
  }

  public static TestData data;
  public static String testBean = "";
  public static CustomPropertyChangeSupport pcs = new CustomPropertyChangeSupport(testBean);

  @BeforeClass
  public static void setUpBeforeClass() throws Exception {
    System.setProperty("no-fx-mode", "1");
    System.setProperty("generator-mode", "true");
  }

  @AfterClass
  public static void tearDownAfterClass() throws Exception {
  }

  @Before
  public void setUp() throws Exception {
    data = new TestData(pcs, null, 0, "TestData");
  }

  @After
  public void tearDown() throws Exception {
  }

  @Test
  public void testHashCode() {

  }

  @Test
  public void testGetPropertyChangeSupport() {
    assertEquals(pcs, data.getPropertyChangeSupport());
  }

  @Test
  public void testGetConfigDataManager() {
    assertNull(data.getConfigDataManager());
  }

  @Test
  public void testSetThreshold() {
    Threshold threshold = Threshold.UI_ALL;
    data.setThreshold(threshold);
    assertTrue(threshold.equals(data.getThreshold()));
  }

  @Test
  public void testEqualsObject() {
    TestData tmp = new TestData(null, null, 0, "TestData");
    assertTrue(data.equals(tmp));
  }


  @Test
  public void testCompareTo() {
    TestData tmp = new TestData(null, null, 10, "temp");
    assertEquals(-10, data.compareTo(tmp));
    assertEquals(-1, data.compareTo(null));
  }

  @Test
  public void testToString() {
    System.out.println(data.toString());
  }

  @Test
  public void testCommit() {
    data.setThreshold(Threshold.SIX);
    data.setId(10);
    data.commit();
    assertFalse(data.isChanged());
    assertEquals(10, data.getId());

    data.setName("hello");
    data.commit(Threshold.EIGHT);
    assertFalse(data.isChanged(Threshold.SEVEN));
    assertTrue(data.isChanged(Threshold.SIX));
    assertEquals("hello", data.getName());

    data.commit(Threshold.SIX);
    assertFalse(data.isChanged(Threshold.SIX));
    assertTrue(data.isChanged(Threshold.FIVE));
    assertEquals("hello", data.getName());
  }

  @Test
  public void testCommitPropertyStringIntArray() {
    data.setThreshold(Threshold.SIX);
    data.setId(10);
    data.setName("hello");
    data.commitProperty(TestData.PROPERTY_ID);
    assertFalse(data.isPropertyChanged(TestData.PROPERTY_ID));
    assertTrue(data.isPropertyChanged(TestData.PROPERTY_NAME));
  }

  @Test
  public void testRollback() {
    data.setThreshold(Threshold.SIX);
    data.setId(10);

    data.rollback(Threshold.SEVEN);
    assertEquals(10, data.getId());
    data.rollback(Threshold.SIX);
    assertEquals(0, data.getId());
    assertFalse(data.isChanged(Threshold.FIVE));
    assertFalse(data.isChanged(Threshold.ALL));

    data.setId(10);
    data.rollback(Threshold.FIVE);
    assertFalse(data.isChanged(Threshold.FIVE));
    assertTrue(data.isChanged(Threshold.FOUR));
    assertEquals(10, data.getId());
  }

  @Test
  public void testRollbackPropertyStringIntArray() {
    data.setThreshold(Threshold.SIX);
    data.setId(10);
    data.setName("hello");

    data.rollbackProperty(Threshold.SIX, TestData.PROPERTY_ID);
    assertEquals(0, data.getId());
    assertEquals("hello", data.getName());

  }

  @Test
  public void testIsChanged() {
    data.setThreshold(Threshold.SIX);
    data.setId(10);
    assertTrue(data.isChanged());
    assertTrue(data.isChanged(Threshold.SIX));
    assertTrue(data.isChanged(Threshold.ALL));
    assertFalse(data.isChanged(Threshold.SEVEN));
  }


  @Test
  public void testPropertyEquals() {
    data.setThreshold(Threshold.SIX);
    data.setId(10);
    assertTrue(data.propertyEquals(Threshold.SIX, TestData.PROPERTY_ID));
    assertFalse(data.propertyEquals(Threshold.SEVEN, TestData.PROPERTY_ID));
    assertFalse(data.propertyEquals(Threshold.FIVE, TestData.PROPERTY_ID));
    assertFalse(data.propertyEquals(Threshold.ALL, TestData.PROPERTY_ID));
  }


  class TestChangedListener implements ChangedListener {

    private boolean changed = false;

    @Override
    public void handleChanged(ChangedEvent paramChangedEvent) {
      // TODO Auto-generated method stub
      changed = true;
    }

    boolean isChanged() {
      return changed;
    }

    void reset() {
      changed = false;
    }

  }

  @Test
  public void testAddChangedListener() {
    TestChangedListener listener = new TestChangedListener();
    data.addChangedListener(listener);
    data.setId(10);
    assertTrue(listener.isChanged());

    listener.reset();
    data.removeChangedListener(listener);
    data.setId(20);
    assertFalse(listener.isChanged());


  }

  @Test
  public void testDataOffset() {
    Version[] versions = new Version[]{Version.getVersion(Version2x.VERSION_VALUE),
        Version.getVersion(Version3x.VERSION_VALUE), Version.getVersion(Version4x.VERSION_VALUE)};

    for (Version version : versions) {
      System.out.println("Testing version : " + version.getClass().getName());
      System.setProperty("latest-dkm-version", version.getVersionValue() + "");
      CaesarConfigDataModel dataModel = new CaesarConfigDataModel();
      dataModel.initDefaults();
      ConfigData configData = dataModel.getConfigData();
      int[] rands = new int[10];
      for (int i = 0; i < rands.length; i++) {
        rands[i] = (int) (Math.random() * 0xff);
      }

      configData.getSystemConfigData().setStatus(rands[0]);
      configData.getCpuData(0).setStatus(rands[1]);
      configData.getConsoleData(0).setStatus(rands[2]);
      configData.getExtenderData(0).setStatus(rands[3]);
      configData.getUserData(0).setStatus(rands[4]);
      configData.getUserGroupData(0).setStatus(rands[5]);
      configData.getMultiScreenData(0).setStatus(rands[6]);
      rands[7] |= 0x80;
      configData.getFunctionKeyData(0).setStatus(rands[7]);
      ConfigDataManager configDataManager = dataModel.getConfigDataManager();
      for (int i = 0; i < configDataManager.getConfigMetaData().getFunctionKeyCount(); i++) {
        configData.getFunctionKeyData(i).setStatusActive(true);
      }
      configData.getMatrixData(0).setStatus(rands[8]);
      configData.getPortData(0).setStatus(rands[9]);

      ByteArrayOutputStream baos = new ByteArrayOutputStream();
      CfgWriter cfgWriter = new CfgWriter(baos);
      try {
        configData.writeData(cfgWriter, IoMode.All);
      } catch (ConfigException e) {
        fail("Expect no exception");
      }

      byte[] bytes = baos.toByteArray();
      assertEquals(version.getConfigDataSize(), bytes.length);
      int oid = 0;
      String fqn = "";
      CustomPropertyChangeSupport pcs = new CustomPropertyChangeSupport(new Object());
      AbstractData[] datas = new AbstractData[]{new SystemConfigData(pcs, configDataManager, fqn),
          new CpuData(pcs, configDataManager, oid, fqn),
          new ConsoleData(pcs, configDataManager, oid, fqn),
          new ExtenderData(pcs, configDataManager, oid, fqn),
          new UserData(pcs, configDataManager, oid, fqn),
          new UserGroupData(pcs, configDataManager, oid, fqn),
          new MultiScreenData(pcs, configDataManager, oid, fqn),
          new FunctionKeyData(pcs, configDataManager, oid, fqn),
          new MatrixData(pcs, configDataManager, oid, fqn),
          new PortData(pcs, configDataManager, oid, fqn)};

      Class<?>[] clazzes = new Class[]{
          SystemConfigData.class, CpuData.class, ConsoleData.class, ExtenderData.class,
          UserData.class,
          UserGroupData.class, MultiScreenData.class, FunctionKeyData.class, MatrixData.class,
          PortData.class
      };

      for (int i = 0; i < datas.length; i++) {
        byte[] rawData = Arrays.copyOfRange(bytes, version.getClassOffset(clazzes[i]),
            version.getClassOffset(clazzes[i]) + version.getClassSize(clazzes[i]));
        AbstractData data = datas[i];
        System.out.println(data.getClass().getName());
        ByteArrayInputStream bais = new ByteArrayInputStream(rawData);
        try {
          ((CaesarCommunicatable) data).readData(new CfgReader(bais));
        } catch (ConfigException e) {
          fail("expect no exception");
        }

        try {
          if (data.getClass() != FunctionKeyData.class) {
            Method method = data.getClass().getMethod("getStatus");
            int status = (Integer) method.invoke(data);
            assertEquals(rands[i], status);
          } else {
            int value = ((FunctionKeyData) data).getStatus();
            assertEquals(rands[i], value);
          }
        } catch (NoSuchMethodException | SecurityException | IllegalAccessException
            | IllegalArgumentException | InvocationTargetException e) {
          fail("expect no exception");
        }
      }
    }
  }

  @Test
  public void testDataSize() {
    Version[] versions = new Version[]{Version.getVersion(Version2x.VERSION_VALUE),
        Version.getVersion(Version3x.VERSION_VALUE), Version.getVersion(Version4x.VERSION_VALUE)};
    for (Version version : versions) {
      System.out.println("Testing version : " + version.getClass().getName());
      Map<AbstractData, Integer> dataSizes = new HashMap<>();
      CustomPropertyChangeSupport pcs = new CustomPropertyChangeSupport(new Object());
      String fqn = "";
      ConfigDataManager configDataManager = new ConfigDataManager();
      ConfigData configData = new ConfigData(pcs, configDataManager);

      ConfigMetaData metaData = new ConfigMetaData(pcs, configDataManager, fqn);
      configDataManager.setConfigMetaData(metaData);
      dataSizes.put(metaData, version.getClassSize(ConfigMetaData.class));
      SystemConfigData systemConfigData = new SystemConfigData(pcs, configDataManager, fqn);
      dataSizes.put(systemConfigData, version.getClassSize(SystemConfigData.class));
      int oid = 0;
      CpuData cpuData = new CpuData(pcs, configDataManager, oid, fqn);
      dataSizes.put(cpuData, version.getClassSize(CpuData.class));
      ConsoleData consoleData = new ConsoleData(pcs, configDataManager, oid, fqn);
      dataSizes.put(consoleData, version.getClassSize(ConsoleData.class));
      ExtenderData extenderData = new ExtenderData(pcs, configDataManager, oid, fqn);
      dataSizes.put(extenderData, version.getClassSize(ExtenderData.class));
      UserData userData = new UserData(pcs, configDataManager, oid, fqn);
      dataSizes.put(userData, version.getClassSize(UserData.class));
      UserGroupData userGroupData = new UserGroupData(pcs, configDataManager, oid, fqn);
      dataSizes.put(userGroupData, version.getClassSize(UserGroupData.class));
      MultiScreenData multiScreenData = new MultiScreenData(pcs, configDataManager, oid, fqn);
      dataSizes.put(multiScreenData, version.getClassSize(MultiScreenData.class));
      FunctionKeyData functionKeyData = new FunctionKeyData(pcs, configDataManager, oid, fqn);
      dataSizes.put(functionKeyData, version.getClassSize(FunctionKeyData.class));
      MatrixData matrixData = new MatrixData(pcs, configDataManager, oid, fqn);
      dataSizes.put(matrixData, version.getClassSize(MatrixData.class));
      PortData portData = new PortData(pcs, configDataManager, oid, fqn);
      dataSizes.put(portData, version.getClassSize(PortData.class));

      ModuleData moduleData = new ModuleData(pcs, configDataManager, oid);
      dataSizes.put(moduleData, version.getClassSize(ModuleData.class));

      for (Entry<AbstractData, Integer> entry : dataSizes.entrySet()) {
        metaData.initWithVersion(version);
        byte[] value = new byte[entry.getValue()];
        ByteArrayInputStream bais = new ByteArrayInputStream(value);
        CfgReader reader = new CfgReader(bais);
        try {
          ((CaesarCommunicatable) entry.getKey()).readData(reader);
        } catch (ConfigException e) {
          e.printStackTrace();
          fail("expect no exception");
        }

        metaData.initWithVersion(version);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        CfgWriter writer = new CfgWriter(baos);
        try {
          ((CaesarCommunicatable) entry.getKey()).writeData(writer);
        } catch (ConfigException e) {
          fail("expect no exception");
        }
        System.out.println(entry.getKey().getClass().getName());
        assertEquals(entry.getValue().intValue(), baos.toByteArray().length);
      }
    }
  }

}
