package com.mc.tool.caesar.api.utils;

import com.mc.tool.caesar.api.CaesarConstants.Extender.Type;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.utils.SampleModelCreater.SampleModelInfo;
import java.util.ArrayList;
import java.util.List;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

/**
 * .
 */
public class DataCheckerTest {

  @BeforeClass
  public static void beforeClass() {
    System.setProperty("no-fx-mode", "1");
  }

  @Test
  public void testPortError() {
    CaesarSwitchDataModel model = new CaesarSwitchDataModel();
    model.initDefaults();
    SampleModelInfo statusInfo = new SampleModelInfo();
    statusInfo.setTxCount(1);
    statusInfo.setRxCount(1);
    SampleModelCreater.createDataModel(model, statusInfo, 0);
    model.getConfigData().getExtenderData(0).setPort(0);
    // 检查端口外设不匹配
    List<PortData> errorPortList = new ArrayList<>();
    List<ExtenderData> errorExtenderList = new ArrayList<>();
    List<CpuData> errorCpuList = new ArrayList<>();
    List<ConsoleData> errorConsoleList = new ArrayList<>();
    int result = DataChecker
        .checkErrors(model, errorPortList, errorExtenderList, errorCpuList, errorConsoleList, true);
    Assert.assertEquals(1, result);
    Assert.assertEquals(1, errorPortList.size());
    Assert.assertEquals(model.getConfigData().getPortData(0), errorPortList.get(0));
    int port = model.getConfigData().getExtenderData(0).getPort();
    model.getConfigData().getExtenderData(0).setPort(port); // 还原
    // 检查端口外设的TX、RX不匹配
    model.getConfigData().getCpuData(0).setExtender(0, 0);
    model.getConfigData().getConsoleData(0).setExtender(0, 0);
    result = DataChecker
        .checkErrors(model, errorPortList, errorExtenderList, errorCpuList, errorConsoleList, true);
    Assert.assertEquals(2, result);
    Assert.assertEquals(2, errorPortList.size());
  }

  @Test
  public void testExtenderError() {
    CaesarSwitchDataModel model = new CaesarSwitchDataModel();
    model.initDefaults();
    SampleModelInfo statusInfo = new SampleModelInfo();
    statusInfo.setTxCount(1);
    statusInfo.setRxCount(1);
    SampleModelCreater.createDataModel(model, statusInfo, 0);
    ExtenderData extenderData = model.getConfigData().getExtenderData(2);
    extenderData.setStatusActive(true);
    int port = 10;
    extenderData.setPort(port);

    List<PortData> errorPortList = new ArrayList<>();
    List<ExtenderData> errorExtenderList = new ArrayList<>();
    List<CpuData> errorCpuList = new ArrayList<>();
    List<ConsoleData> errorConsoleList = new ArrayList<>();
    int result = DataChecker
        .checkErrors(model, errorPortList, errorExtenderList, errorCpuList, errorConsoleList, true);
    Assert.assertEquals(1, result);
    Assert.assertEquals(1, errorExtenderList.size());
    Assert.assertEquals(extenderData, errorExtenderList.get(0));

    //usb
    PortData portData = model.getConfigData().getPortData(port - 1);
    portData.setStatusActive(true);
    portData.setStatusAvailable(true);
    portData.setExtender(extenderData.getOid() + 1);
    portData.setType(Type.USB_CON);
    extenderData.setType(Type.USB_CON);

    ConsoleData consoleData = model.getConfigData().getConsoleData(0);
    extenderData.setConsoleData(consoleData);

    result = DataChecker
        .checkErrors(model, errorPortList, errorExtenderList, errorCpuList, errorConsoleList, true);
    Assert.assertEquals(1, result);
    Assert.assertEquals(1, errorPortList.size());
    Assert.assertEquals(portData, errorPortList.get(0));
    // 正确
    consoleData.setExtender(1, extenderData.getOid() + 1);
    result = DataChecker
        .checkErrors(model, errorPortList, errorExtenderList, errorCpuList, errorConsoleList, true);
    Assert.assertEquals(0, result);
  }

  @Test
  public void testTxError() {
    CaesarSwitchDataModel model = new CaesarSwitchDataModel();
    model.initDefaults();
    SampleModelInfo statusInfo = new SampleModelInfo();
    statusInfo.setTxCount(1);
    statusInfo.setRxCount(1);
    SampleModelCreater.createDataModel(model, statusInfo, 0);
    CpuData cpuData = model.getConfigData().getCpuData(1);
    cpuData.setStatusActive(true);
    cpuData.setExtender(0, 1);

    List<PortData> errorPortList = new ArrayList<>();
    List<ExtenderData> errorExtenderList = new ArrayList<>();
    List<CpuData> errorCpuList = new ArrayList<>();
    List<ConsoleData> errorConsoleList = new ArrayList<>();
    int result = DataChecker
        .checkErrors(model, errorPortList, errorExtenderList, errorCpuList, errorConsoleList, true);
    Assert.assertEquals(1, result);
    Assert.assertEquals(1, errorCpuList.size());
    Assert.assertEquals(cpuData, errorCpuList.get(0));
  }

  @Test
  public void testRxError() {
    CaesarSwitchDataModel model = new CaesarSwitchDataModel();
    model.initDefaults();
    SampleModelInfo statusInfo = new SampleModelInfo();
    statusInfo.setTxCount(1);
    statusInfo.setRxCount(1);
    SampleModelCreater.createDataModel(model, statusInfo, 0);
    ConsoleData consoleData = model.getConfigData().getConsoleData(1);
    consoleData.setStatusActive(true);
    consoleData.setExtender(0, 1);

    List<PortData> errorPortList = new ArrayList<>();
    List<ExtenderData> errorExtenderList = new ArrayList<>();
    List<CpuData> errorCpuList = new ArrayList<>();
    List<ConsoleData> errorConsoleList = new ArrayList<>();
    int result = DataChecker
        .checkErrors(model, errorPortList, errorExtenderList, errorCpuList, errorConsoleList, true);
    Assert.assertEquals(1, result);
    Assert.assertEquals(1, errorConsoleList.size());
    Assert.assertEquals(consoleData, errorConsoleList.get(0));
  }
}
