package com.mc.tool.caesar.api;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * .
 */
public class TestBase {

  public static CaesarSwitchDataModel model = null;
  public static String ip = "************";

  /**
   * .
   */
  public static void setupBeforeClass() {
    System.setProperty("no-fx-mode", "1");
    System.setProperty("default.charset", "utf-8");
    CaesarSwitchDataModelManager manager = CaesarSwitchDataModelManager.getInstance();
    assertNotNull(manager);
    try {
      model = new CaesarSwitchDataModel();
      model.setConnection(ip, true);
      // model.setLevel(10);
    } catch (ConfigException e) {
      e.printStackTrace();
      assertTrue(false);
    } catch (BusyException e) {
      e.printStackTrace();
      assertTrue(false);
    }
    assertNotNull(model);
    try {
      Thread.sleep(1000);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }

    try {
      model.reloadConfigData();
      model.getSwitchModuleData().reloadModules();
      Gson gson =
          new GsonBuilder().excludeFieldsWithoutExposeAnnotation().setPrettyPrinting().create();
      String content = gson.toJson(model.getConfigData());
      java.io.File file = new java.io.File("c://config.json");
      FileOutputStream fos = new FileOutputStream(file);
      fos.write(content.getBytes());
      fos.close();
    } catch (ConfigException | BusyException | IOException e) {
      e.printStackTrace();
    } catch (Exception e) {
      e.printStackTrace();
    }

  }

  /**
   * .
   */
  public static void tearDownAfterClass() {
    if (model != null) {
      model.closeConnection();
    }
  }
}
