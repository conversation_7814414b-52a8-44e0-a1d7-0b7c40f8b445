package com.mc.tool.caesar.api;

import static junit.framework.TestCase.assertEquals;
import static junit.framework.TestCase.assertFalse;
import static junit.framework.TestCase.assertTrue;

import com.google.common.primitives.Ints;
import java.security.SecureRandom;
import java.util.Arrays;
import org.junit.Test;

/**
 * .
 */
public class DkmProtocalCryptoTest {

  @Test public void generateKey() {
    SecureRandom random = new SecureRandom();
    byte[] key = new byte[16];
    byte[] iv = new byte[16];
    random.nextBytes(key);
    random.nextBytes(iv);
    StringBuilder builder = new StringBuilder();
    builder.append("byte[] key = new byte[] {");
    for (byte item : key) {
      builder.append(String.format("0x%x, ", item));
    }
    builder.append("};");
    System.out.println(builder.toString());

    builder = new StringBuilder();
    builder.append("byte[] iv = new byte[] {");
    for (byte item : iv) {
      builder.append(String.format("0x%x, ", item));
    }
    builder.append("};");
    System.out.println(builder.toString());
  }

  @Test public void encrypt_1_byte() {
    // 1个字节
    try {
      byte[] input = new byte[1];
      input[0] = 0x1B;
      byte[] output = DkmProtocalCrypto.decrypt(input);
      assertEquals(1, output.length);
      assertEquals(input[0], output[0]);
    } catch (Exception exception) {
      assertFalse(true);
    }
  }

  @Test public void encrypt_3_byte() {
    try {
      // 3个字节
      byte[] input = new byte[3];
      input[0] = 0x1B;
      input[1] = 0x10;
      input[2] = 0x20;
      byte[] output = DkmProtocalCrypto.decrypt(input);
      assertEquals(3, output.length);
      assertEquals(true, Arrays.equals(input, output));
    } catch (Exception exception) {
      assertFalse(true);
    }
  }

  @Test public void encrypt_less_than_16_bytes_data() {
    try {
      byte[] input = new byte[16];
      int length = 16;
      for (int i = 0; i < length; i++) {
        input[i] = (byte) ((1000 / (i + 1)) % 255);
      }
      byte[] output = DkmProtocalCrypto.encrypt(input);
      assertTrue(output.length > 7);
      byte[] lenBytes = Arrays.copyOfRange(output, 3, 7);
      byte[] lenBytesReversed = {lenBytes[3], lenBytes[2], lenBytes[1], lenBytes[0]};
      assertEquals(output.length, Ints.fromByteArray(lenBytesReversed));
      byte[] verity = DkmProtocalCrypto.decrypt(output);
      assertEquals(length, verity.length);
      assertEquals(true, Arrays.equals(input, verity));
    } catch (Exception exception) {
      exception.printStackTrace();
      assertFalse(true);
    }
  }

  @Test public void encrypt_more_than_16_bytes_data() {
    try {
      byte[] input = new byte[20];
      int length = 20;
      for (int i = 0; i < length; i++) {
        input[i] = (byte) ((1000 / (i + 1)) % 255);
      }
      byte[] output = DkmProtocalCrypto.encrypt(input);
      assertTrue(output.length > 7);
      byte[] lenBytes = Arrays.copyOfRange(output, 3, 7);
      byte[] lenBytesReversed = {lenBytes[3], lenBytes[2], lenBytes[1], lenBytes[0]};
      assertEquals(output.length, Ints.fromByteArray(lenBytesReversed));
      byte[] verity = DkmProtocalCrypto.decrypt(output);
      assertEquals(length, verity.length);
      assertEquals(true, Arrays.equals(input, verity));
    } catch (Exception exception) {
      exception.printStackTrace();
      assertFalse(true);
    }
  }
}
